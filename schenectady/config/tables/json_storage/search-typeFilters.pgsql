INSERT INTO config.json_storage (
  properties,
  created_by,
  created_date,
  last_modified_by,
  json_data,
  last_modified_date
)
VALUES (
  '{"search": "type_filters"}'::jsonb,
  'service-account-clerkxpress-seed',
  CURRENT_TIMESTAMP,
  'service-account-clerkxpress-seed',
  '[
     {
       "id": "all",
       "icon": "Search",
       "name": "All",
       "columns": [],
       "table_filters": []
     },
     {
       "id": "individual",
       "icon": "User",
       "name": "Individual",
       "table": "license.participant",
       "columns": [
         {
           "id": "first_name",
           "name": "First Name",
           "type": "text"
         },
         {
           "id": "last_name",
           "name": "Last Name",
           "type": "text"
         },
         {
           "id": "email",
           "name": "Email",
           "type": "text"
         },
         {
           "id": "phone",
           "name": "Phone",
           "type": "text"
         },
         {
           "id": "address",
           "name": "Address",
           "type": "text"
         }
       ],
       "table_filters": [
         "Individual"
       ]
     },
     {
       "id": "dog",
       "icon": "Dog",
       "name": "Dog",
       "table": "license.participant",
       "columns": [
         {
           "id": "tag_number",
           "name": "Tag Number",
           "type": "text"
         },
         {
           "id": "dog_name",
           "name": "Dog Name",
           "type": "text"
         },
         {
           "id": "breed",
           "name": "Breed",
           "type": "text"
         },
         {
           "id": "color",
           "name": "Color",
           "type": "text"
         },
         {
           "id": "vaccination_date",
           "name": "Vaccination Date",
           "type": "date"
         }
       ],
       "table_filters": [
         "Dog"
       ]
     },
     {
       "id": "license",
       "icon": "FileText",
       "name": "License",
       "table": "license.license",
       "columns": [
         {
           "id": "license_number",
           "name": "License Number",
           "type": "text"
         },
         {
           "id": "license_type",
           "name": "License Type",
           "type": "text"
         },
         {
           "id": "license_status",
           "name": "License Status",
           "type": "text"
         },
         {
           "id": "issue_date",
           "name": "Issue Date",
           "type": "date"
         },
         {
           "id": "expiry_date",
           "name": "Expiry Date",
           "type": "date"
         }
       ]
     }
   ]'::jsonb,
  CURRENT_TIMESTAMP
)
ON CONFLICT (properties)
DO UPDATE SET
  json_data = EXCLUDED.json_data,
  last_modified_by = EXCLUDED.last_modified_by,
  last_modified_date = CURRENT_TIMESTAMP;
