drop FUNCTION license.fn_determineDogLicenseDuration;
CREATE OR REPLACE FUNCTION license.fn_determineDogLicenseDuration(
    in i_license_entity_id uuid
)
RETURNS TABLE(
    duration INT,
    message TEXT,
    isRenewable BOOLEAN,
    reasonCodes TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_license record;
    v_license_status record;
    v_dog_properties jsonb;

    v_vaccine_expiration_date date;
    v_license_expiration_date date;
    v_is_vaccine_exempt boolean;
    v_max_renewal_years int := 3;
    v_current_date date := CURRENT_DATE;
    v_future_current_date date;
    v_max_vaccine_expiration_date date;
    v_whichever_is_less date;
    v_years_until_exp int := 0;
    v_years_current float := 0;

    v_duration int;
    v_message text;
    v_is_renewable boolean := true;
    v_reasons text[] := ARRAY[]::text[];
BEGIN
    -- Fetch license information
    SELECT l.*
    INTO v_license
    FROM license.view_license l
    WHERE l.license_type_code = 'dogLicense'
    AND l.license_uuid = i_license_entity_id;

    IF v_license IS NULL THEN
        RAISE EXCEPTION 'License with UUID % not found', i_license_entity_id;
    END IF;

    select ls.*
    into v_license_status
    from license.license_status ls
    where ls.license_status_id = v_license.license_status_id;

    -- Fetch dog properties
    SELECT pdog.properties
    INTO v_dog_properties
    FROM license.association aDog
    LEFT JOIN license.view_participant pdog
        ON pdog.participant_id = aDog.child_id
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
    AND pdog.group_name ILIKE 'Dog'
    order by pdog.properties->>'vaccineDueDate' ASC
    LIMIT 1;


    IF v_dog_properties IS NULL THEN
        RAISE EXCEPTION 'Dog not found for license with UUID %', i_license_entity_id;
    END IF;

    -- Fetch vaccine information
    v_vaccine_expiration_date := (v_dog_properties->>'vaccineDueDate')::date;
    v_is_vaccine_exempt := COALESCE(v_dog_properties->>'vaccineDatesExempt','false') = 'true';
    v_license_expiration_date := COALESCE(v_license.valid_to_date, now());

    -- Calculate future current date (3 years from the current month end)
    v_future_current_date := (license.addYears(license.end_of_month(v_current_date), v_max_renewal_years))::date;

    -- Check if vaccine exempt, calculate based on license expiration
    IF v_is_vaccine_exempt THEN
        v_years_until_exp := GREATEST(
            LEAST(
                v_max_renewal_years, 
                DATE_PART('year', AGE(v_future_current_date, v_license_expiration_date))
            ), 
            0
        );
        IF v_years_until_exp = 0 THEN
            v_duration := 0;
            v_message := 'Exempt from vaccine, but not eligible for renewal yet';
            v_is_renewable := false;
            v_reasons := array_append(v_reasons, 'alreadyRenewed');
        ELSE
            v_duration := v_years_until_exp;
            v_message := 'Exempt from vaccine, eligible for renewal';
            v_is_renewable := true;
            v_reasons := array_append(v_reasons, 'vaccineValid');
        END IF;

    -- If vaccine expiration exists, calculate based on it
    ELSIF v_vaccine_expiration_date IS NOT NULL THEN
        v_max_vaccine_expiration_date := (license.end_of_month(license.addMonths(v_vaccine_expiration_date, 11)))::date;

        -- Choose the lesser of max vaccine expiration date or future current date
        v_whichever_is_less := LEAST(v_max_vaccine_expiration_date, v_future_current_date);

        -- Calculate years until expiration
        v_years_until_exp := GREATEST(
            LEAST(v_max_renewal_years, 
                DATE_PART('year', AGE(v_whichever_is_less, v_license_expiration_date))
            ), 
            0
        );

        -- calculate years from today how many years until until license expiration
        v_years_current := ROUND(ABS(v_license_expiration_date::date - license.end_of_month(v_current_date)::date) / 365.25, 2);
        raise notice 'v_years_current: %', v_years_current;
        raise notice 'v_years_until_exp: %', v_years_until_exp;

        IF v_years_until_exp = 0 and v_years_current >= 1 THEN
            v_duration := 0;
            v_message := format('Already renewed for %s %s, update vaccine to renew', v_years_current, 
            CASE WHEN v_years_current = 1 THEN 'year' ELSE 'years' END);
            v_is_renewable := true;
            v_reasons := array_append(v_reasons, 'alreadyRenewed');
            v_reasons := array_append(v_reasons, 'vaccineExpired');
        ELSIF v_years_until_exp = 0 and v_years_current > 0 THEN
            v_duration := 0;
            v_message := format('Your license will expire on %s, update vaccine to renew', v_license_expiration_date);
            v_is_renewable := true;
            v_reasons := array_append(v_reasons, 'alreadyRenewed');
            v_reasons := array_append(v_reasons, 'vaccineExpired');
        ELSIF v_years_until_exp = 0 THEN
            v_duration := 0;
            v_message := 'Vaccine expired, not eligible for renewal';
            v_is_renewable := true;
            v_reasons := array_append(v_reasons, 'vaccineExpired');
        ELSE
            v_duration := v_years_until_exp;
            v_message := 'Vaccine valid, eligible for renewal';
            v_is_renewable := true;
            v_reasons := array_append(v_reasons, 'vaccineValid');
        END IF;

    -- If No vaccine info at all
    ELSE
        v_duration := 0;
        v_message := 'No vaccine info and not exempt';
        v_is_renewable := true;
        v_reasons := array_append(v_reasons, 'vaccineExpired');
    END IF;

    IF v_license_status.code IN ('PENDING_PAYMENT') THEN
        v_is_renewable := false;
        v_message := 'License is pending payment. Please complete payment to renew or undo the previous renewal.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF v_license_status.code IN ('PENDING_APPROVAL') THEN
        v_is_renewable := false;
        v_message := 'License is pending approval. Please wait for approval before renewing.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF v_license_status.code IN ('REJECTED', 'APPROVED', 'SUBMITTED') THEN
        v_is_renewable := false;
        v_message := 'License is already approved or rejected. Cannot renew.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF v_license_status.code IN ('CANCELED','CLOSED') THEN
        v_is_renewable := false;
        v_message := 'License is canceled or closed. Cannot renew.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    END IF;

    -- If the license is already renewed for max years
    IF v_years_current > (v_max_renewal_years - 1) THEN
        v_is_renewable := false;
        v_message := 'License has already been renewed for the maximum number of years.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    END IF;

    RETURN QUERY SELECT v_duration, v_message, v_is_renewable, to_json(v_reasons)::text;

END;
$$;

--select * from license.fn_determineDogLicenseDuration('e53d8676-70d5-41d6-a338-74d66e8efd59');

update license.license_type lt 
set duration_function = 'fn_determineDogLicenseDuration'
where lt.code = 'dogLicense';