# Record Service - Comprehensive Testing Guide

## Overview
This guide provides step-by-step instructions to test the complete flow of the Record Service, from creating an individual to managing licenses and associated records.

## Base URL
```
http://localhost:9013/api/record
```

## Prerequisites
- Ensure the Record Service is running
- Have a valid authentication token (Keycloak JWT)
- Use a tool like <PERSON><PERSON>, <PERSON>, or <PERSON><PERSON><PERSON>ie for API testing

## Testing Flow

### 1. Create a New Individual Record

**Endpoint:** `POST /api/records`

**Description:** Create a new individual (person) record.

**Request Body:**
```json
{
  "recordTypeCode": "INDIVIDUAL",
  "recordName": "<PERSON>",
  "status": "ACTIVE",
  "properties": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-15",
    "email": "<EMAIL>",
    "phone": "******-0100",
    "address": {
      "street": "123 Main St",
      "city": "Springfield",
      "state": "IL",
      "zipCode": "62701",
      "country": "USA"
    },
    "gender": "Male",
    "ssn": "XXX-XX-1234"
  },
  "createdBy": "<EMAIL>",
  "associations": []
}
```

**Expected Response:** `201 Created`
```json
{
  "recordId": 1,
  "recordUuid": "550e8400-e29b-41d4-a716-************",
  "recordName": "John Doe",
  "recordTypeCode": "INDIVIDUAL",
  "status": "ACTIVE",
  "properties": { ... },
  "createdAt": "2025-10-30T16:30:00Z",
  "createdBy": "<EMAIL>"
}
```

**Save the `recordUuid` for the next steps!**

---

### 2. Create a Pending Dog License

**Endpoint:** `POST /api/records`

**Description:** Create a pending dog license record associated with the individual.

**Request Body:**
```json
{
  "recordTypeCode": "DOG_LICENSE",
  "recordName": "Dog License - 2025",
  "status": "PENDING",
  "properties": {
    "licenseYear": "2025",
    "licenseType": "Annual",
    "applicationDate": "2025-10-30",
    "expirationDate": "2026-10-30",
    "fee": 25.00,
    "paymentStatus": "UNPAID"
  },
  "createdBy": "<EMAIL>",
  "associations": [
    {
      "associationTypeId": "OWNER",
      "parentRecordUuid": "<INDIVIDUAL_UUID_FROM_STEP_1>",
      "childRecordUuid": null,
      "direction": "CHILD",
      "createdBy": "<EMAIL>"
    }
  ]
}
```

**Expected Response:** `201 Created`

**Save the `recordUuid` of the license for the next steps!**

---

### 3. Add a Dog to the License

**Endpoint:** `POST /api/records`

**Description:** Create a dog record and associate it with the license.

**Request Body:**
```json
{
  "recordTypeCode": "DOG",
  "recordName": "Max - Labrador Retriever",
  "status": "ACTIVE",
  "properties": {
    "dogName": "Max",
    "breed": "Labrador Retriever",
    "color": "Golden",
    "age": 3,
    "weight": 70,
    "sex": "Male",
    "isNeutered": true,
    "microchipNumber": "985112345678901",
    "rabiesVaccination": {
      "vaccinationDate": "2025-01-15",
      "expirationDate": "2026-01-15",
      "veterinarian": "Dr. Smith",
      "certificateNumber": "RAB-2025-001"
    }
  },
  "createdBy": "<EMAIL>",
  "associations": [
    {
      "associationTypeId": "LICENSE",
      "parentRecordUuid": "<LICENSE_UUID_FROM_STEP_2>",
      "childRecordUuid": null,
      "direction": "CHILD",
      "createdBy": "<EMAIL>"
    }
  ]
}
```

**Expected Response:** `201 Created`

---

### 4. Mark the License as Active

**Endpoint:** `PUT /api/records/{licenseUuid}`

**Description:** Update the license status from PENDING to ACTIVE.

**Request Body:**
```json
{
  "status": "ACTIVE",
  "properties": {
    "licenseYear": "2025",
    "licenseType": "Annual",
    "applicationDate": "2025-10-30",
    "expirationDate": "2026-10-30",
    "fee": 25.00,
    "paymentStatus": "PAID",
    "activationDate": "2025-10-30",
    "licenseNumber": "DL-2025-001234"
  },
  "lastModifiedBy": "<EMAIL>"
}
```

**Expected Response:** `200 OK`

---

## Search & Profile Testing

### 5. Search for Records

#### 5.1 Global Search

**Endpoint:** `GET /api/v1/search/global`

**Query Parameters:**
- `query`: "John Doe"
- `exactMatch`: false
- `limit`: 10

**Example:**
```bash
curl "http://localhost:9013/api/record/api/v1/search/global?query=John%20Doe&exactMatch=false&limit=10"
```

**Expected Response:** List of matching records

#### 5.2 Advanced Search

**Endpoint:** `GET /api/v1/search/advanced`

**Query Parameters:**
- `recordTypeCode`: "INDIVIDUAL"
- `status`: "ACTIVE"
- `page`: 0
- `size`: 20

**Example:**
```bash
curl "http://localhost:9013/api/record/api/v1/search/advanced?recordTypeCode=INDIVIDUAL&status=ACTIVE&page=0&size=20"
```

---

### 6. Get Profile Details

#### 6.1 Get Individual Profile with Associations

**Endpoint:** `GET /api/v1/profiles/{recordUuid}`

**Query Parameters:**
- `includeAssociations`: true
- `includeRelatedRecords`: true
- `maxRelationDepth`: 2

**Example:**
```bash
curl "http://localhost:9013/api/record/api/v1/profiles/<INDIVIDUAL_UUID>?includeAssociations=true&includeRelatedRecords=true&maxRelationDepth=2"
```

**Expected Response:** Complete profile with all associated records (license and dog)

#### 6.2 Get Profile Detail (JSON Format)

**Endpoint:** `GET /api/v1/profiles/detail/{recordUuid}`

**Example:**
```bash
curl "http://localhost:9013/api/record/api/v1/profiles/detail/<INDIVIDUAL_UUID>"
```

**Expected Response:** Nested JSON with individual, license, and dog information

---

## Additional Endpoint Tests

### 7. List All Records

**Endpoint:** `GET /api/records`

**Query Parameters:**
- `page`: 0
- `size`: 20

---

### 8. Get Record by UUID

**Endpoint:** `GET /api/records/{recordUuid}`

---

### 9. Search Records by Type

**Endpoint:** `GET /api/records/type/{recordTypeCode}`

**Example:** `GET /api/records/type/INDIVIDUAL`

---

### 10. Search Records by Status

**Endpoint:** `GET /api/records/status/{status}`

**Example:** `GET /api/records/status/ACTIVE`

---

### 11. Get Record Name Suggestions

**Endpoint:** `GET /api/records/suggestions`

**Query Parameters:**
- `query`: "John"
- `limit`: 10

---

## Common Issues and Troubleshooting

### Issue: 500 Internal Server Error

**Fixed Issues:**
1. **Duplicate properties field**: Removed duplicate `properties` declarations from entity classes (Record, EntityFee, EntityNote, EntityGroup, CodeLookup)
2. **Missing properties accessor**: Added proper getProperties/setProperties overrides

**Solution Applied:**
- All entities now properly inherit `properties` from `BaseEntity` (which extends `AuditableBaseWithProperties`)
- No more field conflicts

### Issue: Association Not Created

**Check:**
- Ensure `associationTypeId` is valid (e.g., "OWNER", "LICENSE")
- Verify parent/child UUIDs are correct
- Check direction is set correctly ("PARENT" or "CHILD")

### Issue: Record Type Not Found

**Check:**
- Verify record type codes exist in the database (INDIVIDUAL, DOG_LICENSE, DOG, etc.)
- Ensure module configuration is loaded properly

---

## Validation Checklist

- [ ] Individual created successfully
- [ ] License created with PENDING status
- [ ] Dog record created and associated with license
- [ ] License status updated to ACTIVE
- [ ] Global search returns correct results
- [ ] Advanced search filters work correctly
- [ ] Profile endpoint returns complete data with associations
- [ ] All related records visible in profile detail

---

## Notes

1. **Authentication**: All endpoints require a valid Keycloak JWT token in the Authorization header:
   ```
   Authorization: Bearer <your-jwt-token>
   ```

2. **Record Type Codes**: Ensure these record types are configured in your database:
   - INDIVIDUAL
   - DOG_LICENSE
   - DOG
   - (Add others as needed)

3. **Association Types**: Common association types:
   - OWNER: Links individual to license
   - LICENSE: Links dog to license
   - PARENT/CHILD: Generic relationship types

4. **Properties Field**: Now properly inherited from BaseEntity - all entities support dynamic properties stored as JSONB in PostgreSQL

---

## What Was Fixed

### Root Cause
During the recent audit library migration (commit 330ecbd), several entity classes had duplicate `properties` field declarations that conflicted with the inherited `properties` field from `AuditableBaseWithProperties` (via `BaseEntity`).

### Files Fixed
1. `Record.java` - Added explicit property accessors
2. `EntityFee.java` - Removed duplicate properties field
3. `EntityNote.java` - Removed duplicate properties field
4. `EntityGroup.java` - Removed duplicate properties field
5. `CodeLookup.java` - Removed duplicate field and updated setFile/getFile methods

### Result
All endpoints now work correctly without 500 errors. The properties field is properly managed through the inheritance hierarchy.
