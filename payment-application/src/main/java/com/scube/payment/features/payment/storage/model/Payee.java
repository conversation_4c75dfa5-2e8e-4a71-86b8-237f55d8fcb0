package com.scube.payment.features.payment.storage.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Entity
@Table(name = "payee")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Audited
public class Payee extends AuditableEntity {
    @Size(max = 255)
    private String firstName;

    @Size(max = 255)
    private String lastName;

    @Size(max = 255)
    private String businessName;

    @Size(max = 255)
    @Email
    private String email;

    @Size(max = 20)
    private String phone;

    @Size(max = 255)
    private String mailingAddress;

    @Size(max = 255)
    private String mailingAddress2;

    @Size(max = 255)
    private String mailingCity;

    @Size(max = 255)
    private String mailingState;

    @Size(max = 255)
    private String mailingZipCode;

    private boolean billingSameAsMailing;

    @Size(max = 255)
    private String billingAddress;

    @Size(max = 255)
    private String billingAddress2;

    @Size(max = 255)
    private String billingCity;

    @Size(max = 255)
    private String billingState;

    @Size(max = 255)
    private String billingZipCode;
}
