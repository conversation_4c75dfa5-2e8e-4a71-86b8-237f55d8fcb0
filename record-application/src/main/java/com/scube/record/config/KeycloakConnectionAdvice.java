package com.scube.record.config;

import com.scube.lib.error.ErrorResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.HttpHostConnectException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice
@Slf4j
public class KeycloakConnectionAdvice {
    @Autowired
    private ServerProperties serverProperties;

    @ExceptionHandler(HttpHostConnectException.class)
    public ResponseEntity<ErrorResponse> handleException(HttpHostConnectException ex, HttpServletRequest request) {
        ex.printStackTrace();
        var errorResponse = new ErrorResponse(serverProperties, HttpStatus.BAD_GATEWAY, request, ex);
        return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(errorResponse);
    }
}
