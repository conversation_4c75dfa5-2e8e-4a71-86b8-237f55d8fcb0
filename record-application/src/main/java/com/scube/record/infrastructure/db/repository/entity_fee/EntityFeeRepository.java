package com.scube.record.infrastructure.db.repository.entity_fee;

import com.scube.record.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.record.infrastructure.db.repository.AuditableEntityRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface EntityFeeRepository extends AuditableEntityRepository<EntityFee, Long> {

    @Query("SELECT ef FROM EntityFee ef WHERE ef.isRecurring = true AND ef.nextDueDate <= :currentDate AND ef.isDeleted = false")
    List<EntityFee> findActiveRecurringFeesDue(@Param("currentDate") Instant currentDate);

    @Query("SELECT ef FROM EntityFee ef WHERE ef.paymentStatus IN ('UNPAID', 'PARTIALLY_PAID') AND ef.isDeleted = false")
    List<EntityFee> findUnpaidFees();

    @Query("SELECT ef FROM EntityFee ef WHERE ef.orderId = :orderId AND ef.isDeleted = false")
    List<EntityFee> findByOrderId(@Param("orderId") String orderId);

    @Query("SELECT ef FROM EntityFee ef WHERE ef.feeName = :feeName AND ef.isDeleted = false")
    List<EntityFee> findByFeeName(@Param("feeName") String feeName);

    @Query("SELECT ef FROM EntityFee ef WHERE ef.dueDate <= :dueDate AND ef.paymentStatus = 'UNPAID' AND ef.isDeleted = false")
    List<EntityFee> findOverdueFees(@Param("dueDate") Instant dueDate);
}