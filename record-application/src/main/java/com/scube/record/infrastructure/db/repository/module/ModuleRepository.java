package com.scube.record.infrastructure.db.repository.module;

import com.scube.record.infrastructure.db.entity.module.ModuleDefinition;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for ModuleDefinition entity.
 * Provides methods to query and manage module definitions.
 */
@Repository
public interface ModuleRepository extends JpaRepository<ModuleDefinition, Long> {

    /**
     * Find module by UUID.
     */
    Optional<ModuleDefinition> findByModuleUuid(UUID moduleUuid);

    /**
     * Find module by code.
     */
    Optional<ModuleDefinition> findByModuleCode(String moduleCode);

    /**
     * Find all active modules.
     */
    List<ModuleDefinition> findByIsActive(Boolean isActive);

    /**
     * Find all active modules with pagination.
     */
    Page<ModuleDefinition> findByIsActive(Boolean isActive, Pageable pageable);

    /**
     * Search modules by keyword in name, code, or description.
     */
    @Query("SELECT m FROM ModuleDefinition m WHERE " +
           "m.moduleName LIKE %:keyword% OR " +
           "m.moduleCode LIKE %:keyword% OR " +
           "m.description LIKE %:keyword%")
    Page<ModuleDefinition> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * Check if module exists by code.
     */
    boolean existsByModuleCode(String moduleCode);

    /**
     * Check if module exists by UUID.
     */
    boolean existsByModuleUuid(UUID moduleUuid);

    /**
     * Find modules by version.
     */
    List<ModuleDefinition> findByVersion(String version);

    /**
     * Count active modules.
     */
    long countByIsActive(Boolean isActive);
}

