package com.scube.record.infrastructure.db.entity.settings;

import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@Entity
@Table(name = "setting", schema = "record",
       uniqueConstraints = @UniqueConstraint(columnNames = {"setting_key", "realm"}))
@Getter
@Setter
@NoArgsConstructor
@Audited
public class Setting extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "setting_id")
    private Long settingId;

    @Column(name = "setting_key", nullable = false)
    private String settingKey;

    @Column(name = "setting_value", columnDefinition = "TEXT")
    private String settingValue;

    @Column(name = "setting_type")
    private String settingType;

    @Column(name = "category")
    private String category;

    @Column(name = "description")
    private String description;

    @Column(name = "is_public")
    private Boolean isPublic = false;

    @Column(name = "is_encrypted")
    private Boolean isEncrypted = false;

    @Column(name = "realm", nullable = false)
    private String realm;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;

    @Override
    public String getTableName() {
        return "setting";
    }

    @Override
    public Long getId() {
        return settingId;
    }
}