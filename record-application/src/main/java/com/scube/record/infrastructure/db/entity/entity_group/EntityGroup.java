package com.scube.record.infrastructure.db.entity.entity_group;

import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@Entity
@Table(name = "entity_group", schema = "record")
@Getter
@Setter
@NoArgsConstructor
@Audited
public class EntityGroup extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "entity_group_id")
    private Long entityGroupId;

    @Column(name = "group_name", nullable = false)
    private String groupName;

    @Column(name = "group_type", nullable = false)
    private String groupType;

    @Column(name = "description")
    private String description;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "custom_fields", columnDefinition = "jsonb")
    private Map<String, Object> customFields;

    @Override
    public String getTableName() {
        return "entity_group";
    }

    @Override
    public Long getId() {
        return entityGroupId;
    }
}