package com.scube.record.infrastructure.db.repository.code_lookup;

import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookup;
import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.record.infrastructure.db.repository.AuditableEntityRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CodeLookupRepository extends AuditableEntityRepository<CodeLookup, Long> {

    Optional<CodeLookup> findByCode(String code);

    @Query("SELECT cl FROM CodeLookup cl WHERE cl.entityType = :entityType AND cl.entityId = :entityId AND cl.realm = :realm")
    List<CodeLookup> findByEntityTypeAndEntityIdAndRealm(
        @Param("entityType") String entityType,
        @Param("entityId") String entityId,
        @Param("realm") String realm
    );

    @Query("SELECT cl FROM CodeLookup cl WHERE cl.entityType = :entityType AND cl.entityId = :entityId AND cl.realm = :realm AND cl.action = :action")
    Optional<CodeLookup> findByEntityTypeAndEntityIdAndRealmAndAction(
        @Param("entityType") String entityType,
        @Param("entityId") String entityId,
        @Param("realm") String realm,
        @Param("action") CodeLookupActionEnum action
    );

    @Query("SELECT cl FROM CodeLookup cl WHERE cl.entityType = :entityType AND cl.realm = :realm")
    List<CodeLookup> findByEntityTypeAndRealm(
        @Param("entityType") String entityType,
        @Param("realm") String realm
    );

    @Query("SELECT cl FROM CodeLookup cl WHERE cl.realm = :realm")
    List<CodeLookup> findByRealm(@Param("realm") String realm);

    @Query("SELECT cl FROM CodeLookup cl WHERE cl.action = :action AND cl.realm = :realm")
    List<CodeLookup> findByActionAndRealm(
        @Param("action") CodeLookupActionEnum action,
        @Param("realm") String realm
    );

    boolean existsByCode(String code);

    @Query("SELECT cl FROM CodeLookup cl WHERE cl.entityType = :entityType AND cl.entityId = :entityId AND cl.realm = :realm AND cl.isDeleted = false")
    List<CodeLookup> findActiveByEntityTypeAndEntityIdAndRealm(
        @Param("entityType") String entityType,
        @Param("entityId") String entityId,
        @Param("realm") String realm
    );
}