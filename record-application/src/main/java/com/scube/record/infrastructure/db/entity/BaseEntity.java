package com.scube.record.infrastructure.db.entity;

import com.scube.audit.auditable.entity.AuditableBaseWithProperties;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

@MappedSuperclass
@SuperBuilder
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public abstract class BaseEntity extends AuditableBaseWithProperties<Long> implements IBaseEntity {

    @Column(name = "is_deleted")
    private Boolean isDeleted = false;

    @Override
    public abstract Long getId();

    // Wrapper methods to match the naming convention used in the service code
    public Instant getCreatedAt() {
        return getCreatedDate();
    }

    public void setCreatedAt(Instant createdAt) {
        setCreatedDate(createdAt);
    }

    public Instant getLastModifiedAt() {
        return getLastModifiedDate();
    }

    public void setLastModifiedAt(Instant lastModifiedAt) {
        setLastModifiedDate(lastModifiedAt);
    }
}