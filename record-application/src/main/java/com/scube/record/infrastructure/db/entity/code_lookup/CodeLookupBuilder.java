package com.scube.record.infrastructure.db.entity.code_lookup;

import com.scube.record.utils.code_generator.CGOptions;
import com.scube.record.utils.code_generator.CodeGeneratorUtil;

public class CodeLookupBuilder {

    public static CodeLookup buildRecordCode(String entityId, String realm) {
        CodeLookup codeLookup = new CodeLookup();
        codeLookup.setCode(CodeGeneratorUtil.generateCode(CGOptions.ALPHANUMERIC, 12));
        codeLookup.setEntityType("RECORD");
        codeLookup.setEntityId(entityId);
        codeLookup.setRealm(realm);
        codeLookup.setAction(CodeLookupActionEnum.LOOKUP);
        return codeLookup;
    }

    public static CodeLookup buildIndividualCode(String entityId, String realm) {
        CodeLookup codeLookup = new CodeLookup();
        codeLookup.setCode(CodeGeneratorUtil.generateCode(CGOptions.ALPHANUMERIC, 12));
        codeLookup.setEntityType("INDIVIDUAL");
        codeLookup.setEntityId(entityId);
        codeLookup.setRealm(realm);
        codeLookup.setAction(CodeLookupActionEnum.LOOKUP);
        return codeLookup;
    }

    public static CodeLookup buildTagCode(String entityId, String realm, CodeLookupActionEnum action) {
        CodeLookup codeLookup = new CodeLookup();
        codeLookup.setCode(CodeGeneratorUtil.generateCode(CGOptions.ALPHANUMERIC, 12));
        codeLookup.setEntityType("TAG");
        codeLookup.setEntityId(entityId);
        codeLookup.setRealm(realm);
        codeLookup.setAction(action != null ? action : CodeLookupActionEnum.LOOKUP);
        return codeLookup;
    }

    public static CodeLookup buildPublicPageCode(String pageType, String realm) {
        CodeLookup codeLookup = new CodeLookup();
        codeLookup.setCode(CodeGeneratorUtil.generateCode(CGOptions.ALPHANUMERIC, 12));
        codeLookup.setEntityType("PUBLIC_PAGE");
        codeLookup.setEntityId(pageType);
        codeLookup.setRealm(realm);

        switch (pageType.toUpperCase()) {
            case "LANDING":
                codeLookup.setAction(CodeLookupActionEnum.LANDING_PAGE);
                break;
            case "LOGIN":
                codeLookup.setAction(CodeLookupActionEnum.LOGIN_PAGE);
                break;
            case "SIGNUP":
                codeLookup.setAction(CodeLookupActionEnum.SIGN_UP_PAGE);
                break;
            case "DEMO":
                codeLookup.setAction(CodeLookupActionEnum.DEMO_PAGE);
                break;
            default:
                codeLookup.setAction(CodeLookupActionEnum.LANDING_PAGE);
        }

        return codeLookup;
    }

    public static CodeLookup buildCustomCode(String entityType, String entityId, String realm, CodeLookupActionEnum action) {
        CodeLookup codeLookup = new CodeLookup();
        codeLookup.setCode(CodeGeneratorUtil.generateCode(CGOptions.ALPHANUMERIC, 12));
        codeLookup.setEntityType(entityType);
        codeLookup.setEntityId(entityId);
        codeLookup.setRealm(realm);
        codeLookup.setAction(action != null ? action : CodeLookupActionEnum.LOOKUP);
        return codeLookup;
    }
}