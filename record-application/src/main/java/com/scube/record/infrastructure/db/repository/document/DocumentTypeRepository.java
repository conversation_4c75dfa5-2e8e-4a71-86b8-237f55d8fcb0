package com.scube.record.infrastructure.db.repository.document;

import com.scube.record.infrastructure.db.entity.document.DocumentType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentTypeRepository extends JpaRepository<DocumentType, Long>, JpaSpecificationExecutor<DocumentType> {

    Optional<DocumentType> findByTypeKey(String typeKey);

    @Query("SELECT dt FROM DocumentType dt WHERE dt.isActive = true")
    List<DocumentType> findAllActive();

    @Query("SELECT dt FROM DocumentType dt WHERE dt.isActive = true AND dt.typeKey = :typeKey")
    Optional<DocumentType> findActiveByTypeKey(@Param("typeKey") String typeKey);

    @Query("SELECT dt FROM DocumentType dt WHERE dt.groupName = :groupName AND dt.isActive = true")
    List<DocumentType> findAllActiveByGroupName(@Param("groupName") String groupName);

    boolean existsByTypeKey(String typeKey);
}