package com.scube.record.infrastructure.db.repository.record;

import com.scube.record.infrastructure.db.entity.record.AssociationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssociationTypeRepository extends JpaRepository<AssociationType, Long> {

    Optional<AssociationType> findByAssociationTypeUuid(String associationTypeUuid);

    Optional<AssociationType> findByAssociationName(String associationName);

    @Query("SELECT at FROM AssociationType at WHERE at.associationName LIKE %:keyword%")
    Page<AssociationType> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    boolean existsByAssociationTypeUuid(String associationTypeUuid);

    boolean existsByAssociationName(String associationName);

    @Query(value = "SELECT * FROM association_type WHERE properties->>'type_code' = ?1", nativeQuery = true)
    Optional<AssociationType> findByTypeCode(String typeCode);

    @Query(value = "SELECT * FROM association_type WHERE jsonb_exists(properties->'parent_record_types', ?1)", nativeQuery = true)
    List<AssociationType> findByParentRecordType(String recordType);

    @Query(value = "SELECT * FROM association_type WHERE jsonb_exists(properties->'child_record_types', ?1)", nativeQuery = true)
    List<AssociationType> findByChildRecordType(String recordType);
}