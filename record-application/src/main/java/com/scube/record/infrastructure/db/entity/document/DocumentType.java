package com.scube.record.infrastructure.db.entity.document;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;

@Entity
@Table(name = "document_type", uniqueConstraints = {
    @UniqueConstraint(name = "uk_document_type_type_key", columnNames = "type_key")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentType {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "document_type_id")
    private Long documentTypeId;

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "created_by", length = 250, nullable = false)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedAt;

    @Column(name = "last_modified_by", length = 250, nullable = false)
    private String lastModifiedBy;

    // Document type specific fields
    @Size(max = 255)
    @Column(name = "type_key", unique = true, nullable = false)
    private String typeKey;

    @Size(max = 255)
    @Column(name = "name")
    private String name;

    @Size(max = 255)
    @Column(name = "group_name")
    private String groupName;

    @Size(max = 500)
    @Column(name = "description")
    private String description;

    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;
}