package com.scube.record.infrastructure.db.repository.record;

import com.scube.record.infrastructure.db.entity.record.RecordType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RecordTypeRepository extends JpaRepository<RecordType, Long> {

    Optional<RecordType> findByRecordTypeUuid(UUID recordTypeUuid);

    Optional<RecordType> findByTypeCode(String typeCode);

    List<RecordType> findByTypeName(String typeName);

    List<RecordType> findByParentIsNull();

    List<RecordType> findByParent(RecordType parent);

    @Query("SELECT rt FROM RecordType rt WHERE rt.typeName LIKE %:keyword% OR rt.typeCode LIKE %:keyword% OR rt.description LIKE %:keyword%")
    Page<RecordType> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    boolean existsByTypeCode(String typeCode);

    boolean existsByRecordTypeUuid(UUID recordTypeUuid);

    List<RecordType> findByModuleCode(String moduleCode);

    long countByModuleCode(String moduleCode);
}