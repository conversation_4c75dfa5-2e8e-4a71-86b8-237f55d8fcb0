package com.scube.record.infrastructure.db.repository.record;

import com.scube.record.infrastructure.db.entity.record.Association;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface AssociationRepository extends JpaRepository<Association, Long>, JpaSpecificationExecutor<Association> {

    Optional<Association> findByAssociationUuid(UUID associationUuid);

    List<Association> findByParentId(Long parentId);

    List<Association> findByChildId(Long childId);

    List<Association> findByParentIdAndParentType(Long parentId, String parentType);

    List<Association> findByChildIdAndChildType(Long childId, String childType);

    List<Association> findByAssociationTypeId(String associationTypeId);

    @Query("SELECT a FROM Association a WHERE a.parentId = :parentId AND a.childId = :childId")
    List<Association> findByParentIdAndChildId(@Param("parentId") Long parentId, @Param("childId") Long childId);

    @Query("SELECT a FROM Association a WHERE a.parentType = :parentType AND a.childType = :childType")
    Page<Association> findByParentTypeAndChildType(@Param("parentType") String parentType, @Param("childType") String childType, Pageable pageable);

    boolean existsByAssociationUuid(UUID associationUuid);

    boolean existsByParentIdAndChildIdAndAssociationTypeId(Long parentId, Long childId, String associationTypeId);

    @Query("SELECT a FROM Association a WHERE a.parentId = :recordId OR a.childId = :recordId")
    List<Association> findByParentIdOrChildId(@Param("recordId") Long recordId, @Param("recordId") Long recordId2);

    @Query("SELECT a FROM Association a WHERE (a.parentId = :recordId OR a.childId = :recordId) AND a.associationTypeId IN :associationTypes")
    List<Association> findByParentIdOrChildIdAndAssociationTypeIdIn(@Param("recordId") Long recordId, @Param("recordId") Long recordId2, @Param("associationTypes") List<String> associationTypes);

    @Query("SELECT COUNT(a) FROM Association a WHERE a.parentId = :recordId OR a.childId = :recordId")
    int countByParentIdOrChildId(@Param("recordId") Long recordId, @Param("recordId") Long recordId2);

    List<Association> findByParentIdAndAssociationTypeIdIn(Long parentId, List<String> associationTypes);

    List<Association> findByChildIdAndAssociationTypeIdIn(Long childId, List<String> associationTypes);

    @Modifying
    @Query("DELETE FROM Association a WHERE a.parentId = :parentId AND a.childId = :childId")
    void deleteByParentIdAndChildId(@Param("parentId") Long parentId, @Param("childId") Long childId);
}