package com.scube.record.infrastructure.db.entity.record;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "association")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Audited
public class Association extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "association_id")
    private Long associationId;

    @Column(name = "association_uuid", nullable = false)
    private UUID associationUuid;

    @Column(name = "parent_type", length = 20)
    private String parentType;

    @Column(name = "child_type", length = 20)
    private String childType;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "child_id")
    private Long childId;

    @Column(name = "association_type_id", length = 50)
    private String associationTypeId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "properties", columnDefinition = "jsonb")
    private JsonNode properties;

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "created_by", length = 100, nullable = false)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedAt;

    @Column(name = "last_modified_by", length = 100, nullable = false)
    private String lastModifiedBy;

    @PrePersist
    private void generateUuid() {
        if (associationUuid == null) {
            associationUuid = UUID.randomUUID();
        }
    }

    @Override
    public Long getId() {
        return associationId;
    }

    @Override
    public String getTableName() {
        return "association";
    }
}