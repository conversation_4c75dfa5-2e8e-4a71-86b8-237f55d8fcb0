package com.scube.record.infrastructure.db.entity.document;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "document", indexes = {
    @Index(name = "idx_document_document_uuid", columnList = "document_uuid"),
    @Index(name = "idx_document_document_type_id", columnList = "document_type_id"),
    @Index(name = "idx_document_is_deleted", columnList = "is_deleted")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Document {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "document_id")
    private Long documentId;

    @Column(name = "document_uuid", unique = true, nullable = false)
    private UUID documentUuid;

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "created_by", length = 1000, nullable = false)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedAt;

    @Column(name = "last_modified_by", length = 1000, nullable = false)
    private String lastModifiedBy;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "properties", columnDefinition = "jsonb")
    private JsonNode properties;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "events", columnDefinition = "jsonb")
    private JsonNode events;

    // Document specific fields
    @Size(max = 255)
    @Column(name = "content_type")
    private String contentType;

    @Size(max = 255)
    @Column(name = "file_name")
    private String fileName;

    @PositiveOrZero
    @Column(name = "file_size")
    private Long fileSize;

    @Size(max = 255)
    @Column(name = "file_url")
    private String fileUrl;

    @Column(name = "document_service_uuid", nullable = false)
    private UUID documentServiceUuid;

    @Column(name = "deleted_date")
    private Instant deletedDate;

    @Column(name = "is_deleted", nullable = false)
    private boolean isDeleted = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_type_id", nullable = false)
    private DocumentType documentType;

    // Association support
    @Column(name = "dummy_column", length = 50)
    private String dummyColumn = "DOCUMENT";

    @PrePersist
    private void generateUuid() {
        if (documentUuid == null) {
            documentUuid = UUID.randomUUID();
        }
    }
}