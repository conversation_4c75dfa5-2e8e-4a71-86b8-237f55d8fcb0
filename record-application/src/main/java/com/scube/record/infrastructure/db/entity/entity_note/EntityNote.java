package com.scube.record.infrastructure.db.entity.entity_note;

import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@Entity
@Table(name = "entity_note", schema = "record")
@Getter
@Setter
@NoArgsConstructor
@Audited
public class EntityNote extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "entity_note_id")
    private Long entityNoteId;

    @Column(name = "title")
    private String title;

    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    @Column(name = "note_type")
    private String noteType;

    @Column(name = "is_public")
    private Boolean isPublic = false;

    @Column(name = "is_pinned")
    private Boolean isPinned = false;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "tags", columnDefinition = "jsonb")
    private String[] tags;

    @Override
    public String getTableName() {
        return "entity_note";
    }

    @Override
    public Long getId() {
        return entityNoteId;
    }
}