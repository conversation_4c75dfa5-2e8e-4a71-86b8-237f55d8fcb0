package com.scube.record.infrastructure.db.repository.settings;

import com.scube.record.infrastructure.db.entity.settings.Setting;
import com.scube.record.infrastructure.db.repository.AuditableEntityRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SettingRepository extends AuditableEntityRepository<Setting, Long> {

    @Query("SELECT s FROM Setting s WHERE s.settingKey = :settingKey AND s.realm = :realm AND s.isDeleted = false")
    Optional<Setting> findBySettingKeyAndRealm(@Param("settingKey") String settingKey, @Param("realm") String realm);

    @Query("SELECT s FROM Setting s WHERE s.realm = :realm AND s.isDeleted = false")
    List<Setting> findByRealm(@Param("realm") String realm);

    @Query("SELECT s FROM Setting s WHERE s.category = :category AND s.realm = :realm AND s.isDeleted = false")
    List<Setting> findByCategoryAndRealm(@Param("category") String category, @Param("realm") String realm);

    @Query("SELECT s FROM Setting s WHERE s.isPublic = true AND s.isDeleted = false")
    List<Setting> findPublicSettings();

    @Query("SELECT s FROM Setting s WHERE s.settingType = :settingType AND s.realm = :realm AND s.isDeleted = false")
    List<Setting> findBySettingTypeAndRealm(@Param("settingType") String settingType, @Param("realm") String realm);

    boolean existsBySettingKeyAndRealm(String settingKey, String realm);
}