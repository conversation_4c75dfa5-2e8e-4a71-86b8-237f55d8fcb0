package com.scube.record.infrastructure.db.entity.record;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "record_type")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RecordType extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "record_type_id")
    private Long recordTypeId;

    @Column(name = "record_type_uuid", unique = true, nullable = false)
    private UUID recordTypeUuid;

    @Column(name = "type_code", length = 100, unique = true, nullable = false)
    private String typeCode;

    @Column(name = "type_name", length = 255, nullable = false)
    private String typeName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", referencedColumnName = "record_type_id")
    private RecordType parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RecordType> children;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "properties", columnDefinition = "jsonb")
    private JsonNode properties;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "config_json", columnDefinition = "jsonb")
    private JsonNode configJson;

    @Column(name = "module_code", length = 100)
    private String moduleCode;

    @CreationTimestamp
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "created_by", length = 100, nullable = false)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedAt;

    @Column(name = "last_modified_by", length = 100, nullable = false)
    private String lastModifiedBy;

    @PrePersist
    private void generateUuid() {
        if (recordTypeUuid == null) {
            recordTypeUuid = UUID.randomUUID();
        }
    }

    @Override
    public Long getId() {
        return recordTypeId;
    }

    @Override
    public String getTableName() {
        return "record_type";
    }
}