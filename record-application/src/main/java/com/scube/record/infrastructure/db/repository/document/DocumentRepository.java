package com.scube.record.infrastructure.db.repository.document;

import com.scube.record.infrastructure.db.entity.document.Document;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long>, JpaSpecificationExecutor<Document> {

    Optional<Document> findByDocumentUuid(UUID documentUuid);

    @Query("SELECT d FROM Document d WHERE d.isDeleted = false")
    List<Document> findAllActive();

    @Query("SELECT d FROM Document d WHERE d.isDeleted = false AND d.documentType.documentTypeId = :documentTypeId")
    List<Document> findAllActiveByDocumentType(@Param("documentTypeId") Long documentTypeId);

    @Query("SELECT d FROM Document d WHERE d.isDeleted = false AND d.documentUuid = :documentUuid")
    Optional<Document> findActiveByDocumentUuid(@Param("documentUuid") UUID documentUuid);

    @Query("SELECT d FROM Document d WHERE d.createdBy = :createdBy AND d.isDeleted = false")
    List<Document> findAllActiveByCreatedBy(@Param("createdBy") String createdBy);
}