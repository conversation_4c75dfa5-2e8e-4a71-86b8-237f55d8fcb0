package com.scube.record.infrastructure.db.repository.record;

import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RecordRepository extends JpaRepository<Record, Long>, JpaSpecificationExecutor<Record> {

    Optional<Record> findByRecordUuid(UUID recordUuid);

    List<Record> findByRecordType(RecordType recordType);

    List<Record> findByStatus(String status);

    Page<Record> findByRecordTypeAndStatus(RecordType recordType, String status, Pageable pageable);

    @Query("SELECT r FROM Record r WHERE r.recordName LIKE %:keyword%")
    Page<Record> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    List<Record> findByRecordNameContaining(String recordName);

    boolean existsByRecordUuid(UUID recordUuid);

    void deleteByRecordUuid(UUID recordUuid);

    @Query(value = "SELECT DISTINCT r.record_name FROM record r WHERE LOWER(r.record_name) LIKE LOWER(CONCAT('%', :query, '%')) ORDER BY r.record_name LIMIT :limit", nativeQuery = true)
    List<String> findRecordNameSuggestions(@Param("query") String query, @Param("limit") int limit);

    @Query(value = "SELECT DISTINCT r.record_name FROM record r WHERE r.record_type_id = :recordTypeId AND LOWER(r.record_name) LIKE LOWER(CONCAT('%', :query, '%')) ORDER BY r.record_name LIMIT :limit", nativeQuery = true)
    List<String> findRecordNameSuggestionsByType(@Param("query") String query, @Param("recordTypeId") Long recordTypeId, @Param("limit") int limit);
}