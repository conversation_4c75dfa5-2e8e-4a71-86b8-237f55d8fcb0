package com.scube.record.infrastructure.db.entity.code_lookup;

import com.scube.record.infrastructure.db.entity.BaseEntity;
import com.scube.record.features.qr_codes.QrCodeMultipartFile;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Base64;
import java.util.Map;

@Entity
@Table(name = "code_lookup", schema = "record")
@Getter
@Setter
@NoArgsConstructor
@Audited
public class CodeLookup extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "code_lookup_id")
    private Long codeLookupId;

    @Column(name = "code", unique = true, nullable = false)
    private String code;

    @Column(name = "entity_type", nullable = false)
    private String entityType;

    @Column(name = "entity_id", nullable = false)
    private String entityId;

    @Column(name = "realm", nullable = false)
    private String realm;

    @Enumerated(EnumType.STRING)
    @Column(name = "action", nullable = false)
    private CodeLookupActionEnum action;

    public void setFile(MultipartFile file) throws IOException {
        if (file != null && !file.isEmpty()) {
            byte[] fileBytes = file.getBytes();
            String base64 = Base64.getEncoder().encodeToString(fileBytes);

            Map<String, Object> props = getProperties();
            if (props == null) {
                props = new java.util.HashMap<>();
                setProperties(props);
            }

            props.put("file_name", file.getOriginalFilename());
            props.put("file_type", file.getContentType());
            props.put("file_size", file.getSize());
            props.put("file_data", base64);
        }
    }

    public MultipartFile getFile() {
        Map<String, Object> props = getProperties();
        if (props == null || !props.containsKey("file_data")) {
            return null;
        }

        String base64Data = (String) props.get("file_data");
        String fileName = (String) props.get("file_name");
        String contentType = (String) props.get("file_type");

        byte[] fileBytes = Base64.getDecoder().decode(base64Data);
        return new QrCodeMultipartFile(fileName, contentType, fileBytes);
    }

    @Override
    public String getTableName() {
        return "code_lookup";
    }

    @Override
    public Long getId() {
        return codeLookupId;
    }
}