package com.scube.record.infrastructure.db.entity.record;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;
import org.hibernate.type.SqlTypes;

import java.util.UUID;

@Entity
@Table(name = "record")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Audited
public class Record extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "record_id")
    private Long recordId;

    @Column(name = "record_uuid", unique = true, nullable = false)
    private UUID recordUuid;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "record_type_id", referencedColumnName = "record_type_id")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private RecordType recordType;

    @Column(name = "record_name", length = 255, nullable = false)
    private String recordName;

    @Column(name = "status", length = 50, nullable = false)
    private String status = "ACTIVE";

    @PrePersist
    private void generateUuid() {
        if (recordUuid == null) {
            recordUuid = UUID.randomUUID();
        }
    }

    @Override
    public String getTableName() {
        return "record";
    }

    @Override
    public Long getId() {
        return recordId;
    }
}