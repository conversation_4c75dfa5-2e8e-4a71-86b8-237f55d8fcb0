package com.scube.record.infrastructure.db.entity.entity_fee;

import com.scube.record.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;

@Entity
@Table(name = "entity_fee", schema = "record")
@Getter
@Setter
@NoArgsConstructor
@Audited
public class EntityFee extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "entity_fee_id")
    private Long entityFeeId;

    @Column(name = "fee_name", nullable = false)
    private String feeName;

    @Column(name = "fee_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal feeAmount;

    @Column(name = "paid_amount", precision = 10, scale = 2)
    private BigDecimal paidAmount = BigDecimal.ZERO;

    @Column(name = "outstanding_amount", precision = 10, scale = 2)
    private BigDecimal outstandingAmount;

    @Column(name = "payment_status")
    @Enumerated(EnumType.STRING)
    private PaymentStatus paymentStatus = PaymentStatus.UNPAID;

    @Column(name = "due_date")
    private Instant dueDate;

    @Column(name = "paid_date")
    private Instant paidDate;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "is_recurring")
    private Boolean isRecurring = false;

    @Column(name = "recurring_frequency")
    private String recurringFrequency;

    @Column(name = "next_due_date")
    private Instant nextDueDate;

    @Column(name = "last_processed_date")
    private Instant lastProcessedDate;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "custom_fields", columnDefinition = "jsonb")
    private Map<String, Object> customFields;

    @Override
    public String getTableName() {
        return "entity_fee";
    }

    @PrePersist
    @PreUpdate
    private void updateOutstandingAmount() {
        if (feeAmount != null && paidAmount != null) {
            this.outstandingAmount = feeAmount.subtract(paidAmount);

            if (outstandingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                this.paymentStatus = PaymentStatus.PAID;
                if (this.paidDate == null) {
                    this.paidDate = Instant.now();
                }
            } else if (paidAmount.compareTo(BigDecimal.ZERO) > 0) {
                this.paymentStatus = PaymentStatus.PARTIALLY_PAID;
            } else {
                this.paymentStatus = PaymentStatus.UNPAID;
            }
        }
    }

    @Override
    public Long getId() {
        return entityFeeId;
    }

    public enum PaymentStatus {
        UNPAID,
        PARTIALLY_PAID,
        PAID,
        OVERDUE,
        CANCELLED
    }
}