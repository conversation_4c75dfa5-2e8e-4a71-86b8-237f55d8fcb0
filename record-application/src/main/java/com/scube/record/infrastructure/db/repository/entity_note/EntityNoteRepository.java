package com.scube.record.infrastructure.db.repository.entity_note;

import com.scube.record.infrastructure.db.entity.entity_note.EntityNote;
import com.scube.record.infrastructure.db.repository.AuditableEntityRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntityNoteRepository extends AuditableEntityRepository<EntityNote, Long> {

    @Query("SELECT en FROM EntityNote en WHERE en.noteType = :noteType AND en.isDeleted = false")
    List<EntityNote> findByNoteType(@Param("noteType") String noteType);

    @Query("SELECT en FROM EntityNote en WHERE en.isPublic = true AND en.isDeleted = false")
    List<EntityNote> findPublicNotes();

    @Query("SELECT en FROM EntityNote en WHERE en.isPinned = true AND en.isDeleted = false")
    List<EntityNote> findPinnedNotes();

    @Query("SELECT en FROM EntityNote en WHERE en.createdBy = :createdBy AND en.isDeleted = false")
    List<EntityNote> findByCreatedBy(@Param("createdBy") String createdBy);

    @Query("SELECT en FROM EntityNote en WHERE en.title ILIKE %:keyword% OR en.content ILIKE %:keyword% AND en.isDeleted = false")
    List<EntityNote> searchByKeyword(@Param("keyword") String keyword);
}