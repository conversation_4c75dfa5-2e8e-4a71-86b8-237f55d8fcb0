package com.scube.record.infrastructure.db.repository.entity_group;

import com.scube.record.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.record.infrastructure.db.repository.AuditableEntityRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EntityGroupRepository extends AuditableEntityRepository<EntityGroup, Long> {

    @Query("SELECT eg FROM EntityGroup eg WHERE eg.isActive = true AND eg.isDeleted = false")
    List<EntityGroup> findAllActive();

    @Query("SELECT eg FROM EntityGroup eg WHERE eg.groupName = :groupName AND eg.isActive = true AND eg.isDeleted = false")
    Optional<EntityGroup> findActiveByGroupName(@Param("groupName") String groupName);

    @Query("SELECT eg FROM EntityGroup eg WHERE eg.groupType = :groupType AND eg.isActive = true AND eg.isDeleted = false")
    List<EntityGroup> findAllActiveByGroupType(@Param("groupType") String groupType);

    boolean existsByGroupName(String groupName);
}