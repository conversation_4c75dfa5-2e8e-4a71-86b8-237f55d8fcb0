package com.scube.record.infrastructure.httpclient;

import com.scube.record.features.record.fee.Fee;
import com.scube.record.features.record.fee.GetFeesQuery;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Calculation Service HTTP Client for Record Service
 * Communicates with Calculation Service via RabbitMQ for fee queries
 */
@Service
@RequiredArgsConstructor
public class CalculationService {
    private final AmqpGateway amqpGateway;

    @Cacheable(value = "getFeeByKey", key = "#key", unless = "#result == null")
    public Fee getFeeByKey(String key) {
        var queryResponse = amqpGateway.queryResult(new GetFeesQuery(List.of(key)))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to get fee by key: " + key));
        return Optional.ofNullable(queryResponse.getFees())
                .filter(f -> !ObjectUtils.isEmpty(f))
                .map(List::getFirst)
                .orElse(null);
    }
}
