package com.scube.record.permission;

public class Permissions {

    public static class Record {
        public static final String CREATE_RECORD = "RECORD:CREATE";
        public static final String GET_RECORD_BY_UUID = "RECORD:GET_BY_UUID";
        public static final String UPDATE_RECORD = "RECORD:UPDATE";
        public static final String DELETE_RECORD = "RECORD:DELETE";
        public static final String GET_ALL_RECORDS = "RECORD:GET_ALL";
        public static final String GET_RECORD_BY_TYPE = "RECORD:GET_BY_TYPE";
        public static final String GET_RECORD_BY_STATUS = "RECORD:GET_BY_STATUS";
        public static final String SEARCH_RECORDS = "RECORD:SEARCH";
        public static final String MANAGE_FEES = "RECORD:MANAGE_FEES";
    }

    public static class LoggedInUserRecord {
        public static final String CREATE_RECORD = "LOGGED_IN_USER_RECORD:CREATE";
        public static final String GET_RECORD_BY_UUID = "LOGGED_IN_USER_RECORD:GET_BY_UUID";
        public static final String GET_RECORD_FEE = "LOGGED_IN_USER_RECORD:GET_BY_UUID";
        public static final String GET_RECORD_STATUS = "LOGGED_IN_USER_RECORD:GET_BY_STATUS";
        public static final String GET_RECORD_TYPECODE = "LOGGED_IN_USER_RECORD:GET_BY_TYPECODE";
        public static final String UPDATE_RECORD_BY_UUID = "LOGGED_IN_USER_RECORD:UPDATE_BY_UUID";


    }





    public static class LoggedInUserProfile {
        public static final String GET_PROFILE = "LOGGED_IN_USER_PROFILE:GET_PROFILE";
        public static final String GET_REJECTED_FIELDS = "LOGGED_IN_USER_PROFILE:GET_REJECTED_FIELDS";
        public static final String GET_PROFILE_BY_UUID = "LOGGED_IN_USER_PROFILE:GET_PROFILE_BY_UUID";
        public static final String GET_LICENSE_BY_ID = "LOGGED_IN_USER_PROFILE:GET_LICENSE_BY_ID";
        public static final String UPDATE_REJECT_FIELD = "LOGGED_IN_USER_PROFILE:UPDATE_REJECT_FIELD";
        public static final String GET_PROFILE_DETAIL_BY_UUID = "LOGGED_IN_USER_PROFILE:GET_PROFILE_DETAIL_BY_UUID";
    }

    public static class LoggedInUserEntityFee {
        
        public static final String GET_ENTITY_FEE_BY_ID = "LOGGED_IN_USER_ENTITY_FEE:GET_BY_ID";
        public static final String UPDATE_PAYMENT_STATUS = "LOGGED_IN_USER_ENTITY_FEE:UPDATE_PAYMENT_STATUS";
        public static final String GET_FEES_BY_ORDER_ID = "LOGGED_IN_USER_ENTITY_FEE:GET_BY_ORDER_ID";
        public static final String GET_FEES_BY_NAME = "LOGGED_IN_USER_ENTITY_FEE:GET_BY_NAME";
 
    }

   

    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "PERMISSION:SEED_ROLES_TO_ALL_REALMS";
        public static final String SEED_ROLES_BY_REALM = "PERMISSION:SEED_ROLES_BY_REALM";
    }
}