package com.scube.record.utils.code_generator;

import java.security.SecureRandom;

public class CodeGeneratorUtil {

    private static final SecureRandom random = new SecureRandom();

    public static String generateCode(CGOptions option, int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be positive");
        }

        String characters = option.getCharacters();
        StringBuilder code = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            code.append(characters.charAt(random.nextInt(characters.length())));
        }

        return code.toString();
    }

    public static String generateCode(CGOptions option) {
        return generateCode(option, 8);
    }

    public static String generateNumericCode(int length) {
        return generateCode(CGOptions.NUMERIC, length);
    }

    public static String generateAlphanumericCode(int length) {
        return generateCode(CGOptions.ALPHANUMERIC, length);
    }

    public static String generateAlphabeticCode(int length) {
        return generateCode(CGOptions.ALPHABETIC, length);
    }
}