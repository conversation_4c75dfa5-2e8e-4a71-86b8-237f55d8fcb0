package com.scube.record.utils.code_generator;

public enum CGOptions {
    ALPHANUMERIC("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"),
    NUMERIC("0123456789"),
    ALPHABETIC("ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
    LOWERCASE_ALPHANUMERIC("abcdefghijklmnopqrstuvwxyz0123456789"),
    LOWERCASE_ALPHABETIC("abcdefghijklmnopqrstuvwxyz"),
    MIXED_CASE_ALPHANUMERIC("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"),
    MIXED_CASE_ALPHABETIC("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz");

    private final String characters;

    CGOptions(String characters) {
        this.characters = characters;
    }

    public String getCharacters() {
        return characters;
    }
}