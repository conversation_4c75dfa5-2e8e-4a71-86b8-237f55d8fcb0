package com.scube.record.features.record.fee.rules;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeeRule {
    
    @JsonProperty("feeCode")
    private String feeCode;
    
    @JsonProperty("feeName")
    private String feeName;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("amount")
    private Object amount;
    
    @JsonProperty("condition")
    private String condition;
    
    public BigDecimal getAmountAsDecimal() {
        if (amount instanceof Number) {
            return BigDecimal.valueOf(((Number) amount).doubleValue());
        }
        return null;
    }
    
    public String getAmountAsExpression() {
        if (amount instanceof String) {
            return (String) amount;
        }
        return null;
    }
    
    public boolean isAmountExpression() {
        return amount instanceof String;
    }
}

