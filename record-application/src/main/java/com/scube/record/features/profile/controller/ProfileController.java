package com.scube.record.features.profile.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.record.features.profile.dto.ProfileHeaderResponse;
import com.scube.record.features.profile.dto.ProfileRequest;
import com.scube.record.features.profile.dto.ProfileResponse;
import com.scube.record.features.profile.service.ProfileService;
import com.scube.record.features.profile.service.RejectedFieldsService;
import com.scube.record.permission.Permissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/profiles")
@RequiredArgsConstructor
@Validated
@Slf4j
@Tag(name = "Profile Management", description = "Record profile operations with associations and relationships")
public class ProfileController {

    private final ProfileService profileService;
    private final RejectedFieldsService rejectedFieldsService;

    @GetMapping("/{recordUuid}")
    @RolesAllowed(Permissions.Record.GET_RECORD_BY_UUID)
    @Operation(summary = "Get record profile by UUID", description = "Get detailed profile of a record by its UUID")
    public ResponseEntity<ProfileResponse> getProfileByUuid(
            @Parameter(description = "Record UUID") @PathVariable String recordUuid,
            @Parameter(description = "Include associations") @RequestParam(defaultValue = "true") boolean includeAssociations,
            @Parameter(description = "Include related records") @RequestParam(defaultValue = "false") boolean includeRelatedRecords,
            @Parameter(description = "Maximum relation depth") @RequestParam(defaultValue = "1") int maxRelationDepth) {

        ProfileRequest request = new ProfileRequest();
        request.setRecordUuid(recordUuid);
        request.setIncludeAssociations(includeAssociations);
        request.setIncludeRelatedRecords(includeRelatedRecords);
        request.setMaxRelationDepth(maxRelationDepth);

        ProfileResponse response = profileService.getProfile(request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/detail/{recordUuid}")
    @RolesAllowed(Permissions.Record.GET_RECORD_BY_UUID)
    @Operation(summary = "Get record profile by UUID with JSON response",
            description = "Get detailed profile of any record type by UUID with associations as JSON")
    public JsonNode getRecordProfileDetailByUuid(
            @Parameter(description = "Record UUID", example = "550e8400-e29b-41d4-a716-************")
            @PathVariable @NotNull UUID recordUuid) {

        log.info("Getting record profile detail for UUID: {}", recordUuid);
        return profileService.getRecordProfileAndAssociationsAsJsonByUuid(recordUuid);
    }


    @PatchMapping("/{recordUuid}/reject-fields")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "Add fields to rejected list",
            description = "Mark one or more fields as rejected for a record using UUID")
    public void addToRejectedFieldList(
            @PathVariable @NotNull UUID recordUuid,
            @RequestBody RejectFieldRequest request) {

        log.info("Adding rejected fields for UUID: {}, fields: {}", recordUuid, request.fields());
        rejectedFieldsService.addToRejectedFieldListByUuid(recordUuid, request.fields(), false);
    }

    @DeleteMapping("/{recordUuid}/reject-fields")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "Remove fields from rejected list")
    public void removeFromRejectedFieldList(
            @PathVariable @NotNull UUID recordUuid,
            @RequestParam("field") Set<String> fields) {

        log.info("Removing rejected fields for UUID: {}, fields: {}", recordUuid, fields);
        rejectedFieldsService.removeFromRejectedFieldListByUuid(recordUuid, fields, false);
    }

    @GetMapping("/{recordUuid}/reject-fields")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get rejected fields")
    public RejectFieldResponse getRejectedFieldList(
            @PathVariable @NotNull UUID recordUuid) {

        var fields = rejectedFieldsService.getRejectedFieldListByUuid(recordUuid);
        return new RejectFieldResponse(fields);
    }

    public record RejectFieldRequest(Set<String> fields) {}
    public record RejectFieldResponse(Set<String> fields) {}
}