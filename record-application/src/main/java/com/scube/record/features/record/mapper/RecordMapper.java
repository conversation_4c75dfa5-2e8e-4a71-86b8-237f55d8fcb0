package com.scube.record.features.record.mapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.dto.UpdateRecordRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE
)
public abstract class RecordMapper {

    @Autowired
    protected ObjectMapper objectMapper;

    @Mapping(source = "createdBy", target = "lastModifiedBy")
    @Mapping(target = "properties", ignore = true)
    public abstract Record toEntity(CreateRecordRequest request);

    @Mapping(source = "recordType.typeCode", target = "recordTypeCode")
    @Mapping(source = "recordType.typeName", target = "recordTypeName")
    @Mapping(source = "properties", target = "properties", qualifiedByName = "mapToJsonNode")
    public abstract RecordResponse toResponse(Record record);

    @Mapping(target = "properties", ignore = true)
    public abstract void updateEntity(@MappingTarget Record record, UpdateRecordRequest request);

    @Named("mapToJsonNode")
    protected JsonNode mapToJsonNode(Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        return objectMapper.valueToTree(map);
    }
}