package com.scube.record.features.user_registration;

import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
public class UserRegistrationEventHandler extends FanoutListener<UserRegistrationEvent> {

    private final UserRegistrationService userRegistrationService;

    public UserRegistrationEventHandler(UserRegistrationService userRegistrationService) {
        this.userRegistrationService = userRegistrationService;
        log.warn("========================================");
        log.warn("UserRegistrationEventHandler INITIALIZED");
        log.warn("Event type: {}", UserRegistrationEvent.class.getSimpleName());
        log.warn("Handler class: {}", this.getClass().getSimpleName());
        log.warn("========================================");
    }

    @Override
    public void consume(UserRegistrationEvent event) {
        log.warn("========================================");
        log.warn("=== UserRegistrationEventHandler.consume() CALLED ===");
        log.warn("Event: {}", event);
        log.warn("========================================");
        try {
            userRegistrationService.registerUser(event);
            log.info("=== Successfully processed user registration event for: {} ===", event.getEmail());
        } catch (Exception e) {
            log.error("========================================");
            log.error("Error processing user registration event: {}", event, e);
            log.error("========================================");
        }
    }
}