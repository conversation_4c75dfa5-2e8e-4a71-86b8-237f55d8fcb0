package com.scube.record.features.module.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateModuleRequest {

    @NotBlank(message = "Module code is required")
    private String moduleCode;

    @NotBlank(message = "Module name is required")
    private String moduleName;

    private String description;

    private String version;

    @NotNull(message = "Module configuration is required")
    private JsonNode config;

    private Boolean isActive = true;
}

