package com.scube.record.features.entity_note.controller;

import com.scube.record.features.entity_note.dto.EntityNoteDto;
import com.scube.record.features.entity_note.dto.EntityNoteRequest;
import com.scube.record.features.entity_note.service.EntityNoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/entity-notes")
@RequiredArgsConstructor
public class EntityNoteController {

    private final EntityNoteService entityNoteService;

    @PostMapping
    public ResponseEntity<EntityNoteDto> createEntityNote(
        @Valid @RequestBody EntityNoteRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityNoteDto created = entityNoteService.createEntityNote(request, userId);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @GetMapping("/{entityNoteId}")
    public ResponseEntity<EntityNoteDto> getEntityNoteById(@PathVariable Long entityNoteId) {
        return entityNoteService.findById(entityNoteId)
            .map(entityNote -> ResponseEntity.ok(entityNote))
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{entityNoteId}")
    public ResponseEntity<EntityNoteDto> updateEntityNote(
        @PathVariable Long entityNoteId,
        @Valid @RequestBody EntityNoteRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityNoteDto updated = entityNoteService.updateEntityNote(entityNoteId, request, userId);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{entityNoteId}")
    public ResponseEntity<Void> deleteEntityNote(@PathVariable Long entityNoteId) {
        entityNoteService.deleteEntityNote(entityNoteId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    public ResponseEntity<Page<EntityNoteDto>> getAllEntityNotes(Pageable pageable) {
        Page<EntityNoteDto> entityNotes = entityNoteService.findAllEntityNotes(pageable);
        return ResponseEntity.ok(entityNotes);
    }

    @GetMapping("/type/{noteType}")
    public ResponseEntity<List<EntityNoteDto>> getEntityNotesByType(@PathVariable String noteType) {
        List<EntityNoteDto> entityNotes = entityNoteService.findByNoteType(noteType);
        return ResponseEntity.ok(entityNotes);
    }

    @GetMapping("/public")
    public ResponseEntity<List<EntityNoteDto>> getPublicNotes() {
        List<EntityNoteDto> entityNotes = entityNoteService.findPublicNotes();
        return ResponseEntity.ok(entityNotes);
    }

    @GetMapping("/pinned")
    public ResponseEntity<List<EntityNoteDto>> getPinnedNotes() {
        List<EntityNoteDto> entityNotes = entityNoteService.findPinnedNotes();
        return ResponseEntity.ok(entityNotes);
    }

    @GetMapping("/my-notes")
    public ResponseEntity<List<EntityNoteDto>> getMyNotes(
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {
        List<EntityNoteDto> entityNotes = entityNoteService.findByCreatedBy(userId);
        return ResponseEntity.ok(entityNotes);
    }

    @GetMapping("/search")
    public ResponseEntity<List<EntityNoteDto>> searchNotes(@RequestParam String keyword) {
        List<EntityNoteDto> entityNotes = entityNoteService.searchByKeyword(keyword);
        return ResponseEntity.ok(entityNotes);
    }
}