package com.scube.record.features.code_lookup.service;

import com.scube.record.features.code_lookup.dto.CodeLookupRequest;
import com.scube.record.features.code_lookup.dto.CodeLookupResponse;
import com.scube.record.features.code_lookup.dto.CodeLookupResponseLineItem;
import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookup;
import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookupBuilder;
import com.scube.record.infrastructure.db.repository.code_lookup.CodeLookupRepository;
import com.scube.record.utils.code_generator.CGOptions;
import com.scube.record.utils.code_generator.CodeGeneratorUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CodeLookupService {

    private final CodeLookupRepository codeLookupRepository;

    public CodeLookupResponse getCodesByEntity(String entityType, String entityId, String realm) {
        List<CodeLookup> codeLookups = codeLookupRepository.findActiveByEntityTypeAndEntityIdAndRealm(
            entityType, entityId, realm);

        List<CodeLookupResponseLineItem> responseItems = codeLookups.stream()
            .map(this::convertToResponseLineItem)
            .collect(Collectors.toList());

        return new CodeLookupResponse(responseItems);
    }

    public CodeLookupResponse createOrLookupCode(CodeLookupRequest request, String realm) {
        Optional<CodeLookup> existingCode = codeLookupRepository.findByEntityTypeAndEntityIdAndRealmAndAction(
            request.getEntityType(), request.getEntityId(), realm, request.getAction());

        if (existingCode.isPresent()) {
            CodeLookupResponseLineItem item = convertToResponseLineItem(existingCode.get());
            return new CodeLookupResponse(List.of(item));
        }

        CodeLookup newCode = createCodeLookup(request, realm);
        CodeLookupResponseLineItem item = convertToResponseLineItem(newCode);
        return new CodeLookupResponse(List.of(item));
    }

    @Retryable(value = DataIntegrityViolationException.class, maxAttempts = 3, backoff = @Backoff(delay = 100))
    public CodeLookup createCodeLookup(CodeLookupRequest request, String realm) {
        CodeLookup codeLookup = new CodeLookup();
        codeLookup.setCode(generateUniqueCode());
        codeLookup.setEntityType(request.getEntityType());
        codeLookup.setEntityId(request.getEntityId());
        codeLookup.setRealm(realm);
        codeLookup.setAction(request.getAction());

        try {
            return codeLookupRepository.save(codeLookup);
        } catch (DataIntegrityViolationException e) {
            log.warn("Code collision detected, retrying with new code");
            throw e;
        }
    }

    public CodeLookup createRecordCode(String entityId, String realm) {
        CodeLookup codeLookup = CodeLookupBuilder.buildRecordCode(entityId, realm);
        return saveWithRetry(codeLookup);
    }

    public CodeLookup createIndividualCode(String entityId, String realm) {
        CodeLookup codeLookup = CodeLookupBuilder.buildIndividualCode(entityId, realm);
        return saveWithRetry(codeLookup);
    }

    public CodeLookup createTagCode(String entityId, String realm, CodeLookupActionEnum action) {
        CodeLookup codeLookup = CodeLookupBuilder.buildTagCode(entityId, realm, action);
        return saveWithRetry(codeLookup);
    }

    public CodeLookup createPublicPageCode(String pageType, String realm) {
        CodeLookup codeLookup = CodeLookupBuilder.buildPublicPageCode(pageType, realm);
        return saveWithRetry(codeLookup);
    }

    @Retryable(value = DataIntegrityViolationException.class, maxAttempts = 3, backoff = @Backoff(delay = 100))
    private CodeLookup saveWithRetry(CodeLookup codeLookup) {
        try {
            codeLookup.setCode(generateUniqueCode());
            return codeLookupRepository.save(codeLookup);
        } catch (DataIntegrityViolationException e) {
            log.warn("Code collision detected, retrying with new code");
            throw e;
        }
    }

    @Transactional(readOnly = true)
    public Optional<CodeLookup> findByCode(String code) {
        return codeLookupRepository.findByCode(code);
    }

    @Transactional(readOnly = true)
    public List<CodeLookup> findByEntityTypeAndRealm(String entityType, String realm) {
        return codeLookupRepository.findByEntityTypeAndRealm(entityType, realm);
    }

    @Transactional(readOnly = true)
    public List<CodeLookup> findByRealm(String realm) {
        return codeLookupRepository.findByRealm(realm);
    }

    public void deleteCodeLookup(String code) {
        CodeLookup codeLookup = codeLookupRepository.findByCode(code)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Code lookup not found with code: " + code));

        codeLookupRepository.delete(codeLookup);
    }

    private String generateUniqueCode() {
        String code;
        int attempts = 0;
        do {
            code = CodeGeneratorUtil.generateCode(CGOptions.ALPHANUMERIC, 12);
            attempts++;
            if (attempts > 10) {
                throw new RuntimeException("Unable to generate unique code after 10 attempts");
            }
        } while (codeLookupRepository.existsByCode(code));

        return code;
    }

    private CodeLookupResponseLineItem convertToResponseLineItem(CodeLookup codeLookup) {
        return new CodeLookupResponseLineItem(
            codeLookup.getCode(),
            codeLookup.getAction(),
            codeLookup.getEntityType(),
            codeLookup.getEntityId(),
            codeLookup.getRealm()
        );
    }
}