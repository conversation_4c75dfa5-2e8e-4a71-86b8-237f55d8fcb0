package com.scube.record.features.association.service;

import com.scube.record.features.association.dto.AssociationResponse;
import com.scube.record.features.association.dto.AssociationSearchRequest;
import com.scube.record.features.association.dto.CreateAssociationRequest;
import com.scube.record.features.association.dto.UpdateAssociationRequest;
import com.scube.record.features.association.exception.AssociationNotFoundException;
import com.scube.record.features.association.exception.DuplicateAssociationException;
import com.scube.record.features.association.mapper.AssociationMapper;
import com.scube.record.infrastructure.db.entity.record.Association;
import com.scube.record.infrastructure.db.repository.record.AssociationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssociationService {

    private final AssociationRepository associationRepository;
    private final AssociationMapper associationMapper;

    public AssociationResponse createAssociation(CreateAssociationRequest request) {
        boolean exists = associationRepository.existsByParentIdAndChildIdAndAssociationTypeId(
                request.getParentId(), request.getChildId(), request.getAssociationTypeId());

        if (exists) {
            throw new DuplicateAssociationException(
                "Association already exists between parent " + request.getParentId() +
                " and child " + request.getChildId() + " with type " + request.getAssociationTypeId());
        }

        Association association = associationMapper.toEntity(request);
        Association savedAssociation = associationRepository.save(association);

        log.info("Created association: {} [{}] -> {} [{}] with type {}",
                savedAssociation.getParentId(), savedAssociation.getParentType(),
                savedAssociation.getChildId(), savedAssociation.getChildType(),
                savedAssociation.getAssociationTypeId());

        return associationMapper.toResponse(savedAssociation);
    }

    public AssociationResponse getAssociationById(Long id) {
        Association association = associationRepository.findById(id)
                .orElseThrow(() -> new AssociationNotFoundException(
                        "Association not found with ID: " + id));
        return associationMapper.toResponse(association);
    }

    public AssociationResponse getAssociationByUuid(String uuid) {
        Association association = associationRepository.findByAssociationUuid(UUID.fromString(uuid))
                .orElseThrow(() -> new AssociationNotFoundException(
                        "Association not found with UUID: " + uuid));
        return associationMapper.toResponse(association);
    }

    public List<AssociationResponse> getAssociationsByParent(Long parentId, String parentType) {
        List<Association> associations = associationRepository.findByParentIdAndParentType(parentId, parentType);
        return associations.stream()
                .map(associationMapper::toResponse)
                .collect(Collectors.toList());
    }

    public List<AssociationResponse> getAssociationsByChild(Long childId, String childType) {
        List<Association> associations = associationRepository.findByChildIdAndChildType(childId, childType);
        return associations.stream()
                .map(associationMapper::toResponse)
                .collect(Collectors.toList());
    }

    public List<AssociationResponse> getAssociationsByType(String associationTypeId) {
        List<Association> associations = associationRepository.findByAssociationTypeId(associationTypeId);
        return associations.stream()
                .map(associationMapper::toResponse)
                .collect(Collectors.toList());
    }

    public AssociationResponse updateAssociation(Long id, UpdateAssociationRequest request) {
        Association association = associationRepository.findById(id)
                .orElseThrow(() -> new AssociationNotFoundException(
                        "Association not found with ID: " + id));

        associationMapper.updateEntity(association, request);
        Association updatedAssociation = associationRepository.save(association);
        return associationMapper.toResponse(updatedAssociation);
    }

    public AssociationResponse updateAssociationByUuid(String uuid, UpdateAssociationRequest request) {
        Association association = associationRepository.findByAssociationUuid(UUID.fromString(uuid))
                .orElseThrow(() -> new AssociationNotFoundException(
                        "Association not found with UUID: " + uuid));

        associationMapper.updateEntity(association, request);
        Association updatedAssociation = associationRepository.save(association);
        return associationMapper.toResponse(updatedAssociation);
    }

    public void deleteAssociation(Long id) {
        Association association = associationRepository.findById(id)
                .orElseThrow(() -> new AssociationNotFoundException(
                        "Association not found with ID: " + id));
        associationRepository.delete(association);

        log.info("Deleted association with ID: {}", id);
    }

    public void deleteAssociationByUuid(String uuid) {
        Association association = associationRepository.findByAssociationUuid(UUID.fromString(uuid))
                .orElseThrow(() -> new AssociationNotFoundException(
                        "Association not found with UUID: " + uuid));
        associationRepository.delete(association);

        log.info("Deleted association with UUID: {}", uuid);
    }

    public void deleteAssociationsBetween(Long parentId, Long childId) {
        List<Association> associations = associationRepository.findByParentIdAndChildId(parentId, childId);
        if (!associations.isEmpty()) {
            associationRepository.deleteAll(associations);
            log.info("Deleted {} associations between parent {} and child {}",
                    associations.size(), parentId, childId);
        }
    }

    public boolean associationExists(Long parentId, Long childId, String associationTypeId) {
        return associationRepository.existsByParentIdAndChildIdAndAssociationTypeId(
                parentId, childId, associationTypeId);
    }

    public Page<AssociationResponse> searchAssociations(AssociationSearchRequest searchRequest) {
        List<Association> associations;
        Pageable pageable = PageRequest.of(
                searchRequest.getPage() != null ? searchRequest.getPage() : 0,
                searchRequest.getSize() != null ? searchRequest.getSize() : 20,
                searchRequest.getSortDirection() != null && searchRequest.getSortDirection().equalsIgnoreCase("DESC") ?
                        Sort.by(searchRequest.getSortBy() != null ? searchRequest.getSortBy() : "createdAt").descending() :
                        Sort.by(searchRequest.getSortBy() != null ? searchRequest.getSortBy() : "createdAt").ascending()
        );

        if (searchRequest.getParentId() != null && searchRequest.getChildId() != null) {
            associations = associationRepository.findByParentIdAndChildId(
                    searchRequest.getParentId(), searchRequest.getChildId());
        } else if (searchRequest.getParentId() != null && searchRequest.getParentType() != null) {
            associations = associationRepository.findByParentIdAndParentType(
                    searchRequest.getParentId(), searchRequest.getParentType());
        } else if (searchRequest.getChildId() != null && searchRequest.getChildType() != null) {
            associations = associationRepository.findByChildIdAndChildType(
                    searchRequest.getChildId(), searchRequest.getChildType());
        } else if (searchRequest.getParentId() != null) {
            associations = associationRepository.findByParentId(searchRequest.getParentId());
        } else if (searchRequest.getChildId() != null) {
            associations = associationRepository.findByChildId(searchRequest.getChildId());
        } else if (searchRequest.getAssociationTypeId() != null) {
            associations = associationRepository.findByAssociationTypeId(searchRequest.getAssociationTypeId());
        } else {
            associations = associationRepository.findAll();
        }

        int start = Math.min(pageable.getPageNumber() * pageable.getPageSize(), associations.size());
        int end = Math.min(start + pageable.getPageSize(), associations.size());
        List<Association> paginatedAssociations = start < end ? associations.subList(start, end) : List.of();

        List<AssociationResponse> responses = paginatedAssociations.stream()
                .map(associationMapper::toResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(responses, pageable, associations.size());
    }

    public Page<AssociationResponse> getAllAssociations(Pageable pageable) {
        Page<Association> associations = associationRepository.findAll(pageable);
        List<AssociationResponse> responses = associations.getContent().stream()
                .map(associationMapper::toResponse)
                .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, associations.getTotalElements());
    }

    public long countAssociations() {
        return associationRepository.count();
    }

    public long countAssociationsByType(String associationTypeId) {
        return associationRepository.findByAssociationTypeId(associationTypeId).size();
    }

    public long countAssociationsByParent(Long parentId) {
        return associationRepository.findByParentId(parentId).size();
    }

    public long countAssociationsByChild(Long childId) {
        return associationRepository.findByChildId(childId).size();
    }
}