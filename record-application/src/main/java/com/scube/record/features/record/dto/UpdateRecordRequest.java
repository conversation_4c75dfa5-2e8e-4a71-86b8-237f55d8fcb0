package com.scube.record.features.record.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRecordRequest {

    private String recordName;

    private String status;

    private JsonNode properties;

    @NotBlank(message = "Last modified by is required")
    private String lastModifiedBy;
}