package com.scube.record.features.document.controller;

import com.scube.record.features.document.dto.CreateDocumentRequest;
import com.scube.record.features.document.dto.DocumentDto;
import com.scube.record.features.document.dto.UpdateDocumentRequest;
import com.scube.record.features.document.service.DocumentService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.UUID;

@RestController
@RequestMapping("/documents")
@RequiredArgsConstructor
public class DocumentController {

    private final DocumentService documentService;

    @PostMapping
    public ResponseEntity<DocumentDto> createDocument(
        @Valid @RequestBody CreateDocumentRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        DocumentDto created = documentService.createDocument(request, userId);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @GetMapping("/{documentUuid}")
    public ResponseEntity<DocumentDto> getDocumentByUuid(@PathVariable UUID documentUuid) {
        return documentService.findByDocumentUuid(documentUuid)
            .map(document -> ResponseEntity.ok(document))
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{documentUuid}")
    public ResponseEntity<DocumentDto> updateDocument(
        @PathVariable UUID documentUuid,
        @Valid @RequestBody UpdateDocumentRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        DocumentDto updated = documentService.updateDocument(documentUuid, request, userId);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{documentUuid}")
    public ResponseEntity<Void> softDeleteDocument(
        @PathVariable UUID documentUuid,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        documentService.softDeleteDocument(documentUuid, userId);
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/{documentUuid}/hard")
    public ResponseEntity<Void> hardDeleteDocument(@PathVariable UUID documentUuid) {
        documentService.hardDeleteDocument(documentUuid);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    public ResponseEntity<Page<DocumentDto>> getAllDocuments(Pageable pageable) {
        Page<DocumentDto> documents = documentService.findAllDocuments(pageable);
        return ResponseEntity.ok(documents);
    }

    @GetMapping("/created-by/{createdBy}")
    public ResponseEntity<Page<DocumentDto>> getDocumentsByCreatedBy(
        @PathVariable String createdBy,
        Pageable pageable) {

        Page<DocumentDto> documents = documentService.findDocumentsByCreatedBy(createdBy, pageable);
        return ResponseEntity.ok(documents);
    }

    @GetMapping("/document-type/{documentTypeKey}")
    public ResponseEntity<Page<DocumentDto>> getDocumentsByDocumentType(
        @PathVariable String documentTypeKey,
        Pageable pageable) {

        Page<DocumentDto> documents = documentService.findDocumentsByDocumentType(documentTypeKey, pageable);
        return ResponseEntity.ok(documents);
    }
}