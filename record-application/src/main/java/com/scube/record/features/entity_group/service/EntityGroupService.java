package com.scube.record.features.entity_group.service;

import com.scube.record.features.entity_group.dto.EntityGroupDto;
import com.scube.record.features.entity_group.dto.EntityGroupRequest;
import com.scube.record.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.record.infrastructure.db.repository.entity_group.EntityGroupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class EntityGroupService {

    private final EntityGroupRepository entityGroupRepository;

    public EntityGroupDto createEntityGroup(EntityGroupRequest request, String createdBy) {
        if (entityGroupRepository.existsByGroupName(request.getGroupName())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT,
                "Entity group already exists with name: " + request.getGroupName());
        }

        EntityGroup entityGroup = new EntityGroup();
        mapRequestToEntity(request, entityGroup);
        entityGroup.setCreatedBy(createdBy);
        entityGroup.setLastModifiedBy(createdBy);

        EntityGroup savedEntityGroup = entityGroupRepository.save(entityGroup);
        return convertToDto(savedEntityGroup);
    }

    public EntityGroupDto updateEntityGroup(Long entityGroupId, EntityGroupRequest request, String modifiedBy) {
        EntityGroup entityGroup = entityGroupRepository.findById(entityGroupId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Entity group not found with ID: " + entityGroupId));

        if (StringUtils.hasText(request.getGroupName())) {
            entityGroup.setGroupName(request.getGroupName());
        }
        if (StringUtils.hasText(request.getGroupType())) {
            entityGroup.setGroupType(request.getGroupType());
        }
        if (StringUtils.hasText(request.getDescription())) {
            entityGroup.setDescription(request.getDescription());
        }
        if (request.getIsActive() != null) {
            entityGroup.setIsActive(request.getIsActive());
        }
        if (request.getCustomFields() != null) {
            entityGroup.setCustomFields(request.getCustomFields());
        }
        if (request.getProperties() != null) {
            entityGroup.setProperties(request.getProperties());
        }
        entityGroup.setLastModifiedBy(modifiedBy);

        EntityGroup updatedEntityGroup = entityGroupRepository.save(entityGroup);
        return convertToDto(updatedEntityGroup);
    }

    @Transactional(readOnly = true)
    public Optional<EntityGroupDto> findById(Long entityGroupId) {
        return entityGroupRepository.findById(entityGroupId)
            .map(this::convertToDto);
    }

    @Transactional(readOnly = true)
    public Optional<EntityGroupDto> findByGroupName(String groupName) {
        return entityGroupRepository.findActiveByGroupName(groupName)
            .map(this::convertToDto);
    }

    @Transactional(readOnly = true)
    public Page<EntityGroupDto> findAllActiveEntityGroups(Pageable pageable) {
        List<EntityGroup> entityGroups = entityGroupRepository.findAllActive();
        List<EntityGroupDto> entityGroupDtos = entityGroups.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), entityGroupDtos.size());

        return new PageImpl<>(
            entityGroupDtos.subList(start, end),
            pageable,
            entityGroupDtos.size()
        );
    }

    @Transactional(readOnly = true)
    public List<EntityGroupDto> findByGroupType(String groupType) {
        List<EntityGroup> entityGroups = entityGroupRepository.findAllActiveByGroupType(groupType);
        return entityGroups.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    public void deleteEntityGroup(Long entityGroupId) {
        EntityGroup entityGroup = entityGroupRepository.findById(entityGroupId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Entity group not found with ID: " + entityGroupId));

        entityGroup.setIsActive(false);
        entityGroupRepository.save(entityGroup);
    }

    private void mapRequestToEntity(EntityGroupRequest request, EntityGroup entityGroup) {
        entityGroup.setGroupName(request.getGroupName());
        entityGroup.setGroupType(request.getGroupType());
        entityGroup.setDescription(request.getDescription());
        entityGroup.setIsActive(request.getIsActive() != null ? request.getIsActive() : true);
        entityGroup.setCustomFields(request.getCustomFields());
        entityGroup.setProperties(request.getProperties());
    }

    private EntityGroupDto convertToDto(EntityGroup entityGroup) {
        EntityGroupDto dto = new EntityGroupDto();
        dto.setEntityGroupId(entityGroup.getEntityGroupId());
        dto.setGroupName(entityGroup.getGroupName());
        dto.setGroupType(entityGroup.getGroupType());
        dto.setDescription(entityGroup.getDescription());
        dto.setIsActive(entityGroup.getIsActive());
        dto.setCustomFields(entityGroup.getCustomFields());
        dto.setProperties(entityGroup.getProperties());

        dto.setCreatedAt(entityGroup.getCreatedAt());
        dto.setCreatedBy(entityGroup.getCreatedBy());
        dto.setLastModifiedAt(entityGroup.getLastModifiedAt());
        dto.setLastModifiedBy(entityGroup.getLastModifiedBy());
        dto.setIsDeleted(entityGroup.getIsDeleted());

        return dto;
    }
}