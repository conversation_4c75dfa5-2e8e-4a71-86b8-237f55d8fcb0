package com.scube.record.features.search.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.record.features.search.dto.*;
import com.scube.record.infrastructure.db.entity.record.Association;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import com.scube.record.infrastructure.db.repository.record.AssociationRepository;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SearchService {

    private final RecordRepository recordRepository;
    private final RecordTypeRepository recordTypeRepository;
    private final AssociationRepository associationRepository;
    private final ObjectMapper objectMapper;

    public Page<SearchResult> advancedSearch(AdvancedSearchRequest request) {
        Specification<Record> spec = buildAdvancedSearchSpecification(request);

        Sort sort = Sort.by(
                "ASC".equalsIgnoreCase(request.getSortDirection())
                    ? Sort.Direction.ASC
                    : Sort.Direction.DESC,
                request.getSortBy()
        );

        Pageable pageable = PageRequest.of(request.getPage(), request.getSize(), sort);
        Page<Record> records = recordRepository.findAll(spec, pageable);

        List<SearchResult> searchResults = records.getContent().stream()
                .map(this::toSearchResult)
                .toList();

        return new PageImpl<>(searchResults, pageable, records.getTotalElements());
    }

    public List<SearchResult> globalSearchRecords(String query, boolean exactMatch, String recordTypeCode, int limit) {
        Specification<Record> spec = (root, queryBuilder, criteriaBuilder) -> {
            var predicates = criteriaBuilder.conjunction();

            if (query != null && !query.trim().isEmpty()) {
                if (exactMatch) {
                    var nameSearch = criteriaBuilder.equal(
                            criteriaBuilder.lower(root.get("recordName")), query.toLowerCase());
                    predicates = criteriaBuilder.and(predicates, nameSearch);
                } else {
                    String searchTerm = "%" + query.toLowerCase() + "%";
                    var nameSearch = criteriaBuilder.like(
                            criteriaBuilder.lower(root.get("recordName")), searchTerm);
                    predicates = criteriaBuilder.and(predicates, nameSearch);
                }
            }

            if (recordTypeCode != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.equal(root.get("recordType").get("typeCode"), recordTypeCode));
            }

            return predicates;
        };

        Pageable pageable = PageRequest.of(0, limit);
        Page<Record> records = recordRepository.findAll(spec, pageable);

        return records.getContent().stream()
                .map(this::toSearchResult)
                .collect(Collectors.toList());
    }

    private void findRelatedRecordsRecursive(
            Long recordId,
            List<String> associationTypes,
            String direction,
            int maxDepth,
            int currentDepth,
            Set<Long> relatedRecordIds,
            Set<Long> processedIds) {

        if (currentDepth >= maxDepth || processedIds.contains(recordId)) {
            return;
        }

        processedIds.add(recordId);

        List<Association> associations = new ArrayList<>();

        if ("BOTH".equals(direction) || "PARENT".equals(direction)) {
            if (associationTypes == null || associationTypes.isEmpty()) {
                associations.addAll(associationRepository.findByParentId(recordId));
            } else {
                associations.addAll(associationRepository.findByParentIdAndAssociationTypeIdIn(recordId, associationTypes));
            }
        }

        if ("BOTH".equals(direction) || "CHILD".equals(direction)) {
            if (associationTypes == null || associationTypes.isEmpty()) {
                associations.addAll(associationRepository.findByChildId(recordId));
            } else {
                associations.addAll(associationRepository.findByChildIdAndAssociationTypeIdIn(recordId, associationTypes));
            }
        }

        for (Association association : associations) {
            Long relatedId = null;
            if (association.getParentId().equals(recordId)) {
                relatedId = association.getChildId();
            } else if (association.getChildId().equals(recordId)) {
                relatedId = association.getParentId();
            }

            if (relatedId != null && !processedIds.contains(relatedId)) {
                relatedRecordIds.add(relatedId);
                if (currentDepth + 1 < maxDepth) {
                    findRelatedRecordsRecursive(
                            relatedId,
                            associationTypes,
                            direction,
                            maxDepth,
                            currentDepth + 1,
                            relatedRecordIds,
                            processedIds
                    );
                }
            }
        }
    }

    private Specification<Record> buildAdvancedSearchSpecification(AdvancedSearchRequest request) {
        return (root, query, criteriaBuilder) -> {
            var predicates = criteriaBuilder.conjunction();

            if (request.getRecordTypeCode() != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.equal(root.get("recordType").get("typeCode"), request.getRecordTypeCode()));
            }

            if (request.getStatuses() != null && !request.getStatuses().isEmpty()) {
                predicates = criteriaBuilder.and(predicates,
                        root.get("status").in(request.getStatuses()));
            }

            if (request.getCreatedAfter() != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), request.getCreatedAfter()));
            }

            if (request.getCreatedBefore() != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), request.getCreatedBefore()));
            }

            if (request.getModifiedAfter() != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.greaterThanOrEqualTo(root.get("lastModifiedAt"), request.getModifiedAfter()));
            }

            if (request.getModifiedBefore() != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.lessThanOrEqualTo(root.get("lastModifiedAt"), request.getModifiedBefore()));
            }

            if (request.getCreatedBy() != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.equal(root.get("createdBy"), request.getCreatedBy()));
            }

            if (request.getLastModifiedBy() != null) {
                predicates = criteriaBuilder.and(predicates,
                        criteriaBuilder.equal(root.get("lastModifiedBy"), request.getLastModifiedBy()));
            }

            return predicates;
        };
    }

    private SearchResult toSearchResult(Record record) {
        return new SearchResult(
                record.getRecordId(),
                record.getRecordUuid(),
                record.getRecordName(),
                record.getRecordType() != null ? record.getRecordType().getTypeCode() : null,
                record.getRecordType() != null ? record.getRecordType().getTypeName() : null,
                record.getStatus(),
                convertMapToJsonNode(record.getProperties()),
                record.getCreatedAt(),
                record.getCreatedBy(),
                1.0  // relevanceScore
        );
    }

    private com.fasterxml.jackson.databind.JsonNode convertMapToJsonNode(java.util.Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        return objectMapper.valueToTree(map);
    }
}
