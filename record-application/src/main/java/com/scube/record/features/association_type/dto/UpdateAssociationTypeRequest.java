package com.scube.record.features.association_type.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAssociationTypeRequest {

    private String associationName;

    private String description;

    private List<String> parentRecordTypes;

    private List<String> childRecordTypes;

    private JsonNode additionalProperties;

    @NotBlank(message = "Last modified by is required")
    private String lastModifiedBy;
}