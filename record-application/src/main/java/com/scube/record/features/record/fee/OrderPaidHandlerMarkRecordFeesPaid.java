package com.scube.record.features.record.fee;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.record.features.entity_fee.service.EntityFeeService;
import com.scube.record.features.record.service.RecordService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Slf4j
@Component
@AllArgsConstructor
public class OrderPaidHandlerMarkRecordFeesPaid extends FanoutListener<OrderPaidHandlerMarkRecordFeesPaid.OrderPaidEvent> {
    private final RecordService recordService;
    private final EntityFeeService entityFeeService;

    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void consume(OrderPaidEvent event) {
        if (ObjectUtils.isEmpty(event.orderInvoice().getItems())) {
            log.warn("OrderPaidEvent received with no items");
            throw new FeeInvoiceException();
        }

        log.info("Processing OrderPaidEvent for {} items", event.orderInvoice().getItems().size());

        entityFeeService.updateEntityFeeAsPaid(event.orderInvoice());

        var recordItems = event.orderInvoice().getItems().stream()
                .filter(i -> i.getItemType().equalsIgnoreCase("record"))
                .toList();

        log.info("Found {} record items to process", recordItems.size());

        for (var recordItem : recordItems) {
            processRecord(recordItem);
        }
    }

    private void processRecord(OrderInvoiceItem item) {
        log.info("Processing record payment for itemId: {}", item.getItemId());
        
        
        log.info("Record {} payment processed successfully", item.getItemId());
    }

    public record OrderPaidEvent(OrderInvoiceResponse orderInvoice) implements IRabbitFanoutSubscriber {
    }

    public static class FeeInvoiceException extends RuntimeException {
        public FeeInvoiceException() {
            super("Fee invoice not found");
        }
    }
}
