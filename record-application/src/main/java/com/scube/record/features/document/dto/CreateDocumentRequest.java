package com.scube.record.features.document.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateDocumentRequest {
    private String fileName;
    private String contentType;
    private Long fileSize;
    private String fileUrl;
    private UUID documentServiceUuid;
    private String documentTypeKey;
    private JsonNode properties;
}