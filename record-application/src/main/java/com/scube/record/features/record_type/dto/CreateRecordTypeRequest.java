package com.scube.record.features.record_type.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateRecordTypeRequest {

    @NotBlank(message = "Type code is required")
    private String typeCode;

    @NotBlank(message = "Type name is required")
    private String typeName;

    private String parentTypeCode;

    private String description;

    private JsonNode properties;

    private JsonNode configJson;

    @NotBlank(message = "Created by is required")
    private String createdBy;
}