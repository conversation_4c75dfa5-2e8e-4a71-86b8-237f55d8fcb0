package com.scube.record.features.association_type.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateAssociationTypeRequest {

    @NotBlank(message = "Association name is required")
    private String associationName;

    @NotBlank(message = "Type code is required")
    private String typeCode;

    private String description;

    @NotNull(message = "Parent record types are required")
    private List<String> parentRecordTypes;

    @NotNull(message = "Child record types are required")
    private List<String> childRecordTypes;

    private JsonNode additionalProperties;

    @NotBlank(message = "Created by is required")
    private String createdBy;
}