package com.scube.record.features.association.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAssociationRequest {

    private String associationTypeId;

    private JsonNode properties;

    @NotBlank(message = "Last modified by is required")
    private String lastModifiedBy;
}