package com.scube.record.features.entity_fee.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityFeeRequest {

    @NotNull(message = "Fee name is required")
    private String feeName;

    @NotNull(message = "Fee amount is required")
    @Positive(message = "Fee amount must be positive")
    private BigDecimal feeAmount;

    private BigDecimal paidAmount;

    private Instant dueDate;

    private String orderId;

    private Boolean isRecurring;

    private String recurringFrequency;

    private Instant nextDueDate;

    private Map<String, Object> customFields;

    private Map<String, Object> properties;
}