package com.scube.record.features.entity_note.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityNoteDto {

    private Long entityNoteId;
    private String title;
    private String content;
    private String noteType;
    private Boolean isPublic;
    private Boolean isPinned;
    private String[] tags;
    private Map<String, Object> properties;

    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    private Boolean isDeleted;
}