package com.scube.record.features.document.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentDto {
    private Long documentId;
    private UUID documentUuid;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    private JsonNode properties;
    private JsonNode events;

    private String contentType;
    private String fileName;
    private Long fileSize;
    private String fileUrl;
    private UUID documentServiceUuid;
    private Instant deletedDate;
    private boolean isDeleted;

    private Long documentTypeId;
    private String documentTypeKey;
    private String documentTypeName;
}