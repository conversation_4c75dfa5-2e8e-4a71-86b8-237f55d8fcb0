package com.scube.record.features.record.controller;

import com.scube.calculation.dto.gen_dto.CartInvoiceResponse;
import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.dto.UpdateRecordRequest;
import com.scube.record.features.record.fee.rules.RecordFeeCalculationResult;
import com.scube.record.features.record.fee.service.RecordFeeService;
import com.scube.record.features.record.service.RecordService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/records")
@RequiredArgsConstructor
public class RecordController {

    private final RecordService recordService;
    private final RecordFeeService recordFeeService;

    @PostMapping
    public ResponseEntity<RecordResponse> createRecord(
            @Valid @RequestBody CreateRecordRequest request) {
        RecordResponse response = recordService.createRecord(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{recordUuid}")
    public ResponseEntity<RecordResponse> updateRecord(
            @PathVariable UUID recordUuid,
            @Valid @RequestBody UpdateRecordRequest request) {
        RecordResponse response = recordService.updateRecord(recordUuid, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{recordUuid}")
    public ResponseEntity<RecordResponse> getRecordByUuid(@PathVariable UUID recordUuid) {
        RecordResponse response = recordService.getRecordByUuid(recordUuid);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    public ResponseEntity<Page<RecordResponse>> getAllRecords(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<RecordResponse> records = recordService.getAllRecords(pageable);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/type/{recordTypeCode}")
    public ResponseEntity<List<RecordResponse>> getRecordsByType(
            @PathVariable String recordTypeCode) {
        List<RecordResponse> records = recordService.getRecordsByType(recordTypeCode);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/status/{status}")
    public ResponseEntity<List<RecordResponse>> getRecordsByStatus(
            @PathVariable String status) {
        List<RecordResponse> records = recordService.getRecordsByStatus(status);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/type/{recordTypeCode}/status/{status}")
    public ResponseEntity<Page<RecordResponse>> getRecordsByTypeAndStatus(
            @PathVariable String recordTypeCode,
            @PathVariable String status,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<RecordResponse> records = recordService.getRecordsByTypeAndStatus(
                recordTypeCode, status, pageable);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/search")
    public ResponseEntity<Page<RecordResponse>> searchRecords(
            @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<RecordResponse> records = recordService.searchRecords(keyword, pageable);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/search/name")
    public ResponseEntity<List<RecordResponse>> searchRecordsByName(
            @RequestParam String recordName) {
        List<RecordResponse> records = recordService.searchRecordsByName(recordName);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/suggestions")
    public ResponseEntity<List<String>> getRecordNameSuggestions(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int limit) {
        List<String> suggestions = recordService.getRecordNameSuggestions(query, limit);
        return ResponseEntity.ok(suggestions);
    }

    @GetMapping("/suggestions/type/{recordTypeCode}")
    public ResponseEntity<List<String>> getRecordNameSuggestionsByType(
            @RequestParam String query,
            @PathVariable String recordTypeCode,
            @RequestParam(defaultValue = "10") int limit) {
        List<String> suggestions = recordService.getRecordNameSuggestionsByType(
                query, recordTypeCode, limit);
        return ResponseEntity.ok(suggestions);
    }

    @DeleteMapping("/{recordUuid}")
    public ResponseEntity<Void> deleteRecord(@PathVariable UUID recordUuid) {
        recordService.deleteRecord(recordUuid);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/fees/{recordUuid}")
    public ResponseEntity<String> getRecordFees(@PathVariable UUID recordUuid) {
        String fees = recordFeeService.getRecordFees(recordUuid);
        return ResponseEntity.ok(fees);
    }

    @GetMapping("/fees/{recordUuid}/calculate")
    public ResponseEntity<String> calculateRecordFees(@PathVariable UUID recordUuid) {
        String fees = recordFeeService.calculateRecordFees(recordUuid);
        return ResponseEntity.ok(fees);
    }

    @PostMapping("/fees/{recordUuid}/calculate-with-rules")
    public ResponseEntity<CartInvoiceResponse> calculateRecordFeesWithRules(@PathVariable UUID recordUuid) {
        CartInvoiceResponse invoice = recordFeeService.calculateRecordFeesViaCalculationService(recordUuid);
        return ResponseEntity.ok(invoice);
    }

    @GetMapping("/fees/{recordUuid}/preview-rules")
    public ResponseEntity<List<RecordFeeCalculationResult>> previewFeeRules(@PathVariable UUID recordUuid) {
        List<RecordFeeCalculationResult> results = recordFeeService.calculateRecordFeesUsingRules(recordUuid);
        return ResponseEntity.ok(results);
    }

    @PostMapping("/fees/{recordUuid}/calculate-local")
    public ResponseEntity<String> calculateRecordFeesLocally(@PathVariable UUID recordUuid) {
        String result = recordFeeService.calculateAndCreateFeesLocally(recordUuid);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/fees/{recordUuid}/reprocess")
    public ResponseEntity<String> reprocessRecordFees(@PathVariable UUID recordUuid) {
        String fees = recordFeeService.reprocessRecordFees(recordUuid);
        return ResponseEntity.ok(fees);
    }

    @GetMapping("/actions/{recordUuid}")
    public ResponseEntity<String> getRecordActions(@PathVariable UUID recordUuid) {
        String actions = recordFeeService.getRecordActions(recordUuid);
        return ResponseEntity.ok(actions);
    }

    @GetMapping("/expired/{recordUuid}")
    public ResponseEntity<Boolean> isRecordExpired(@PathVariable UUID recordUuid) {
        boolean expired = recordFeeService.isRecordExpired(recordUuid);
        return ResponseEntity.ok(expired);
    }

    @GetMapping("/exists/{recordUuid}")
    public ResponseEntity<Boolean> existsByUuid(@PathVariable UUID recordUuid) {
        boolean exists = recordService.existsByUuid(recordUuid);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/count")
    public ResponseEntity<Long> countRecords() {
        long count = recordService.countRecords();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/count/type/{recordTypeCode}")
    public ResponseEntity<Long> countRecordsByType(@PathVariable String recordTypeCode) {
        long count = recordService.countRecordsByType(recordTypeCode);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/count/status/{status}")
    public ResponseEntity<Long> countRecordsByStatus(@PathVariable String status) {
        long count = recordService.countRecordsByStatus(status);
        return ResponseEntity.ok(count);
    }
}