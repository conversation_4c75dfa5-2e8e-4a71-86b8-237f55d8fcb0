package com.scube.record.features.association_type.controller;

import com.scube.record.features.association_type.dto.AssociationTypeResponse;
import com.scube.record.features.association_type.dto.CreateAssociationTypeRequest;
import com.scube.record.features.association_type.dto.UpdateAssociationTypeRequest;
import com.scube.record.features.association_type.service.AssociationTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/config/association-types")
@RequiredArgsConstructor
@Tag(name = "Association Type Configuration", description = "Configure association types (relationship templates)")
public class AssociationTypeConfigController {

    private final AssociationTypeService associationTypeService;

    @PostMapping
    @Operation(summary = "Create association type", description = "Create a new association type configuration")
    public ResponseEntity<AssociationTypeResponse> createAssociationType(
            @Valid @RequestBody CreateAssociationTypeRequest request) {
        AssociationTypeResponse response = associationTypeService.createAssociationType(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{associationTypeUuid}")
    @Operation(summary = "Update association type", description = "Update an existing association type configuration")
    public ResponseEntity<AssociationTypeResponse> updateAssociationType(
            @PathVariable String associationTypeUuid,
            @Valid @RequestBody UpdateAssociationTypeRequest request) {
        AssociationTypeResponse response = associationTypeService.updateAssociationType(associationTypeUuid, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{associationTypeUuid}")
    @Operation(summary = "Get association type by UUID", description = "Retrieve association type configuration by UUID")
    public ResponseEntity<AssociationTypeResponse> getAssociationTypeByUuid(
            @PathVariable String associationTypeUuid) {
        AssociationTypeResponse response = associationTypeService.getAssociationTypeByUuid(associationTypeUuid);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/code/{typeCode}")
    @Operation(summary = "Get association type by code", description = "Retrieve association type configuration by type code")
    public ResponseEntity<AssociationTypeResponse> getAssociationTypeByCode(
            @PathVariable String typeCode) {
        AssociationTypeResponse response = associationTypeService.getAssociationTypeByCode(typeCode);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(summary = "Get all association types (paginated)", description = "Retrieve all association type configurations with pagination")
    public ResponseEntity<Page<AssociationTypeResponse>> getAllAssociationTypes(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<AssociationTypeResponse> associationTypes = associationTypeService.getAllAssociationTypes(pageable);
        return ResponseEntity.ok(associationTypes);
    }

    @GetMapping("/all")
    @Operation(summary = "Get all association types (unpaginated)", description = "Retrieve all association type configurations without pagination")
    public ResponseEntity<List<AssociationTypeResponse>> getAllAssociationTypesUnpaginated() {
        List<AssociationTypeResponse> associationTypes = associationTypeService.getAllAssociationTypes();
        return ResponseEntity.ok(associationTypes);
    }

    @GetMapping("/by-parent-type/{recordType}")
    @Operation(summary = "Get association types by parent record type",
               description = "Find all association types that allow a specific record type as parent")
    public ResponseEntity<List<AssociationTypeResponse>> getAssociationTypesByParentRecordType(
            @PathVariable String recordType) {
        List<AssociationTypeResponse> associationTypes =
            associationTypeService.getAssociationTypesByParentRecordType(recordType);
        return ResponseEntity.ok(associationTypes);
    }

    @GetMapping("/by-child-type/{recordType}")
    @Operation(summary = "Get association types by child record type",
               description = "Find all association types that allow a specific record type as child")
    public ResponseEntity<List<AssociationTypeResponse>> getAssociationTypesByChildRecordType(
            @PathVariable String recordType) {
        List<AssociationTypeResponse> associationTypes =
            associationTypeService.getAssociationTypesByChildRecordType(recordType);
        return ResponseEntity.ok(associationTypes);
    }

    @GetMapping("/search")
    @Operation(summary = "Search association types", description = "Search association types by keyword")
    public ResponseEntity<Page<AssociationTypeResponse>> searchAssociationTypes(
            @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<AssociationTypeResponse> associationTypes =
            associationTypeService.searchAssociationTypes(keyword, pageable);
        return ResponseEntity.ok(associationTypes);
    }

    @DeleteMapping("/{associationTypeUuid}")
    @Operation(summary = "Delete association type", description = "Delete an association type configuration")
    public ResponseEntity<Void> deleteAssociationType(@PathVariable String associationTypeUuid) {
        associationTypeService.deleteAssociationType(associationTypeUuid);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{associationTypeUuid}/exists")
    @Operation(summary = "Check if association type exists", description = "Check if association type exists by UUID")
    public ResponseEntity<Boolean> existsByUuid(@PathVariable String associationTypeUuid) {
        boolean exists = associationTypeService.existsByUuid(associationTypeUuid);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/count")
    @Operation(summary = "Count association types", description = "Get total count of association types")
    public ResponseEntity<Long> countAssociationTypes() {
        long count = associationTypeService.countAssociationTypes();
        return ResponseEntity.ok(count);
    }
}