package com.scube.record.features.settings.controller;

import com.scube.record.features.settings.dto.SettingDto;
import com.scube.record.features.settings.dto.SettingRequest;
import com.scube.record.features.settings.service.SettingService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/settings")
@RequiredArgsConstructor
public class SettingController {

    private final SettingService settingService;

    @PostMapping
    public ResponseEntity<SettingDto> createSetting(
        @Valid @RequestBody SettingRequest request,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        SettingDto created = settingService.createSetting(request, realm, userId);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @GetMapping("/{settingKey}")
    public ResponseEntity<SettingDto> getSettingByKey(
        @PathVariable String settingKey,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {
        return settingService.findBySettingKeyAndRealm(settingKey, realm)
            .map(setting -> ResponseEntity.ok(setting))
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{settingKey}")
    public ResponseEntity<SettingDto> updateSetting(
        @PathVariable String settingKey,
        @Valid @RequestBody SettingRequest request,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        SettingDto updated = settingService.updateSetting(settingKey, realm, request, userId);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{settingKey}")
    public ResponseEntity<Void> deleteSetting(
        @PathVariable String settingKey,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {
        settingService.deleteSetting(settingKey, realm);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    public ResponseEntity<Page<SettingDto>> getAllSettings(Pageable pageable) {
        Page<SettingDto> settings = settingService.findAllSettings(pageable);
        return ResponseEntity.ok(settings);
    }

    @GetMapping("/realm")
    public ResponseEntity<List<SettingDto>> getSettingsByRealm(
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {
        List<SettingDto> settings = settingService.findByRealm(realm);
        return ResponseEntity.ok(settings);
    }

    @GetMapping("/category/{category}")
    public ResponseEntity<List<SettingDto>> getSettingsByCategory(
        @PathVariable String category,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {
        List<SettingDto> settings = settingService.findByCategoryAndRealm(category, realm);
        return ResponseEntity.ok(settings);
    }

    @GetMapping("/public")
    public ResponseEntity<List<SettingDto>> getPublicSettings() {
        List<SettingDto> settings = settingService.findPublicSettings();
        return ResponseEntity.ok(settings);
    }

    @GetMapping("/type/{settingType}")
    public ResponseEntity<List<SettingDto>> getSettingsByType(
        @PathVariable String settingType,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {
        List<SettingDto> settings = settingService.findBySettingTypeAndRealm(settingType, realm);
        return ResponseEntity.ok(settings);
    }
}