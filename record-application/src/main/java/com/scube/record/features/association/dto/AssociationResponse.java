package com.scube.record.features.association.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssociationResponse {

    private Long associationId;
    private UUID associationUuid;
    private String parentType;
    private String childType;
    private Long parentId;
    private Long childId;
    private String associationTypeId;
    private JsonNode properties;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
}