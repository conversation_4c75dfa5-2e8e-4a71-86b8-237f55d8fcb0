package com.scube.record.features.record.exception;

public class InvalidRecordTypeException extends RuntimeException {

    public InvalidRecordTypeException(String message) {
        super(message);
    }

    public InvalidRecordTypeException(String message, Throwable cause) {
        super(message, cause);
    }

    public static InvalidRecordTypeException withCode(String typeCode) {
        return new InvalidRecordTypeException("Invalid record type code: " + typeCode);
    }
}