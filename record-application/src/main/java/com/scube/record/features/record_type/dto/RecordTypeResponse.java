package com.scube.record.features.record_type.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecordTypeResponse {

    private Long recordTypeId;
    private UUID recordTypeUuid;
    private String typeCode;
    private String typeName;
    private UUID parentUuid;
    private String parentTypeCode;
    private String parentTypeName;
    private String description;
    private JsonNode properties;
    private JsonNode configJson;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
}