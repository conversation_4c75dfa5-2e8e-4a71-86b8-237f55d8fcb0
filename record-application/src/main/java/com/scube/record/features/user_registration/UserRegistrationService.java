package com.scube.record.features.user_registration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.service.RecordService;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserRegistrationService {

    private final RecordService recordService;
    private final RecordRepository recordRepository;
    private final ObjectMapper objectMapper;

    private static final String INDIVIDUAL_RECORD_TYPE_CODE = "INDIVIDUAL";

    @Transactional
    public void registerUser(UserRegistrationEvent event) {
        log.info("========================================");
        log.info("Processing User Registration Event");
        log.info("Event Details: {}", event);
        log.info("========================================");

        try {
            UUID userId = event.getUserId();
            String email = event.getEmail();
            String firstName = Optional.ofNullable(event.getFirstName()).orElse("Unknown");
            String lastName = Optional.ofNullable(event.getLastName()).orElse("Unknown");

            log.debug("Checking if record exists for Keycloak user ID: {}", userId);
            Optional<Record> existingRecord = recordRepository.findByRecordUuid(userId);

            if (existingRecord.isPresent()) {
                log.info("✓ Record already exists for user ID: {} (Record ID: {}). Skipping creation.",
                    userId, existingRecord.get().getRecordId());
                return;
            }
            log.debug("No existing record found for user ID: {}", userId);

            log.debug("Checking if record exists by email: {}", email);
            Record recordByEmail = findRecordByEmail(email);
            if (recordByEmail != null) {
                log.info("✓ Found existing record with email: {}. Linking to Keycloak user ID: {}",
                    email, userId);
                recordByEmail.setRecordUuid(userId);
                recordRepository.save(recordByEmail);
                log.info("✓ Successfully linked record {} to Keycloak user {}",
                    recordByEmail.getRecordId(), userId);
                return;
            }
            log.debug("No existing record found by email: {}", email);

            log.info("→ Creating new INDIVIDUAL record for user: {} {} ({})", firstName, lastName, email);

            ObjectNode properties = objectMapper.createObjectNode();
            properties.put("firstName", firstName);
            properties.put("lastName", lastName);
            properties.put("email", email);

            CreateRecordRequest request = new CreateRecordRequest();
            request.setRecordTypeCode(INDIVIDUAL_RECORD_TYPE_CODE);
            request.setRecordName(firstName + " " + lastName);
            request.setStatus("ACTIVE");
            request.setProperties(properties);
            request.setCreatedBy("system");

            RecordResponse response = recordService.createRecord(request);

            Record record = recordRepository.findByRecordUuid(response.getRecordUuid())
                .orElseThrow(() -> new RuntimeException("Failed to find created record"));

            record.setRecordUuid(userId);
            recordRepository.save(record);

            log.info("========================================");
            log.info("✓ SUCCESS: Created INDIVIDUAL record {} for Keycloak user {}",
                record.getRecordId(), userId);
            log.info("Record Name: {}", record.getRecordName());
            log.info("========================================");

        } catch (Exception e) {
            log.error("========================================");
            log.error("✗ ERROR: Failed to process user registration event");
            log.error("Event: {}", event);
            log.error("Error: ", e);
            log.error("========================================");
        }
    }

    private Record findRecordByEmail(String email) {

        try {
            return recordRepository.findAll().stream()
                .filter(record -> {
                    if (record.getRecordType() != null &&
                        INDIVIDUAL_RECORD_TYPE_CODE.equals(record.getRecordType().getTypeCode())) {
                        if (record.getProperties() != null && record.getProperties().containsKey("email")) {
                            String recordEmail = String.valueOf(record.getProperties().get("email"));
                            return email.equalsIgnoreCase(recordEmail);
                        }
                    }
                    return false;
                })
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.error("Error searching for record by email: {}", email, e);
            return null;
        }
    }
}