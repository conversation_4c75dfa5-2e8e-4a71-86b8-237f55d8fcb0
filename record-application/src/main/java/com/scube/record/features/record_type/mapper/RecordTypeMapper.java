package com.scube.record.features.record_type.mapper;

import com.scube.record.features.record_type.dto.CreateRecordTypeRequest;
import com.scube.record.features.record_type.dto.RecordTypeResponse;
import com.scube.record.features.record_type.dto.UpdateRecordTypeRequest;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE
)
public interface RecordTypeMapper {

    @Mapping(source = "createdBy", target = "lastModifiedBy")
    RecordType toEntity(CreateRecordTypeRequest request);

    @Mapping(source = "parent.recordTypeUuid", target = "parentUuid")
    @Mapping(source = "parent.typeCode", target = "parentTypeCode")
    @Mapping(source = "parent.typeName", target = "parentTypeName")
    RecordTypeResponse toResponse(RecordType recordType);

    void updateEntity(@MappingTarget RecordType recordType, UpdateRecordTypeRequest request);
}