package com.scube.record.features.entity_fee.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetAllEntityFeeResponse {

    private List<EntityFeeDto> entityFees;
    private Totals totals;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Totals {
        private BigDecimal totalAmount;
        private BigDecimal totalPaidAmount;
        private BigDecimal totalOutstandingAmount;
        private Integer totalCount;
        private Integer paidCount;
        private Integer unpaidCount;
        private Integer partiallyPaidCount;

        public static Totals calculateTotals(List<EntityFeeDto> fees) {
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalPaidAmount = BigDecimal.ZERO;
            BigDecimal totalOutstandingAmount = BigDecimal.ZERO;
            int totalCount = fees.size();
            int paidCount = 0;
            int unpaidCount = 0;
            int partiallyPaidCount = 0;

            for (EntityFeeDto fee : fees) {
                if (fee.getFeeAmount() != null) {
                    totalAmount = totalAmount.add(fee.getFeeAmount());
                }
                if (fee.getPaidAmount() != null) {
                    totalPaidAmount = totalPaidAmount.add(fee.getPaidAmount());
                }
                if (fee.getOutstandingAmount() != null) {
                    totalOutstandingAmount = totalOutstandingAmount.add(fee.getOutstandingAmount());
                }

                switch (fee.getPaymentStatus()) {
                    case PAID -> paidCount++;
                    case UNPAID -> unpaidCount++;
                    case PARTIALLY_PAID -> partiallyPaidCount++;
                }
            }

            return new Totals(totalAmount, totalPaidAmount, totalOutstandingAmount,
                    totalCount, paidCount, unpaidCount, partiallyPaidCount);
        }
    }
}