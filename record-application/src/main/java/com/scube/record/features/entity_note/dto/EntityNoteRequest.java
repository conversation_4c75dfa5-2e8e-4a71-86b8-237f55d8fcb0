package com.scube.record.features.entity_note.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityNoteRequest {

    private String title;

    @NotNull(message = "Content is required")
    private String content;

    private String noteType;

    private Boolean isPublic;

    private Boolean isPinned;

    private String[] tags;

    private Map<String, Object> properties;
}