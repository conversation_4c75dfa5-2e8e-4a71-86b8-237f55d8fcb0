package com.scube.record.features.record.exception;

public class RecordNotFoundException extends RuntimeException {

    public RecordNotFoundException(String message) {
        super(message);
    }

    public RecordNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public static RecordNotFoundException byId(Long id) {
        return new RecordNotFoundException("Record not found with id: " + id);
    }

    public static RecordNotFoundException byUuid(String uuid) {
        return new RecordNotFoundException("Record not found with uuid: " + uuid);
    }
}