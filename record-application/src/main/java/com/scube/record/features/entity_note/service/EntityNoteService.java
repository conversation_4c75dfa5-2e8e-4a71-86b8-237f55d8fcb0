package com.scube.record.features.entity_note.service;

import com.scube.record.features.entity_note.dto.EntityNoteDto;
import com.scube.record.features.entity_note.dto.EntityNoteRequest;
import com.scube.record.infrastructure.db.entity.entity_note.EntityNote;
import com.scube.record.infrastructure.db.repository.entity_note.EntityNoteRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class EntityNoteService {

    private final EntityNoteRepository entityNoteRepository;

    public EntityNoteDto createEntityNote(EntityNoteRequest request, String createdBy) {
        EntityNote entityNote = new EntityNote();
        mapRequestToEntity(request, entityNote);
        entityNote.setCreatedBy(createdBy);
        entityNote.setLastModifiedBy(createdBy);

        EntityNote savedEntityNote = entityNoteRepository.save(entityNote);
        return convertToDto(savedEntityNote);
    }

    public EntityNoteDto updateEntityNote(Long entityNoteId, EntityNoteRequest request, String modifiedBy) {
        EntityNote entityNote = entityNoteRepository.findById(entityNoteId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Entity note not found with ID: " + entityNoteId));

        if (StringUtils.hasText(request.getTitle())) {
            entityNote.setTitle(request.getTitle());
        }
        if (StringUtils.hasText(request.getContent())) {
            entityNote.setContent(request.getContent());
        }
        if (StringUtils.hasText(request.getNoteType())) {
            entityNote.setNoteType(request.getNoteType());
        }
        if (request.getIsPublic() != null) {
            entityNote.setIsPublic(request.getIsPublic());
        }
        if (request.getIsPinned() != null) {
            entityNote.setIsPinned(request.getIsPinned());
        }
        if (request.getTags() != null) {
            entityNote.setTags(request.getTags());
        }
        if (request.getProperties() != null) {
            entityNote.setProperties(request.getProperties());
        }
        entityNote.setLastModifiedBy(modifiedBy);

        EntityNote updatedEntityNote = entityNoteRepository.save(entityNote);
        return convertToDto(updatedEntityNote);
    }

    @Transactional(readOnly = true)
    public Optional<EntityNoteDto> findById(Long entityNoteId) {
        return entityNoteRepository.findById(entityNoteId)
            .map(this::convertToDto);
    }

    @Transactional(readOnly = true)
    public Page<EntityNoteDto> findAllEntityNotes(Pageable pageable) {
        Page<EntityNote> entityNotes = entityNoteRepository.findAll(pageable);
        List<EntityNoteDto> entityNoteDtos = entityNotes.getContent().stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        return new PageImpl<>(entityNoteDtos, pageable, entityNotes.getTotalElements());
    }

    @Transactional(readOnly = true)
    public List<EntityNoteDto> findByNoteType(String noteType) {
        List<EntityNote> entityNotes = entityNoteRepository.findByNoteType(noteType);
        return entityNotes.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EntityNoteDto> findPublicNotes() {
        List<EntityNote> entityNotes = entityNoteRepository.findPublicNotes();
        return entityNotes.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EntityNoteDto> findPinnedNotes() {
        List<EntityNote> entityNotes = entityNoteRepository.findPinnedNotes();
        return entityNotes.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EntityNoteDto> findByCreatedBy(String createdBy) {
        List<EntityNote> entityNotes = entityNoteRepository.findByCreatedBy(createdBy);
        return entityNotes.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EntityNoteDto> searchByKeyword(String keyword) {
        List<EntityNote> entityNotes = entityNoteRepository.searchByKeyword(keyword);
        return entityNotes.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    public void deleteEntityNote(Long entityNoteId) {
        EntityNote entityNote = entityNoteRepository.findById(entityNoteId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Entity note not found with ID: " + entityNoteId));

        entityNoteRepository.delete(entityNote);
    }

    private void mapRequestToEntity(EntityNoteRequest request, EntityNote entityNote) {
        entityNote.setTitle(request.getTitle());
        entityNote.setContent(request.getContent());
        entityNote.setNoteType(request.getNoteType());
        entityNote.setIsPublic(request.getIsPublic() != null ? request.getIsPublic() : false);
        entityNote.setIsPinned(request.getIsPinned() != null ? request.getIsPinned() : false);
        entityNote.setTags(request.getTags());
        entityNote.setProperties(request.getProperties());
    }

    private EntityNoteDto convertToDto(EntityNote entityNote) {
        EntityNoteDto dto = new EntityNoteDto();
        dto.setEntityNoteId(entityNote.getEntityNoteId());
        dto.setTitle(entityNote.getTitle());
        dto.setContent(entityNote.getContent());
        dto.setNoteType(entityNote.getNoteType());
        dto.setIsPublic(entityNote.getIsPublic());
        dto.setIsPinned(entityNote.getIsPinned());
        dto.setTags(entityNote.getTags());
        dto.setProperties(entityNote.getProperties());

        dto.setCreatedAt(entityNote.getCreatedAt());
        dto.setCreatedBy(entityNote.getCreatedBy());
        dto.setLastModifiedAt(entityNote.getLastModifiedAt());
        dto.setLastModifiedBy(entityNote.getLastModifiedBy());
        dto.setIsDeleted(entityNote.getIsDeleted());

        return dto;
    }
}