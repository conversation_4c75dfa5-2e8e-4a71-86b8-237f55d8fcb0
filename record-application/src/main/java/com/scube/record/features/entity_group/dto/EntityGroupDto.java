package com.scube.record.features.entity_group.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityGroupDto {

    private Long entityGroupId;
    private String groupName;
    private String groupType;
    private String description;
    private Boolean isActive;
    private Map<String, Object> customFields;
    private Map<String, Object> properties;

    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    private Boolean isDeleted;
}