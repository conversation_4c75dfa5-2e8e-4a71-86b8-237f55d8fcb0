package com.scube.record.features.entity_fee.controller;

import com.scube.record.features.entity_fee.dto.EntityFeeDto;
import com.scube.record.features.entity_fee.dto.EntityFeeRequest;
import com.scube.record.features.entity_fee.dto.GetAllEntityFeeResponse;
import com.scube.record.features.entity_fee.service.EntityFeeService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/entity-fees")
@RequiredArgsConstructor
public class EntityFeeController {

    private final EntityFeeService entityFeeService;

    @PostMapping
    public ResponseEntity<EntityFeeDto> createEntityFee(
        @Valid @RequestBody EntityFeeRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityFeeDto created = entityFeeService.createEntityFee(request, userId);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @GetMapping("/{entityFeeId}")
    public ResponseEntity<EntityFeeDto> getEntityFeeById(@PathVariable Long entityFeeId) {
        return entityFeeService.findById(entityFeeId)
            .map(entityFee -> ResponseEntity.ok(entityFee))
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{entityFeeId}")
    public ResponseEntity<EntityFeeDto> updateEntityFee(
        @PathVariable Long entityFeeId,
        @Valid @RequestBody EntityFeeRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityFeeDto updated = entityFeeService.updateEntityFee(entityFeeId, request, userId);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{entityFeeId}")
    public ResponseEntity<Void> deleteEntityFee(@PathVariable Long entityFeeId) {
        entityFeeService.deleteEntityFee(entityFeeId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    public ResponseEntity<Page<EntityFeeDto>> getAllEntityFees(Pageable pageable) {
        Page<EntityFeeDto> entityFees = entityFeeService.findAllEntityFees(pageable);
        return ResponseEntity.ok(entityFees);
    }

    @GetMapping("/with-totals")
    public ResponseEntity<GetAllEntityFeeResponse> getAllEntityFeesWithTotals() {
        GetAllEntityFeeResponse response = entityFeeService.findAllEntityFeesWithTotals();
        return ResponseEntity.ok(response);
    }

    @GetMapping("/unpaid")
    public ResponseEntity<List<EntityFeeDto>> getUnpaidFees() {
        List<EntityFeeDto> unpaidFees = entityFeeService.findUnpaidFees();
        return ResponseEntity.ok(unpaidFees);
    }

    @GetMapping("/by-order/{orderId}")
    public ResponseEntity<List<EntityFeeDto>> getFeesByOrderId(@PathVariable String orderId) {
        List<EntityFeeDto> entityFees = entityFeeService.findByOrderId(orderId);
        return ResponseEntity.ok(entityFees);
    }

    @GetMapping("/by-name/{feeName}")
    public ResponseEntity<List<EntityFeeDto>> getFeesByName(@PathVariable String feeName) {
        List<EntityFeeDto> entityFees = entityFeeService.findByFeeName(feeName);
        return ResponseEntity.ok(entityFees);
    }

    @PatchMapping("/{entityFeeId}/payment")
    public ResponseEntity<EntityFeeDto> updatePaymentStatus(
        @PathVariable Long entityFeeId,
        @RequestBody PaymentUpdateRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityFeeDto updated = entityFeeService.updatePaymentStatus(
            entityFeeId, request.paidAmount(), request.orderId(), userId);
        return ResponseEntity.ok(updated);
    }

    @PostMapping("/process-recurring")
    public ResponseEntity<String> processRecurringFees() {
        entityFeeService.processRecurringFees();
        return ResponseEntity.ok("Recurring fees processed successfully");
    }

    @PostMapping("/mark-overdue")
    public ResponseEntity<String> markOverdueFees() {
        entityFeeService.markOverdueFees();
        return ResponseEntity.ok("Overdue fees marked successfully");
    }

    public record PaymentUpdateRequest(BigDecimal paidAmount, String orderId) {}
}