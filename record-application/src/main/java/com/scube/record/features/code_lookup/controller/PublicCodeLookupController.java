package com.scube.record.features.code_lookup.controller;

import com.scube.record.features.code_lookup.service.CodeLookupService;
import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookup;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/public/code-lookup")
@RequiredArgsConstructor
public class PublicCodeLookupController {

    private final CodeLookupService codeLookupService;

    @GetMapping("/{code}")
    public ResponseEntity<CodeLookup> getByCode(@PathVariable String code) {
        Optional<CodeLookup> codeLookup = codeLookupService.findByCode(code);
        return codeLookup
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
}