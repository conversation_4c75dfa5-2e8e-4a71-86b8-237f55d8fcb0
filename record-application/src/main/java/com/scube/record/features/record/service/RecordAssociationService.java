package com.scube.record.features.record.service;

import com.scube.record.features.association_type.exception.AssociationTypeNotFoundException;
import com.scube.record.features.record.dto.RecordAssociationRequest;
import com.scube.record.features.record.exception.RecordNotFoundException;
import com.scube.record.infrastructure.db.entity.record.Association;
import com.scube.record.infrastructure.db.entity.record.AssociationType;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.repository.record.AssociationRepository;
import com.scube.record.infrastructure.db.repository.record.AssociationTypeRepository;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class RecordAssociationService {

    private final AssociationRepository associationRepository;
    private final AssociationTypeRepository associationTypeRepository;
    private final RecordRepository recordRepository;

    @Transactional
    public void createAssociations(Record record, List<RecordAssociationRequest> associationRequests, String createdBy) {
        if (associationRequests == null || associationRequests.isEmpty()) {
            return;
        }

        for (RecordAssociationRequest request : associationRequests) {
            createBidirectionalAssociation(record, request, createdBy);
        }
    }

    @Transactional
    public void createBidirectionalAssociation(Record record, RecordAssociationRequest request, String createdBy) {
        AssociationType associationType = associationTypeRepository.findByTypeCode(request.getAssociationTypeCode())
                .orElseThrow(() -> new AssociationTypeNotFoundException(
                        "Association type not found with code: " + request.getAssociationTypeCode()));

        Record associatedRecord = recordRepository.findByRecordUuid(UUID.fromString(request.getAssociatedRecordUuid()))
                .orElseThrow(() -> new RecordNotFoundException(
                        "Associated record not found with UUID: " + request.getAssociatedRecordUuid()));

        Record parentRecord;
        Record childRecord;

        if (request.getRole() != null && !request.getRole().isEmpty()) {
            if (request.getRole().equalsIgnoreCase("PARENT")) {
                parentRecord = record;
                childRecord = associatedRecord;
            } else if (request.getRole().equalsIgnoreCase("CHILD")) {
                parentRecord = associatedRecord;
                childRecord = record;
            } else {
                throw new IllegalArgumentException("Invalid role: " + request.getRole() + ". Must be PARENT or CHILD");
            }
        } else {
            boolean recordCanBeParent = canBeParent(associationType, record);
            boolean recordCanBeChild = canBeChild(associationType, record);
            boolean associatedCanBeParent = canBeParent(associationType, associatedRecord);
            boolean associatedCanBeChild = canBeChild(associationType, associatedRecord);

            if (recordCanBeParent && associatedCanBeChild) {
                parentRecord = record;
                childRecord = associatedRecord;
            } else if (recordCanBeChild && associatedCanBeParent) {
                parentRecord = associatedRecord;
                childRecord = record;
            } else {
                throw new IllegalArgumentException(
                        String.format("Association type '%s' does not allow relationship between record types '%s' and '%s'",
                                associationType.getAssociationName(),
                                record.getRecordType().getTypeCode(),
                                associatedRecord.getRecordType().getTypeCode()));
            }
        }

        validateAssociation(associationType, parentRecord, childRecord);

        if (associationRepository.existsByParentIdAndChildIdAndAssociationTypeId(
                parentRecord.getRecordId(), childRecord.getRecordId(), associationType.getAssociationTypeId().toString())) {
            log.warn("Association already exists between parent {} and child {} with type {}",
                    parentRecord.getRecordUuid(), childRecord.getRecordUuid(), associationType.getAssociationName());
            return;
        }

        createAssociationPair(parentRecord, childRecord, associationType, createdBy);

        log.info("Created bidirectional association: {} [{}] <-[{}]-> {} [{}]",
                parentRecord.getRecordName(), parentRecord.getRecordType().getTypeCode(),
                associationType.getAssociationName(),
                childRecord.getRecordName(), childRecord.getRecordType().getTypeCode());
    }

    private void createAssociationPair(Record parentRecord, Record childRecord, AssociationType associationType, String createdBy) {
        Association parentToChild = new Association();
        parentToChild.setParentType(parentRecord.getRecordType().getTypeCode());
        parentToChild.setChildType(childRecord.getRecordType().getTypeCode());
        parentToChild.setParentId(parentRecord.getRecordId());
        parentToChild.setChildId(childRecord.getRecordId());
        parentToChild.setAssociationTypeId(associationType.getAssociationTypeId().toString());
        parentToChild.setCreatedBy(createdBy);
        parentToChild.setLastModifiedBy(createdBy);
        associationRepository.save(parentToChild);

        Association childToParent = new Association();
        childToParent.setParentType(childRecord.getRecordType().getTypeCode());
        childToParent.setChildType(parentRecord.getRecordType().getTypeCode());
        childToParent.setParentId(childRecord.getRecordId());
        childToParent.setChildId(parentRecord.getRecordId());
        childToParent.setAssociationTypeId(associationType.getAssociationTypeId().toString());
        childToParent.setCreatedBy(createdBy);
        childToParent.setLastModifiedBy(createdBy);
        associationRepository.save(childToParent);
    }

    private void validateAssociation(AssociationType associationType, Record parentRecord, Record childRecord) {
        String parentTypeCode = parentRecord.getRecordType().getTypeCode();
        String childTypeCode = childRecord.getRecordType().getTypeCode();

        boolean parentAllowed = canBeParent(associationType, parentRecord);
        boolean childAllowed = canBeChild(associationType, childRecord);

        if (!parentAllowed) {
            throw new IllegalArgumentException(
                    String.format("Record type '%s' is not allowed as parent in association type '%s'",
                            parentTypeCode, associationType.getAssociationName()));
        }

        if (!childAllowed) {
            throw new IllegalArgumentException(
                    String.format("Record type '%s' is not allowed as child in association type '%s'",
                            childTypeCode, associationType.getAssociationName()));
        }
    }

    private boolean canBeParent(AssociationType associationType, Record record) {
        if (associationType.getProperties() == null || !associationType.getProperties().has("parent_record_types")) {
            return false;
        }

        String recordTypeCode = record.getRecordType().getTypeCode();
        var parentTypes = associationType.getProperties().get("parent_record_types");

        if (parentTypes.isArray()) {
            for (var type : parentTypes) {
                if (type.asText().equals(recordTypeCode)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean canBeChild(AssociationType associationType, Record record) {
        if (associationType.getProperties() == null || !associationType.getProperties().has("child_record_types")) {
            return false;
        }

        String recordTypeCode = record.getRecordType().getTypeCode();
        var childTypes = associationType.getProperties().get("child_record_types");

        if (childTypes.isArray()) {
            for (var type : childTypes) {
                if (type.asText().equals(recordTypeCode)) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<Association> getAssociationsForRecord(Long recordId) {
        return associationRepository.findByParentIdOrChildId(recordId, recordId);
    }

    @Transactional
    public void deleteAssociation(Long parentId, Long childId) {
        associationRepository.deleteByParentIdAndChildId(parentId, childId);
        associationRepository.deleteByParentIdAndChildId(childId, parentId);
    }
}
