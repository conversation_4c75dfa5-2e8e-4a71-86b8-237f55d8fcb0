package com.scube.record.features.document.service;

import com.scube.record.features.document.dto.DocumentTypeDto;
import com.scube.record.infrastructure.db.entity.document.DocumentType;
import com.scube.record.infrastructure.db.repository.document.DocumentTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DocumentTypeService {

    private final DocumentTypeRepository documentTypeRepository;

    public DocumentTypeDto createDocumentType(DocumentTypeDto documentTypeDto, String createdBy) {
        if (documentTypeRepository.existsByTypeKey(documentTypeDto.getTypeKey())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT,
                "Document type already exists with key: " + documentTypeDto.getTypeKey());
        }

        DocumentType documentType = new DocumentType();
        documentType.setTypeKey(documentTypeDto.getTypeKey());
        documentType.setName(documentTypeDto.getName());
        documentType.setGroupName(documentTypeDto.getGroupName());
        documentType.setDescription(documentTypeDto.getDescription());
        documentType.setActive(documentTypeDto.isActive());
        documentType.setCreatedBy(createdBy);
        documentType.setLastModifiedBy(createdBy);

        DocumentType savedDocumentType = documentTypeRepository.save(documentType);
        return convertToDto(savedDocumentType);
    }

    public DocumentTypeDto updateDocumentType(String typeKey, DocumentTypeDto documentTypeDto, String modifiedBy) {
        DocumentType documentType = documentTypeRepository.findByTypeKey(typeKey)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Document type not found with key: " + typeKey));

        if (StringUtils.hasText(documentTypeDto.getName())) {
            documentType.setName(documentTypeDto.getName());
        }
        if (StringUtils.hasText(documentTypeDto.getGroupName())) {
            documentType.setGroupName(documentTypeDto.getGroupName());
        }
        if (StringUtils.hasText(documentTypeDto.getDescription())) {
            documentType.setDescription(documentTypeDto.getDescription());
        }
        documentType.setActive(documentTypeDto.isActive());
        documentType.setLastModifiedBy(modifiedBy);

        DocumentType updatedDocumentType = documentTypeRepository.save(documentType);
        return convertToDto(updatedDocumentType);
    }

    @Transactional(readOnly = true)
    public Optional<DocumentTypeDto> findByTypeKey(String typeKey) {
        return documentTypeRepository.findActiveByTypeKey(typeKey)
            .map(this::convertToDto);
    }

    @Transactional(readOnly = true)
    public Page<DocumentTypeDto> findAllActiveDocumentTypes(Pageable pageable) {
        List<DocumentType> documentTypes = documentTypeRepository.findAllActive();
        List<DocumentTypeDto> documentTypeDtos = documentTypes.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), documentTypeDtos.size());

        return new PageImpl<>(
            documentTypeDtos.subList(start, end),
            pageable,
            documentTypeDtos.size()
        );
    }

    @Transactional(readOnly = true)
    public Page<DocumentTypeDto> findAllDocumentTypesByGroupName(String groupName, Pageable pageable) {
        List<DocumentType> documentTypes = documentTypeRepository.findAllActiveByGroupName(groupName);
        List<DocumentTypeDto> documentTypeDtos = documentTypes.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), documentTypeDtos.size());

        return new PageImpl<>(
            documentTypeDtos.subList(start, end),
            pageable,
            documentTypeDtos.size()
        );
    }

    public void deleteDocumentType(String typeKey) {
        DocumentType documentType = documentTypeRepository.findByTypeKey(typeKey)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Document type not found with key: " + typeKey));

        documentType.setActive(false);
        documentTypeRepository.save(documentType);
    }

    private DocumentTypeDto convertToDto(DocumentType documentType) {
        DocumentTypeDto dto = new DocumentTypeDto();
        dto.setDocumentTypeId(documentType.getDocumentTypeId());
        dto.setCreatedAt(documentType.getCreatedAt());
        dto.setCreatedBy(documentType.getCreatedBy());
        dto.setLastModifiedAt(documentType.getLastModifiedAt());
        dto.setLastModifiedBy(documentType.getLastModifiedBy());
        dto.setTypeKey(documentType.getTypeKey());
        dto.setName(documentType.getName());
        dto.setGroupName(documentType.getGroupName());
        dto.setDescription(documentType.getDescription());
        dto.setActive(documentType.isActive());

        return dto;
    }
}