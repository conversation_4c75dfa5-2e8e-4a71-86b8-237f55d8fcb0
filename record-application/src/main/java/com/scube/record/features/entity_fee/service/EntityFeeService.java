package com.scube.record.features.entity_fee.service;

import com.scube.record.features.entity_fee.dto.EntityFeeDto;
import com.scube.record.features.entity_fee.dto.EntityFeeRequest;
import com.scube.record.features.entity_fee.dto.GetAllEntityFeeResponse;
import com.scube.record.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.record.infrastructure.db.repository.entity_fee.EntityFeeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class EntityFeeService {

    private final EntityFeeRepository entityFeeRepository;

    public EntityFeeDto createEntityFee(EntityFeeRequest request, String createdBy) {
        EntityFee entityFee = new EntityFee();
        mapRequestToEntity(request, entityFee);
        entityFee.setCreatedBy(createdBy);
        entityFee.setLastModifiedBy(createdBy);

        EntityFee savedEntityFee = entityFeeRepository.save(entityFee);
        return convertToDto(savedEntityFee);
    }

    public EntityFeeDto updateEntityFee(Long entityFeeId, EntityFeeRequest request, String modifiedBy) {
        EntityFee entityFee = entityFeeRepository.findById(entityFeeId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Entity fee not found with ID: " + entityFeeId));

        mapRequestToEntity(request, entityFee);
        entityFee.setLastModifiedBy(modifiedBy);

        EntityFee updatedEntityFee = entityFeeRepository.save(entityFee);
        return convertToDto(updatedEntityFee);
    }

    @Transactional(readOnly = true)
    public Optional<EntityFeeDto> findById(Long entityFeeId) {
        return entityFeeRepository.findById(entityFeeId)
            .map(this::convertToDto);
    }

    @Transactional(readOnly = true)
    public Page<EntityFeeDto> findAllEntityFees(Pageable pageable) {
        Page<EntityFee> entityFees = entityFeeRepository.findAll(pageable);
        List<EntityFeeDto> entityFeeDtos = entityFees.getContent().stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        return new PageImpl<>(entityFeeDtos, pageable, entityFees.getTotalElements());
    }

    @Transactional(readOnly = true)
    public GetAllEntityFeeResponse findAllEntityFeesWithTotals() {
        List<EntityFee> entityFees = entityFeeRepository.findAll();
        List<EntityFeeDto> entityFeeDtos = entityFees.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        GetAllEntityFeeResponse.Totals totals = GetAllEntityFeeResponse.Totals.calculateTotals(entityFeeDtos);
        return new GetAllEntityFeeResponse(entityFeeDtos, totals);
    }

    @Transactional(readOnly = true)
    public List<EntityFeeDto> findUnpaidFees() {
        List<EntityFee> unpaidFees = entityFeeRepository.findUnpaidFees();
        return unpaidFees.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EntityFeeDto> findByOrderId(String orderId) {
        List<EntityFee> entityFees = entityFeeRepository.findByOrderId(orderId);
        return entityFees.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<EntityFeeDto> findByFeeName(String feeName) {
        List<EntityFee> entityFees = entityFeeRepository.findByFeeName(feeName);
        return entityFees.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    public EntityFeeDto updatePaymentStatus(Long entityFeeId, BigDecimal paidAmount, String orderId, String modifiedBy) {
        EntityFee entityFee = entityFeeRepository.findById(entityFeeId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Entity fee not found with ID: " + entityFeeId));

        if (paidAmount != null) {
            entityFee.setPaidAmount(paidAmount);
        }
        if (orderId != null) {
            entityFee.setOrderId(orderId);
        }
        entityFee.setLastModifiedBy(modifiedBy);

        EntityFee updatedEntityFee = entityFeeRepository.save(entityFee);
        return convertToDto(updatedEntityFee);
    }

    public void processRecurringFees() {
        Instant currentDate = Instant.now();
        List<EntityFee> recurringFees = entityFeeRepository.findActiveRecurringFeesDue(currentDate);

        for (EntityFee fee : recurringFees) {
            try {
                processRecurringFee(fee);
            } catch (Exception e) {
                log.error("Error processing recurring fee with ID: {}", fee.getEntityFeeId(), e);
            }
        }
    }

    private void processRecurringFee(EntityFee originalFee) {
        EntityFee newFee = new EntityFee();
        newFee.setFeeName(originalFee.getFeeName());
        newFee.setFeeAmount(originalFee.getFeeAmount());
        newFee.setPaidAmount(BigDecimal.ZERO);
        newFee.setPaymentStatus(EntityFee.PaymentStatus.UNPAID);
        newFee.setDueDate(originalFee.getNextDueDate());
        newFee.setIsRecurring(originalFee.getIsRecurring());
        newFee.setRecurringFrequency(originalFee.getRecurringFrequency());
        newFee.setCustomFields(originalFee.getCustomFields());
        newFee.setProperties(originalFee.getProperties());
        newFee.setCreatedBy("SYSTEM");
        newFee.setLastModifiedBy("SYSTEM");

        Instant nextDueDate = calculateNextDueDate(originalFee.getNextDueDate(), originalFee.getRecurringFrequency());
        newFee.setNextDueDate(nextDueDate);

        originalFee.setNextDueDate(nextDueDate);
        originalFee.setLastProcessedDate(Instant.now());

        entityFeeRepository.save(newFee);
        entityFeeRepository.save(originalFee);

        log.info("Created recurring fee for fee ID: {}, next due date: {}", originalFee.getEntityFeeId(), nextDueDate);
    }

    private Instant calculateNextDueDate(Instant currentDueDate, String frequency) {
        if (frequency == null) {
            return currentDueDate.plus(30, ChronoUnit.DAYS); // Default to monthly
        }

        return switch (frequency.toUpperCase()) {
            case "DAILY" -> currentDueDate.plus(1, ChronoUnit.DAYS);
            case "WEEKLY" -> currentDueDate.plus(7, ChronoUnit.DAYS);
            case "MONTHLY" -> currentDueDate.plus(30, ChronoUnit.DAYS);
            case "QUARTERLY" -> currentDueDate.plus(90, ChronoUnit.DAYS);
            case "YEARLY" -> currentDueDate.plus(365, ChronoUnit.DAYS);
            default -> currentDueDate.plus(30, ChronoUnit.DAYS);
        };
    }

    public void markOverdueFees() {
        Instant currentDate = Instant.now();
        List<EntityFee> overdueFees = entityFeeRepository.findOverdueFees(currentDate);

        for (EntityFee fee : overdueFees) {
            fee.setPaymentStatus(EntityFee.PaymentStatus.OVERDUE);
            entityFeeRepository.save(fee);
        }

        log.info("Marked {} fees as overdue", overdueFees.size());
    }

    public void deleteEntityFee(Long entityFeeId) {
        EntityFee entityFee = entityFeeRepository.findById(entityFeeId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Entity fee not found with ID: " + entityFeeId));

        entityFeeRepository.delete(entityFee);
    }

    public void updateEntityFeeAsPaid(com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse orderInvoice) {
        if (orderInvoice == null || orderInvoice.getItems() == null) {
            log.warn("OrderInvoiceResponse is null or has no items");
            return;
        }

        log.info("Updating entity fees as paid for order: {}", orderInvoice.getOrderId());

        List<EntityFee> fees = entityFeeRepository.findByOrderId(orderInvoice.getOrderId().toString());
        
        for (EntityFee fee : fees) {
            fee.setPaidAmount(fee.getFeeAmount());
            fee.setPaymentStatus(EntityFee.PaymentStatus.PAID);
            fee.setPaidDate(Instant.now());
            fee.setLastModifiedBy("SYSTEM");
            entityFeeRepository.save(fee);
        }

        log.info("Updated {} entity fees as paid", fees.size());
    }

    private void mapRequestToEntity(EntityFeeRequest request, EntityFee entityFee) {
        entityFee.setFeeName(request.getFeeName());
        entityFee.setFeeAmount(request.getFeeAmount());

        if (request.getPaidAmount() != null) {
            entityFee.setPaidAmount(request.getPaidAmount());
        }

        entityFee.setDueDate(request.getDueDate());
        entityFee.setOrderId(request.getOrderId());
        entityFee.setIsRecurring(request.getIsRecurring() != null ? request.getIsRecurring() : false);
        entityFee.setRecurringFrequency(request.getRecurringFrequency());
        entityFee.setNextDueDate(request.getNextDueDate());
        entityFee.setCustomFields(request.getCustomFields());
        entityFee.setProperties(request.getProperties());
    }

    private EntityFeeDto convertToDto(EntityFee entityFee) {
        EntityFeeDto dto = new EntityFeeDto();
        dto.setEntityFeeId(entityFee.getEntityFeeId());
        dto.setFeeName(entityFee.getFeeName());
        dto.setFeeAmount(entityFee.getFeeAmount());
        dto.setPaidAmount(entityFee.getPaidAmount());
        dto.setOutstandingAmount(entityFee.getOutstandingAmount());
        dto.setPaymentStatus(entityFee.getPaymentStatus());
        dto.setDueDate(entityFee.getDueDate());
        dto.setPaidDate(entityFee.getPaidDate());
        dto.setOrderId(entityFee.getOrderId());
        dto.setIsRecurring(entityFee.getIsRecurring());
        dto.setRecurringFrequency(entityFee.getRecurringFrequency());
        dto.setNextDueDate(entityFee.getNextDueDate());
        dto.setLastProcessedDate(entityFee.getLastProcessedDate());
        dto.setCustomFields(entityFee.getCustomFields());
        dto.setProperties(entityFee.getProperties());

        dto.setCreatedAt(entityFee.getCreatedAt());
        dto.setCreatedBy(entityFee.getCreatedBy());
        dto.setLastModifiedAt(entityFee.getLastModifiedAt());
        dto.setLastModifiedBy(entityFee.getLastModifiedBy());
        dto.setIsDeleted(entityFee.getIsDeleted());

        return dto;
    }
}