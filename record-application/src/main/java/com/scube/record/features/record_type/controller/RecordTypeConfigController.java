package com.scube.record.features.record_type.controller;

import com.scube.record.features.record_type.dto.CreateRecordTypeRequest;
import com.scube.record.features.record_type.dto.RecordTypeResponse;
import com.scube.record.features.record_type.dto.UpdateRecordTypeRequest;
import com.scube.record.features.record_type.service.RecordTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/config/record-types")
@RequiredArgsConstructor
@Tag(name = "Record Type Configuration", description = "Configure record types (entity templates)")
public class RecordTypeConfigController {

    private final RecordTypeService recordTypeService;

    @PostMapping
    @Operation(summary = "Create record type", description = "Create a new record type configuration")
    public ResponseEntity<RecordTypeResponse> createRecordType(
            @Valid @RequestBody CreateRecordTypeRequest request) {
        RecordTypeResponse response = recordTypeService.createRecordType(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{recordTypeUuid}")
    @Operation(summary = "Update record type", description = "Update an existing record type configuration")
    public ResponseEntity<RecordTypeResponse> updateRecordType(
            @PathVariable UUID recordTypeUuid,
            @Valid @RequestBody UpdateRecordTypeRequest request) {
        RecordTypeResponse response = recordTypeService.updateRecordType(recordTypeUuid, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{recordTypeUuid}")
    @Operation(summary = "Get record type by UUID", description = "Retrieve record type configuration by UUID")
    public ResponseEntity<RecordTypeResponse> getRecordTypeByUuid(
            @PathVariable UUID recordTypeUuid) {
        RecordTypeResponse response = recordTypeService.getRecordTypeByUuid(recordTypeUuid);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/code/{typeCode}")
    @Operation(summary = "Get record type by code", description = "Retrieve record type configuration by type code")
    public ResponseEntity<RecordTypeResponse> getRecordTypeByCode(
            @PathVariable String typeCode) {
        RecordTypeResponse response = recordTypeService.getRecordTypeByCode(typeCode);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    public ResponseEntity<Page<RecordTypeResponse>> getAllRecordTypes(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<RecordTypeResponse> recordTypes = recordTypeService.getAllRecordTypes(pageable);
        return ResponseEntity.ok(recordTypes);
    }

    @GetMapping("/all")
    public ResponseEntity<List<RecordTypeResponse>> getAllRecordTypesUnpaginated() {
        List<RecordTypeResponse> recordTypes = recordTypeService.getAllRecordTypes();
        return ResponseEntity.ok(recordTypes);
    }

    @GetMapping("/roots")
    public ResponseEntity<List<RecordTypeResponse>> getRootRecordTypes() {
        List<RecordTypeResponse> recordTypes = recordTypeService.getRootRecordTypes();
        return ResponseEntity.ok(recordTypes);
    }

    @GetMapping("/{parentUuid}/children")
    public ResponseEntity<List<RecordTypeResponse>> getChildRecordTypes(
            @PathVariable UUID parentUuid) {
        List<RecordTypeResponse> recordTypes = recordTypeService.getChildRecordTypes(parentUuid);
        return ResponseEntity.ok(recordTypes);
    }

    @GetMapping("/name/{typeName}")
    public ResponseEntity<List<RecordTypeResponse>> getRecordTypesByName(
            @PathVariable String typeName) {
        List<RecordTypeResponse> recordTypes = recordTypeService.getRecordTypesByName(typeName);
        return ResponseEntity.ok(recordTypes);
    }

    @GetMapping("/search")
    public ResponseEntity<Page<RecordTypeResponse>> searchRecordTypes(
            @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<RecordTypeResponse> recordTypes = recordTypeService.searchRecordTypes(keyword, pageable);
        return ResponseEntity.ok(recordTypes);
    }

    @DeleteMapping("/{recordTypeUuid}")
    public ResponseEntity<Void> deleteRecordType(@PathVariable UUID recordTypeUuid) {
        recordTypeService.deleteRecordType(recordTypeUuid);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{recordTypeUuid}/exists")
    public ResponseEntity<Boolean> existsByUuid(@PathVariable UUID recordTypeUuid) {
        boolean exists = recordTypeService.existsByUuid(recordTypeUuid);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/code/{typeCode}/exists")
    public ResponseEntity<Boolean> existsByTypeCode(@PathVariable String typeCode) {
        boolean exists = recordTypeService.existsByTypeCode(typeCode);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/count")
    public ResponseEntity<Long> countRecordTypes() {
        long count = recordTypeService.countRecordTypes();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/count/roots")
    public ResponseEntity<Long> countRootRecordTypes() {
        long count = recordTypeService.countRootRecordTypes();
        return ResponseEntity.ok(count);
    }

    @GetMapping("/count/{parentUuid}/children")
    public ResponseEntity<Long> countChildRecordTypes(@PathVariable UUID parentUuid) {
        long count = recordTypeService.countChildRecordTypes(parentUuid);
        return ResponseEntity.ok(count);
    }
}