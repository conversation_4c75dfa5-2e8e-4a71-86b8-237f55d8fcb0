package com.scube.record.features.profile.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.record.features.association.dto.AssociationResponse;
import com.scube.record.features.association.mapper.AssociationMapper;
import com.scube.record.features.profile.dto.ProfileHeaderResponse;
import com.scube.record.features.profile.dto.ProfileRequest;
import com.scube.record.features.profile.dto.ProfileResponse;
import com.scube.record.features.profile.dto.RecordAndAssociationsDto;
import com.scube.record.features.profile.dto.RecordDto;
import com.scube.record.features.record.exception.RecordNotFoundException;
import com.scube.record.infrastructure.db.entity.record.Association;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.repository.record.AssociationRepository;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProfileService {

    private final RecordRepository recordRepository;
    private final AssociationRepository associationRepository;
    private final AssociationMapper associationMapper;
    private final ObjectMapper objectMapper;
    private final RejectedFieldsService rejectedFieldsService;

    public ProfileResponse getProfile(ProfileRequest request) {
        Record record = getRecordFromRequest(request);

        ProfileResponse.ProfileResponseBuilder builder = ProfileResponse.builder()
                .recordId(record.getRecordId())
                .recordUuid(record.getRecordUuid())
                .recordName(record.getRecordName())
                .status(record.getStatus())
                .properties(convertMapToJsonNode(record.getProperties()))
                .recordTypeCode(record.getRecordType() != null ? record.getRecordType().getTypeCode() : null)
                .recordTypeName(record.getRecordType() != null ? record.getRecordType().getTypeName() : null)
                .recordTypeProperties(record.getRecordType() != null ? record.getRecordType().getProperties() : null)
                .createdAt(record.getCreatedAt())
                .createdBy(record.getCreatedBy())
                .lastModifiedAt(record.getLastModifiedAt())
                .lastModifiedBy(record.getLastModifiedBy());

        if (request.isIncludeAssociations()) {
            List<AssociationResponse> associations = getAssociations(record.getRecordId(), request.getAssociationTypes());
            builder.associations(associations);
        }

        if (request.isIncludeRelatedRecords()) {
            List<ProfileResponse.RelatedRecord> relatedRecords = getRelatedRecords(
                    record.getRecordId(), request.getMaxRelationDepth(), request.getAssociationTypes());
            builder.relatedRecords(relatedRecords);
        }

        return builder.build();
    }

    private Record getRecordFromRequest(ProfileRequest request) {
        if (request.getRecordId() != null) {
            return recordRepository.findById(request.getRecordId())
                    .orElseThrow(() -> new RecordNotFoundException("Record not found with ID: " + request.getRecordId()));
        } else if (request.getRecordUuid() != null) {
            return recordRepository.findByRecordUuid(UUID.fromString(request.getRecordUuid()))
                    .orElseThrow(() -> new RecordNotFoundException("Record not found with UUID: " + request.getRecordUuid()));
        } else {
            throw new IllegalArgumentException("Either recordId or recordUuid must be provided");
        }
    }

    private List<AssociationResponse> getAssociations(Long recordId, List<String> associationTypes) {
        List<Association> associations;

        if (associationTypes == null || associationTypes.isEmpty()) {
            associations = associationRepository.findByParentIdOrChildId(recordId, recordId);
        } else {
            associations = associationRepository.findByParentIdOrChildIdAndAssociationTypeIdIn(
                    recordId, recordId, associationTypes);
        }

        return associations.stream()
                .map(associationMapper::toResponse)
                .toList();
    }

    private List<ProfileResponse.RelatedRecord> getRelatedRecords(
            Long recordId, int maxDepth, List<String> associationTypes) {

        Set<Long> relatedRecordIds = new HashSet<>();
        Map<Long, ProfileResponse.RelatedRecord> recordInfoMap = new HashMap<>();

        findRelatedRecordsRecursive(recordId, maxDepth, 0, associationTypes, relatedRecordIds, recordInfoMap);

        relatedRecordIds.remove(recordId);

        List<Record> relatedRecords = recordRepository.findAllById(relatedRecordIds);
        List<ProfileResponse.RelatedRecord> result = new ArrayList<>();

        for (Record record : relatedRecords) {
            ProfileResponse.RelatedRecord relatedInfo = recordInfoMap.get(record.getRecordId());
            if (relatedInfo != null) {
                relatedInfo.setRecordName(record.getRecordName());
                relatedInfo.setRecordTypeCode(record.getRecordType() != null ? record.getRecordType().getTypeCode() : null);
                relatedInfo.setRecordTypeName(record.getRecordType() != null ? record.getRecordType().getTypeName() : null);
                result.add(relatedInfo);
            }
        }

        return result;
    }

    private void findRelatedRecordsRecursive(
            Long recordId, int maxDepth, int currentDepth, List<String> associationTypes,
            Set<Long> relatedRecordIds, Map<Long, ProfileResponse.RelatedRecord> recordInfoMap) {

        if (currentDepth >= maxDepth) {
            return;
        }

        List<Association> associations;
        if (associationTypes == null || associationTypes.isEmpty()) {
            associations = associationRepository.findByParentIdOrChildId(recordId, recordId);
        } else {
            associations = associationRepository.findByParentIdOrChildIdAndAssociationTypeIdIn(
                    recordId, recordId, associationTypes);
        }

        for (Association association : associations) {
            Long relatedId = null;
            String direction = null;

            if (association.getParentId().equals(recordId)) {
                relatedId = association.getChildId();
                direction = "CHILD";
            } else if (association.getChildId().equals(recordId)) {
                relatedId = association.getParentId();
                direction = "PARENT";
            }

            if (relatedId != null && !relatedRecordIds.contains(relatedId)) {
                relatedRecordIds.add(relatedId);

                ProfileResponse.RelatedRecord relatedRecord = ProfileResponse.RelatedRecord.builder()
                        .recordId(relatedId)
                        .recordUuid(null)
                        .relationshipType(association.getAssociationTypeId())
                        .relationshipDirection(direction)
                        .relationshipDepth(currentDepth + 1)
                        .build();

                recordInfoMap.put(relatedId, relatedRecord);

                findRelatedRecordsRecursive(relatedId, maxDepth, currentDepth + 1, associationTypes,
                        relatedRecordIds, recordInfoMap);
            }
        }
    }

    public JsonNode getRecordProfileAndAssociationsAsJsonByUuid(UUID recordUuid) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
                .orElseThrow(() -> new RecordNotFoundException("Record not found with UUID: " + recordUuid.toString()));

        String recordType = record.getRecordType() != null ? record.getRecordType().getTypeCode() : "unknown";
        return buildRecordProfileJson(record, recordType);
    }

    private JsonNode buildRecordProfileJson(Record record, String recordType) {
        ObjectNode profileNode = objectMapper.createObjectNode();

        ObjectNode recordNode = objectMapper.createObjectNode();
        recordNode.put("recordId", record.getRecordId());
        recordNode.put("recordUuid", record.getRecordUuid().toString());
        recordNode.put("recordName", record.getRecordName());
        recordNode.put("status", record.getStatus());
        recordNode.set("properties", convertMapToJsonNode(record.getProperties()));

        if (record.getRecordType() != null) {
            recordNode.put("recordTypeCode", record.getRecordType().getTypeCode());
            recordNode.put("recordTypeName", record.getRecordType().getTypeName());
            recordNode.set("recordTypeProperties", record.getRecordType().getProperties());
        }

        profileNode.set(recordType, recordNode);

        List<Association> associations = associationRepository.findByParentIdOrChildId(record.getRecordId(), record.getRecordId());

        for (Association association : associations) {
            Long relatedRecordId = null;

            if (association.getParentId().equals(record.getRecordId())) {
                relatedRecordId = association.getChildId();
            } else if (association.getChildId().equals(record.getRecordId())) {
                relatedRecordId = association.getParentId();
            }

            if (relatedRecordId != null) {
                recordRepository.findById(relatedRecordId).ifPresent(relatedRecord -> {
                    ObjectNode relatedRecordNode = objectMapper.createObjectNode();
                    relatedRecordNode.put("recordId", relatedRecord.getRecordId());
                    relatedRecordNode.put("recordUuid", relatedRecord.getRecordUuid().toString());
                    relatedRecordNode.put("recordName", relatedRecord.getRecordName());
                    relatedRecordNode.put("status", relatedRecord.getStatus());
                    relatedRecordNode.set("properties", convertMapToJsonNode(relatedRecord.getProperties()));

                    if (relatedRecord.getRecordType() != null) {
                        relatedRecordNode.put("recordTypeCode", relatedRecord.getRecordType().getTypeCode());
                        relatedRecordNode.put("recordTypeName", relatedRecord.getRecordType().getTypeName());
                        relatedRecordNode.set("recordTypeProperties", relatedRecord.getRecordType().getProperties());

                        String relatedRecordTypeKey = relatedRecord.getRecordType().getTypeCode();
                        profileNode.set(relatedRecordTypeKey, relatedRecordNode);
                    }
                });
            }
        }

        return profileNode;
    }

    private com.fasterxml.jackson.databind.JsonNode convertMapToJsonNode(java.util.Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        return objectMapper.valueToTree(map);
    }
}
