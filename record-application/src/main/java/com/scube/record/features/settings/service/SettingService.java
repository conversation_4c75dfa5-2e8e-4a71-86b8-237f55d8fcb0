package com.scube.record.features.settings.service;

import com.scube.record.features.settings.dto.SettingDto;
import com.scube.record.features.settings.dto.SettingRequest;
import com.scube.record.infrastructure.db.entity.settings.Setting;
import com.scube.record.infrastructure.db.repository.settings.SettingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SettingService {

    private final SettingRepository settingRepository;

    public SettingDto createSetting(SettingRequest request, String realm, String createdBy) {
        if (settingRepository.existsBySettingKeyAndRealm(request.getSettingKey(), realm)) {
            throw new ResponseStatusException(HttpStatus.CONFLICT,
                "Setting already exists with key: " + request.getSettingKey() + " in realm: " + realm);
        }

        Setting setting = new Setting();
        mapRequestToEntity(request, setting);
        setting.setRealm(realm);
        setting.setCreatedBy(createdBy);
        setting.setLastModifiedBy(createdBy);

        Setting savedSetting = settingRepository.save(setting);
        return convertToDto(savedSetting);
    }

    public SettingDto updateSetting(String settingKey, String realm, SettingRequest request, String modifiedBy) {
        Setting setting = settingRepository.findBySettingKeyAndRealm(settingKey, realm)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Setting not found with key: " + settingKey + " in realm: " + realm));

        if (StringUtils.hasText(request.getSettingValue())) {
            setting.setSettingValue(request.getSettingValue());
        }
        if (StringUtils.hasText(request.getSettingType())) {
            setting.setSettingType(request.getSettingType());
        }
        if (StringUtils.hasText(request.getCategory())) {
            setting.setCategory(request.getCategory());
        }
        if (StringUtils.hasText(request.getDescription())) {
            setting.setDescription(request.getDescription());
        }
        if (request.getIsPublic() != null) {
            setting.setIsPublic(request.getIsPublic());
        }
        if (request.getIsEncrypted() != null) {
            setting.setIsEncrypted(request.getIsEncrypted());
        }
        if (request.getMetadata() != null) {
            setting.setMetadata(request.getMetadata());
        }
        setting.setLastModifiedBy(modifiedBy);

        Setting updatedSetting = settingRepository.save(setting);
        return convertToDto(updatedSetting);
    }

    @Transactional(readOnly = true)
    public Optional<SettingDto> findBySettingKeyAndRealm(String settingKey, String realm) {
        return settingRepository.findBySettingKeyAndRealm(settingKey, realm)
            .map(this::convertToDto);
    }

    @Transactional(readOnly = true)
    public List<SettingDto> findByRealm(String realm) {
        List<Setting> settings = settingRepository.findByRealm(realm);
        return settings.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<SettingDto> findByCategoryAndRealm(String category, String realm) {
        List<Setting> settings = settingRepository.findByCategoryAndRealm(category, realm);
        return settings.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<SettingDto> findPublicSettings() {
        List<Setting> settings = settingRepository.findPublicSettings();
        return settings.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<SettingDto> findBySettingTypeAndRealm(String settingType, String realm) {
        List<Setting> settings = settingRepository.findBySettingTypeAndRealm(settingType, realm);
        return settings.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Page<SettingDto> findAllSettings(Pageable pageable) {
        Page<Setting> settings = settingRepository.findAll(pageable);
        List<SettingDto> settingDtos = settings.getContent().stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

        return new PageImpl<>(settingDtos, pageable, settings.getTotalElements());
    }

    public void deleteSetting(String settingKey, String realm) {
        Setting setting = settingRepository.findBySettingKeyAndRealm(settingKey, realm)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Setting not found with key: " + settingKey + " in realm: " + realm));

        settingRepository.delete(setting);
    }

    private void mapRequestToEntity(SettingRequest request, Setting setting) {
        setting.setSettingKey(request.getSettingKey());
        setting.setSettingValue(request.getSettingValue());
        setting.setSettingType(request.getSettingType());
        setting.setCategory(request.getCategory());
        setting.setDescription(request.getDescription());
        setting.setIsPublic(request.getIsPublic() != null ? request.getIsPublic() : false);
        setting.setIsEncrypted(request.getIsEncrypted() != null ? request.getIsEncrypted() : false);
        setting.setMetadata(request.getMetadata());
    }

    private SettingDto convertToDto(Setting setting) {
        SettingDto dto = new SettingDto();
        dto.setSettingId(setting.getSettingId());
        dto.setSettingKey(setting.getSettingKey());
        dto.setSettingValue(setting.getSettingValue());
        dto.setSettingType(setting.getSettingType());
        dto.setCategory(setting.getCategory());
        dto.setDescription(setting.getDescription());
        dto.setIsPublic(setting.getIsPublic());
        dto.setIsEncrypted(setting.getIsEncrypted());
        dto.setRealm(setting.getRealm());
        dto.setMetadata(setting.getMetadata());

        dto.setCreatedAt(setting.getCreatedAt());
        dto.setCreatedBy(setting.getCreatedBy());
        dto.setLastModifiedAt(setting.getLastModifiedAt());
        dto.setLastModifiedBy(setting.getLastModifiedBy());
        dto.setIsDeleted(setting.getIsDeleted());

        return dto;
    }
}