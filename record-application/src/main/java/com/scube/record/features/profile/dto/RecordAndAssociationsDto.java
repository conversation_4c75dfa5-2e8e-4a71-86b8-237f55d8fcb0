package com.scube.record.features.profile.dto;

import com.scube.record.features.association.dto.AssociationResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecordAndAssociationsDto {

    private RecordDto record;
    private List<AssociationResponse> associations;
    private List<RecordDto> relatedRecords;

    private String recordType;
    private Integer associationCount;
    private Integer relatedRecordCount;
    private Integer maxDepth;

    private String requestedBy;
    private String accessLevel;
}
