package com.scube.record.features.record.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecordSearchRequest {

    private String recordTypeCode;
    private String recordName;
    private String status;
    private List<String> statuses;
    private JsonNode propertyFilters;
    private String createdBy;
    private String sortBy = "createdAt";
    private String sortDirection = "DESC";
    private Integer page = 0;
    private Integer size = 20;
}