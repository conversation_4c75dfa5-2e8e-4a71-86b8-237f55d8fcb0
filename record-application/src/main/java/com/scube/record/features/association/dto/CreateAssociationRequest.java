package com.scube.record.features.association.dto;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateAssociationRequest {

    @NotBlank(message = "Parent type is required")
    private String parentType;

    @NotBlank(message = "Child type is required")
    private String childType;

    @NotNull(message = "Parent ID is required")
    private Long parentId;

    @NotNull(message = "Child ID is required")
    private Long childId;

    @NotBlank(message = "Association type ID is required")
    private String associationTypeId;

    private JsonNode properties;

    @NotBlank(message = "Created by is required")
    private String createdBy;
}