package com.scube.record.features.document.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateDocumentRequest {
    private String fileName;
    private String contentType;
    private Long fileSize;
    private String fileUrl;
    private String documentTypeKey;
    private JsonNode properties;
}