package com.scube.record.features.document.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentTypeDto {
    private Long documentTypeId;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    private String typeKey;
    private String name;
    private String groupName;
    private String description;
    private boolean isActive;
}