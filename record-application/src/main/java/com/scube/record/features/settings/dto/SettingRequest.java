package com.scube.record.features.settings.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SettingRequest {

    @NotNull(message = "Setting key is required")
    private String settingKey;

    private String settingValue;

    private String settingType;

    private String category;

    private String description;

    private Boolean isPublic;

    private Boolean isEncrypted;

    private Map<String, Object> metadata;
}