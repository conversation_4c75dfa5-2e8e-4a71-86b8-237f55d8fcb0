package com.scube.record.features.entity_fee.controller;

import com.scube.record.features.entity_fee.dto.EntityFeeDto;
import com.scube.record.features.entity_fee.dto.EntityFeeRequest;
import com.scube.record.features.entity_fee.dto.GetAllEntityFeeResponse;
import com.scube.record.features.entity_fee.service.EntityFeeService;
import com.scube.record.permission.Permissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/me/entity-fees")
@RequiredArgsConstructor
@Tag(name = "Logged In User Entity Fee Management", description = "Entity fee management operations for logged-in users")
public class LoggedInUserEntityFeeController {

    private final EntityFeeService entityFeeService;

  

    @GetMapping("/{entityFeeId}")
    @Operation(summary = "Get entity fee by ID", description = "Retrieves an entity fee by its ID")
    @RolesAllowed(Permissions.LoggedInUserEntityFee.GET_ENTITY_FEE_BY_ID)
    public ResponseEntity<EntityFeeDto> getEntityFeeById(
            @Parameter(description = "Entity Fee ID") @PathVariable Long entityFeeId) {
        return entityFeeService.findById(entityFeeId)
            .map(entityFee -> ResponseEntity.ok(entityFee))
            .orElse(ResponseEntity.notFound().build());
    }

     @PatchMapping("/{entityFeeId}/payment")
    @Operation(summary = "Update payment status", description = "Updates the payment status of an entity fee")
    @RolesAllowed(Permissions.LoggedInUserEntityFee.UPDATE_PAYMENT_STATUS)
    public ResponseEntity<EntityFeeDto> updatePaymentStatus(
        @Parameter(description = "Entity Fee ID") @PathVariable Long entityFeeId,
        @RequestBody PaymentUpdateRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityFeeDto updated = entityFeeService.updatePaymentStatus(
            entityFeeId, request.paidAmount(), request.orderId(), userId);
        return ResponseEntity.ok(updated);
    }


  @GetMapping("/by-order/{orderId}")
    @Operation(summary = "Get fees by order ID", description = "Retrieves entity fees by order ID")
    @RolesAllowed(Permissions.LoggedInUserEntityFee.GET_FEES_BY_ORDER_ID)
    public ResponseEntity<List<EntityFeeDto>> getFeesByOrderId(
            @Parameter(description = "Order ID") @PathVariable String orderId) {
        List<EntityFeeDto> entityFees = entityFeeService.findByOrderId(orderId);
        return ResponseEntity.ok(entityFees);
    }


 @GetMapping("/by-name/{feeName}")
    @Operation(summary = "Get fees by name", description = "Retrieves entity fees by fee name")
    @RolesAllowed(Permissions.LoggedInUserEntityFee.GET_FEES_BY_NAME)
    public ResponseEntity<List<EntityFeeDto>> getFeesByName(
            @Parameter(description = "Fee Name") @PathVariable String feeName) {
        List<EntityFeeDto> entityFees = entityFeeService.findByFeeName(feeName);
        return ResponseEntity.ok(entityFees);
    }


    public record PaymentUpdateRequest(BigDecimal paidAmount, String orderId) {}
}
