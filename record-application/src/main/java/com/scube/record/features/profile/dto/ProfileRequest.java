package com.scube.record.features.profile.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfileRequest {

    private Long recordId;
    private String recordUuid;
    private boolean includeAssociations = true;
    private List<String> associationTypes;
    private boolean includeRelatedRecords = false;
    private int maxRelationDepth = 1;
}