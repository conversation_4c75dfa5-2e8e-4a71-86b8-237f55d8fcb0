package com.scube.record.features.profile.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProfileHeaderResponse {

    private Long recordId;
    private UUID recordUuid;
    private String recordName;
    private String status;
    private String recordTypeCode;
    private String recordTypeName;
    private Instant createdAt;
    private String createdBy;
    private int associationCount;
    private int relatedRecordCount;
}