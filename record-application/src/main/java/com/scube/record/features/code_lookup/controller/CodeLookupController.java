package com.scube.record.features.code_lookup.controller;

import com.scube.record.features.code_lookup.dto.CodeLookupRequest;
import com.scube.record.features.code_lookup.dto.CodeLookupResponse;
import com.scube.record.features.code_lookup.service.CodeLookupService;
import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookup;
import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.record.infrastructure.db.repository.code_lookup.CodeLookupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/code-lookup")
@RequiredArgsConstructor
public class CodeLookupController {

    private final CodeLookupService codeLookupService;
    private final CodeLookupRepository codeLookupRepository;

    @GetMapping("/entity/{entityType}/{entityId}")
    public ResponseEntity<CodeLookupResponse> getCodesByEntity(
        @PathVariable String entityType,
        @PathVariable String entityId,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {

        CodeLookupResponse response = codeLookupService.getCodesByEntity(entityType, entityId, realm);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/create-or-lookup")
    public ResponseEntity<CodeLookupResponse> createOrLookupCode(
        @Valid @RequestBody CodeLookupRequest request,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {

        CodeLookupResponse response = codeLookupService.createOrLookupCode(request, realm);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/create")
    public ResponseEntity<CodeLookup> createCode(
        @Valid @RequestBody CodeLookupRequest request,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {

        CodeLookup codeLookup = codeLookupService.createCodeLookup(request, realm);
        return ResponseEntity.status(HttpStatus.CREATED).body(codeLookup);
    }

    @PostMapping("/batch-tag-create")
    public ResponseEntity<List<CodeLookup>> batchTagCreate(
        @RequestBody BatchTagCreateRequest request,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {

        List<CodeLookup> createdCodes = request.entityIds().stream()
            .map(entityId -> codeLookupService.createTagCode(entityId, realm, request.action()))
            .toList();

        return ResponseEntity.status(HttpStatus.CREATED).body(createdCodes);
    }

    @PatchMapping("/tag-update/{entityId}")
    public ResponseEntity<CodeLookup> updateTagCode(
        @PathVariable String entityId,
        @RequestBody TagUpdateRequest request,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {

        Optional<CodeLookup> existingCode = codeLookupRepository
            .findByEntityTypeAndEntityIdAndRealmAndAction("TAG", entityId, realm, request.oldAction());

        if (existingCode.isPresent()) {
            CodeLookup codeLookup = existingCode.get();
            codeLookup.setAction(request.newAction());
            CodeLookup updatedCode = codeLookupRepository.save(codeLookup);
            return ResponseEntity.ok(updatedCode);
        } else {
            CodeLookup newCode = codeLookupService.createTagCode(entityId, realm, request.newAction());
            return ResponseEntity.status(HttpStatus.CREATED).body(newCode);
        }
    }

    @PatchMapping("/tag-create-or-update/{entityId}")
    public ResponseEntity<CodeLookup> createOrUpdateTagCode(
        @PathVariable String entityId,
        @RequestBody TagCreateOrUpdateRequest request,
        @RequestHeader(value = "X-Realm", defaultValue = "default") String realm) {

        Optional<CodeLookup> existingCode = codeLookupRepository
            .findByEntityTypeAndEntityIdAndRealmAndAction("TAG", entityId, realm, request.action());

        if (existingCode.isPresent()) {
            return ResponseEntity.ok(existingCode.get());
        } else {
            CodeLookup newCode = codeLookupService.createTagCode(entityId, realm, request.action());
            return ResponseEntity.status(HttpStatus.CREATED).body(newCode);
        }
    }

    @DeleteMapping("/{code}")
    public ResponseEntity<Void> deleteCode(@PathVariable String code) {
        codeLookupService.deleteCodeLookup(code);
        return ResponseEntity.noContent().build();
    }

    public record BatchTagCreateRequest(List<String> entityIds, CodeLookupActionEnum action) {}
    public record TagUpdateRequest(CodeLookupActionEnum oldAction, CodeLookupActionEnum newAction) {}
    public record TagCreateOrUpdateRequest(CodeLookupActionEnum action) {}
}