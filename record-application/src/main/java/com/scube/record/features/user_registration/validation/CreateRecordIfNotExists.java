package com.scube.record.features.user_registration.validation;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.record.features.user_registration.UserRegistrationEvent;
import com.scube.record.features.user_registration.UserRegistrationService;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;
import java.util.UUID;

@Target({ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CreateRecordIfNotExists.CheckRecordExistValidator.class)
@Documented
public @interface CreateRecordIfNotExists {
    
    String message() default "Record validation failed";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    @Slf4j
    @Component
    @RequiredArgsConstructor
    class CheckRecordExistValidator implements ConstraintValidator<CreateRecordIfNotExists, MyOpenIdClaimSet> {
        
        private final RecordRepository recordRepository;
        private final UserRegistrationService userRegistrationService;
        
        @Override
        public void initialize(CreateRecordIfNotExists constraintAnnotation) {
        }
        
        @Override
        public boolean isValid(MyOpenIdClaimSet claimSet, ConstraintValidatorContext constraintValidatorContext) {
            if (claimSet == null) {
                log.debug("ClaimSet is null, skipping record creation");
                return true;
            }
            
            try {
                UUID entityId = UUID.fromString(claimSet.getSubject());
                log.debug("Checking if record exists for user: {}", entityId);
                
                boolean exists = recordRepository.existsByRecordUuid(entityId);
                
                if (!exists) {
                    log.info("Record does not exist for user: {}, creating new individual record", entityId);
                    
                    UserRegistrationEvent event = new UserRegistrationEvent(
                        claimSet.getGivenName(),
                        claimSet.getFamilyName(), 
                        claimSet.getEmail(),
                        entityId
                    );
                    
                    userRegistrationService.registerUser(event);
                    log.info("Successfully created individual record for user: {}", entityId);
                } else {
                    log.debug("Record already exists for user: {}", entityId);
                }
                
                return true;
                
            } catch (IllegalArgumentException e) {
                log.warn("Invalid UUID format in JWT subject: {}", claimSet.getSubject(), e);
                return true; // Don't fail validation for invalid UUID format
            } catch (Exception e) {
                log.error("Error during record creation for user: {}", claimSet.getSubject(), e);
                throw e; // Propagate other exceptions
            }
        }
    }
}
