package com.scube.record.features.association.exception;

public class DuplicateAssociationException extends RuntimeException {

    public DuplicateAssociationException(String message) {
        super(message);
    }

    public DuplicateAssociationException(String message, Throwable cause) {
        super(message, cause);
    }

    public static DuplicateAssociationException between(Long parentId, Long childId, String associationType) {
        return new DuplicateAssociationException(
                String.format("Association already exists between parent %d and child %d with type %s",
                        parentId, childId, associationType));
    }
}