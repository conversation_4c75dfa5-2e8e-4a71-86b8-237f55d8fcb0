package com.scube.record.features.record.fee.service;

import com.scube.calculation.dto.gen_dto.CartInvoiceResponse;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.record.features.entity_fee.dto.EntityFeeRequest;
import com.scube.record.features.entity_fee.service.EntityFeeService;
import com.scube.record.features.record.fee.GetCartItemInvoiceQuery;
import com.scube.record.features.record.fee.rules.RecordFeeCalculationResult;
import com.scube.record.features.record.fee.rules.RecordFeeRuleEngine;
import com.scube.record.features.record.service.RecordService;
import com.scube.record.infrastructure.db.entity.record.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class RecordFeeService {

    private final AmqpGateway amqpGateway;
    private final RecordService recordService;
    private final EntityFeeService entityFeeService;
    private final RecordFeeRuleEngine feeRuleEngine;

    public String getRecordFees(UUID recordUuid) {
        log.info("Getting current fees for record: {}", recordUuid);

        var record = recordService.findRecordByUuid(recordUuid)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found"));

        var unpaidFees = entityFeeService.findByOrderId(recordUuid.toString());

        if (unpaidFees.isEmpty()) {
            return "No fees calculated for this record";
        }

        BigDecimal totalAmount = unpaidFees.stream()
                .map(fee -> fee.getOutstandingAmount() != null ? fee.getOutstandingAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return String.format("Total unpaid fees: $%s for %d fees", totalAmount, unpaidFees.size());
    }

    public List<RecordFeeCalculationResult> calculateRecordFeesUsingRules(UUID recordUuid) {
        log.info("Calculating fees for record using fee rules: {}", recordUuid);

        Record record = recordService.findRecordEntityByUuid(recordUuid)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found"));

        return feeRuleEngine.calculateFees(record);
    }

    public CartInvoiceResponse calculateRecordFeesViaCalculationService(UUID recordUuid) {
        log.info("Calculating fees for record via Calculation Service: {}", recordUuid);

        Record record = recordService.findRecordEntityByUuid(recordUuid)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found"));

        List<RecordFeeCalculationResult> calculatedFees = feeRuleEngine.calculateFees(record);

        List<String> applicableFeeCodes = calculatedFees.stream()
                .filter(RecordFeeCalculationResult::isApplied)
                .map(RecordFeeCalculationResult::getFeeCode)
                .toList();

        if (applicableFeeCodes.isEmpty()) {
            log.info("No fees applicable for record: {}", recordUuid);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "No fees applicable for this record");
        }

        log.info("Applicable fee codes for record {}: {}", recordUuid, applicableFeeCodes);

        var request = new GetCartItemInvoiceQuery.AddItemRequest();
        request.setItemType("record");
        request.setItemId(recordUuid);
        request.setName(record.getRecordName());
        request.setDescription(record.getRecordType() != null ? record.getRecordType().getTypeName() : "Record");
        request.setFees(applicableFeeCodes); // Fee codes from rule engine

        var query = new GetCartItemInvoiceQuery(List.of(request));
        CartInvoiceResponse invoice = amqpGateway.queryResult(query)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Failed to get cart invoice from Calculation Service"));

        log.info("Successfully calculated fees via Calculation Service for record: {}", recordUuid);

        return invoice;
    }

    public String calculateAndCreateFeesLocally(UUID recordUuid) {
        log.info("Calculating and creating fees locally for record: {}", recordUuid);

        Record record = recordService.findRecordEntityByUuid(recordUuid)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found"));

        List<RecordFeeCalculationResult> calculatedFees = feeRuleEngine.calculateFees(record);

        List<RecordFeeCalculationResult> appliedFees = calculatedFees.stream()
                .filter(RecordFeeCalculationResult::isApplied)
                .toList();

        if (appliedFees.isEmpty()) {
            log.info("No fees applicable for record: {}", recordUuid);
            return "No fees applicable for this record";
        }

        int createdCount = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (RecordFeeCalculationResult feeResult : appliedFees) {
            EntityFeeRequest feeRequest = new EntityFeeRequest();
            feeRequest.setFeeName(feeResult.getFeeName());
            feeRequest.setFeeAmount(feeResult.getAmount());
            feeRequest.setOrderId(recordUuid.toString());
            feeRequest.setDueDate(Instant.now().plus(30, ChronoUnit.DAYS)); // 30 days to pay
            feeRequest.setIsRecurring(false);

            entityFeeService.createEntityFee(feeRequest, "fee-rule-engine");
            createdCount++;
            totalAmount = totalAmount.add(feeResult.getAmount());

            log.info("Created local fee: {} - ${}", feeResult.getFeeCode(), feeResult.getAmount());
        }

        return String.format("Created %d fees locally totaling $%s", createdCount, totalAmount);
    }

    public String calculateRecordFees(UUID recordUuid) {
        log.info("Calculating fees for record: {}", recordUuid);

        var record = recordService.findRecordByUuid(recordUuid)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found"));

        try {
            var request = new GetCartItemInvoiceQuery.AddItemRequest();
            request.setItemType("record");
            request.setItemId(recordUuid);
            request.setName(record.getRecordTypeName() + " Record");
            request.setDescription("Record fee calculation");
            request.setFees(List.of("RECORD_BASE_FEE")); // Default fee code

            var query = new GetCartItemInvoiceQuery(List.of(request));
            var invoice = amqpGateway.queryResult(query)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to get cart invoice"));

            BigDecimal totalAmount = invoice.getItems().stream()
                    .flatMap(item -> item.getFees().stream())
                    .map(fee -> fee.getFeeAmount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            return String.format("Calculated fees: $%s", totalAmount);
        } catch (Exception e) {
            log.error("Error calculating fees for record: {}", recordUuid, e);
            return "Error calculating fees: " + e.getMessage();
        }
    }

    public String reprocessRecordFees(UUID recordUuid) {
        log.info("Reprocessing fees for record: {}", recordUuid);

        String calculationResult = calculateRecordFees(recordUuid);

        return "Fees reprocessed: " + calculationResult;
    }

    public String getRecordActions(UUID recordUuid) {
        log.info("Getting available actions for record: {}", recordUuid);

        var record = recordService.findRecordByUuid(recordUuid)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found"));

        String status = record.getStatus();

        switch (status.toUpperCase()) {
            case "ACTIVE":
                return "Available actions: RENEW, EXPIRE, UPDATE";
            case "EXPIRED":
                return "Available actions: RENEW";
            case "PENDING":
                return "Available actions: APPROVE, REJECT";
            default:
                return "Available actions: VIEW";
        }
    }

    public boolean isRecordExpired(UUID recordUuid) {
        log.info("Checking if record is expired: {}", recordUuid);

        var record = recordService.findRecordByUuid(recordUuid)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found"));

        return "EXPIRED".equalsIgnoreCase(record.getStatus());
    }
}
