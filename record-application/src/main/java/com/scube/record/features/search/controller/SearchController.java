package com.scube.record.features.search.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.record.features.search.dto.*;
import com.scube.record.features.search.service.SearchService;
import com.scube.record.permission.Permissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.List;

@RestController
@RequestMapping("/api/v1/search")
@RequiredArgsConstructor
@Tag(name = "Search", description = "Generic search operations across all record types")
public class SearchController {

    private final SearchService searchService;

    @GetMapping("/global")
    @RolesAllowed(Permissions.Record.SEARCH_RECORDS)
    @Operation(summary = "Global search with record objects",
            description = "Search across all record types and return full record objects with exact match option")
    public ResponseEntity<List<SearchResult>> globalSearchRecords(
            @Parameter(description = "Search query") @RequestParam String query,
            @Parameter(description = "Exact match only") @RequestParam(defaultValue = "false") boolean exactMatch,
            @Parameter(description = "Record type code (optional)") @RequestParam(required = false) String recordTypeCode,
            @Parameter(description = "Maximum number of results") @RequestParam(defaultValue = "10") int limit) {

        List<SearchResult> results = searchService.globalSearchRecords(query, exactMatch, recordTypeCode, limit);
        return ResponseEntity.ok(results);
    }


    @GetMapping("/advanced")
    @RolesAllowed(Permissions.Record.SEARCH_RECORDS)
    @Operation(summary = "Advanced search with GET",
            description = "Advanced search with detailed filters using GET request and query parameters")
    public ResponseEntity<Page<SearchResult>> advancedSearchGet(
            @Parameter(description = "Record type code") @RequestParam(required = false) String recordTypeCode,
            @Parameter(description = "Record status (comma-separated)") @RequestParam(required = false) String status,
            @Parameter(description = "Created after (ISO date)") @RequestParam(required = false) String createdAfter,
            @Parameter(description = "Created before (ISO date)") @RequestParam(required = false) String createdBefore,
            @Parameter(description = "Modified after (ISO date)") @RequestParam(required = false) String modifiedAfter,
            @Parameter(description = "Modified before (ISO date)") @RequestParam(required = false) String modifiedBefore,
            @Parameter(description = "Field filters (JSON string)") @RequestParam(required = false) String fieldFilters,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {

        List<String> statusList = status != null ? List.of(status.split(",")) : null;

        Instant createdAfterInstant = parseInstant(createdAfter);
        Instant createdBeforeInstant = parseInstant(createdBefore);
        Instant modifiedAfterInstant = parseInstant(modifiedAfter);
        Instant modifiedBeforeInstant = parseInstant(modifiedBefore);

        JsonNode jsonFilters = null;
        if (fieldFilters != null && !fieldFilters.trim().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                jsonFilters = mapper.readTree(fieldFilters);
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid fieldFilters JSON: " + e.getMessage());
            }
        }

        AdvancedSearchRequest request = new AdvancedSearchRequest(
                recordTypeCode,
                null, // fieldFilters
                jsonFilters,
                statusList,
                createdAfterInstant,
                createdBeforeInstant,
                modifiedAfterInstant,
                modifiedBeforeInstant,
                null, // createdBy
                null, // lastModifiedBy
                "createdAt",
                "DESC",
                page,
                size
        );
        Page<SearchResult> results = searchService.advancedSearch(request);
        return ResponseEntity.ok(results);
    }

    private Instant parseInstant(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        try {
            return Instant.parse(dateString);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format: " + dateString + ". Expected ISO format.", e);
        }
    }
}