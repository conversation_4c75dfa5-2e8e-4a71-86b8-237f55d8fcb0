package com.scube.record.features.module.loader;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.record.features.module.service.ModuleSyncService;
import com.scube.record.infrastructure.db.entity.module.ModuleDefinition;
import com.scube.record.infrastructure.db.repository.module.ModuleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class ModuleAutoLoader implements CommandLineRunner {

    private final ModuleRepository moduleRepository;
    private final ModuleSyncService moduleSyncService;
    private final ObjectMapper objectMapper;

    @Value("${module.config.enabled:true}")
    private boolean moduleSystemEnabled;

    @Value("${module.config.auto-load:true}")
    private boolean autoLoad;

    @Value("${module.config.directory:classpath:modules/}")
    private String moduleDirectory;

    @Value("${module.config.update-existing:false}")
    private boolean updateExisting;

    @Override
    public void run(String... args) throws Exception {
        if (!moduleSystemEnabled) {
            log.info("Module system is disabled. Skipping module auto-loading.");
            return;
        }

        if (!autoLoad) {
            log.info("Module auto-loading is disabled. Skipping.");
            return;
        }

        log.info("========================================");
        log.info("Starting Module Auto-Loader");
        log.info("========================================");
        log.info("Module directory: {}", moduleDirectory);
        log.info("Update existing: {}", updateExisting);

        try {
            loadModulesFromDirectory();
            log.info("========================================");
            log.info("Module Auto-Loader completed successfully");
            log.info("========================================");
        } catch (Exception e) {
            log.error("========================================");
            log.error("Error during module auto-loading", e);
            log.error("========================================");
        }
    }

    private void loadModulesFromDirectory() throws Exception {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources(moduleDirectory + "*.json");

        log.info("Found {} module files in directory", resources.length);

        if (resources.length == 0) {
            log.warn("No module files found in: {}", moduleDirectory);
            log.warn("Create module JSON files in src/main/resources/modules/ directory");
            return;
        }

        int loadedCount = 0;
        int skippedCount = 0;
        int updatedCount = 0;
        int errorCount = 0;

        for (Resource resource : resources) {
            try {
                String filename = resource.getFilename();
                log.info("----------------------------------------");
                log.info("Processing module file: {}", filename);

                boolean result = loadModule(resource);

                if (result) {
                    loadedCount++;
                } else {
                    skippedCount++;
                }

            } catch (Exception e) {
                errorCount++;
                log.error("Failed to load module from {}: {}", 
                    resource.getFilename(), e.getMessage(), e);
            }
        }

        log.info("----------------------------------------");
        log.info("Module loading summary:");
        log.info("  - Total files: {}", resources.length);
        log.info("  - Loaded: {}", loadedCount);
        log.info("  - Skipped: {}", skippedCount);
        log.info("  - Errors: {}", errorCount);
        log.info("----------------------------------------");
    }

    private boolean loadModule(Resource resource) throws Exception {
        String filename = resource.getFilename();

        String json = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        JsonNode moduleConfig = objectMapper.readTree(json);

        if (!moduleConfig.has("moduleCode")) {
            log.error("Module file {} is missing required field: moduleCode", filename);
            throw new IllegalArgumentException("Missing required field: moduleCode");
        }

        String moduleCode = moduleConfig.get("moduleCode").asText();
        String moduleName = moduleConfig.has("moduleName") ? 
            moduleConfig.get("moduleName").asText() : moduleCode;
        String version = moduleConfig.has("version") ? 
            moduleConfig.get("version").asText() : "1.0.0";

        log.info("Module code: {}", moduleCode);
        log.info("Module name: {}", moduleName);
        log.info("Version: {}", version);

        Optional<ModuleDefinition> existingModule = moduleRepository.findByModuleCode(moduleCode);

        if (existingModule.isPresent()) {
            if (updateExisting) {
                log.info("Module {} already exists. Updating...", moduleCode);
                return updateModule(existingModule.get(), moduleConfig, moduleName, version);
            } else {
                log.info("Module {} already exists. Skipping (update-existing=false)", moduleCode);
                return false;
            }
        }

        return createModule(moduleCode, moduleName, version, moduleConfig);
    }

    private boolean createModule(String moduleCode, String moduleName, String version, JsonNode config) {
        log.info("Creating new module: {}", moduleCode);

        ModuleDefinition module = new ModuleDefinition();
        module.setModuleCode(moduleCode);
        module.setModuleName(moduleName);
        module.setVersion(version);
        module.setConfig(config);
        module.setIsActive(true);

        if (config.has("description")) {
            module.setDescription(config.get("description").asText());
        }

        module.setCreatedBy("module-auto-loader");
        module.setLastModifiedBy("module-auto-loader");

        module = moduleRepository.save(module);
        log.info("✓ Module definition saved with ID: {}", module.getModuleId());

        try {
            moduleSyncService.syncModuleToDatabase(module);
            log.info("✓ Module synced to database successfully");
            
            var stats = moduleSyncService.getModuleSyncStats(moduleCode);
            log.info("✓ Created {} record types", stats.get("recordTypeCount"));
            
            return true;
        } catch (Exception e) {
            log.error("✗ Failed to sync module to database: {}", e.getMessage(), e);
            moduleRepository.delete(module);
            throw new RuntimeException("Failed to sync module: " + moduleCode, e);
        }
    }

    private boolean updateModule(ModuleDefinition existingModule, JsonNode newConfig, 
                                  String moduleName, String version) {
        log.info("Updating existing module: {}", existingModule.getModuleCode());

        existingModule.setModuleName(moduleName);
        existingModule.setVersion(version);
        existingModule.setConfig(newConfig);

        if (newConfig.has("description")) {
            existingModule.setDescription(newConfig.get("description").asText());
        }

        existingModule.setLastModifiedBy("module-auto-loader");

        existingModule = moduleRepository.save(existingModule);
        log.info("✓ Module definition updated");

        try {
            moduleSyncService.syncModuleToDatabase(existingModule);
            log.info("✓ Module re-synced to database successfully");
            
            var stats = moduleSyncService.getModuleSyncStats(existingModule.getModuleCode());
            log.info("✓ Updated {} record types", stats.get("recordTypeCount"));
            
            return true;
        } catch (Exception e) {
            log.error("✗ Failed to re-sync module to database: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to re-sync module: " + existingModule.getModuleCode(), e);
        }
    }
}

