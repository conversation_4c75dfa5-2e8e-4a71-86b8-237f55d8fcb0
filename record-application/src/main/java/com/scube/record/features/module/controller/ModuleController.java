package com.scube.record.features.module.controller;

import com.scube.record.features.module.dto.CreateModuleRequest;
import com.scube.record.features.module.dto.ModuleResponse;
import com.scube.record.features.module.dto.UpdateModuleRequest;
import com.scube.record.features.module.service.ModuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/config/modules")
@RequiredArgsConstructor
@Tag(name = "Module Management", description = "Manage module configurations")
public class ModuleController {

    private final ModuleService moduleService;

    @PostMapping
    @Operation(summary = "Create module", description = "Create a new module from JSON configuration")
    public ResponseEntity<ModuleResponse> createModule(
            @Valid @RequestBody CreateModuleRequest request) {
        ModuleResponse response = moduleService.createModule(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{moduleUuid}")
    @Operation(summary = "Update module", description = "Update an existing module configuration")
    public ResponseEntity<ModuleResponse> updateModule(
            @PathVariable UUID moduleUuid,
            @Valid @RequestBody UpdateModuleRequest request) {
        ModuleResponse response = moduleService.updateModule(moduleUuid, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{moduleUuid}")
    @Operation(summary = "Get module by UUID", description = "Retrieve module configuration by UUID")
    public ResponseEntity<ModuleResponse> getModuleByUuid(
            @PathVariable UUID moduleUuid) {
        ModuleResponse response = moduleService.getModuleByUuid(moduleUuid);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/code/{moduleCode}")
    @Operation(summary = "Get module by code", description = "Retrieve module configuration by module code")
    public ResponseEntity<ModuleResponse> getModuleByCode(
            @PathVariable String moduleCode) {
        ModuleResponse response = moduleService.getModuleByCode(moduleCode);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(summary = "Get all modules", description = "Retrieve all modules with pagination")
    public ResponseEntity<Page<ModuleResponse>> getAllModules(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ModuleResponse> modules = moduleService.getAllModules(pageable);
        return ResponseEntity.ok(modules);
    }

    @GetMapping("/active")
    @Operation(summary = "Get active modules", description = "Retrieve all active modules with pagination")
    public ResponseEntity<Page<ModuleResponse>> getActiveModules(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ModuleResponse> modules = moduleService.getActiveModules(pageable);
        return ResponseEntity.ok(modules);
    }

    @GetMapping("/search")
    @Operation(summary = "Search modules", description = "Search modules by keyword in name, code, or description")
    public ResponseEntity<Page<ModuleResponse>> searchModules(
            @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ModuleResponse> modules = moduleService.searchModules(keyword, pageable);
        return ResponseEntity.ok(modules);
    }

    @DeleteMapping("/{moduleUuid}")
    @Operation(summary = "Delete module", description = "Delete a module and all its associated record types")
    public ResponseEntity<Void> deleteModule(@PathVariable UUID moduleUuid) {
        moduleService.deleteModule(moduleUuid);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{moduleUuid}/resync")
    @Operation(summary = "Re-sync module", description = "Re-sync module configuration to database (update record types)")
    public ResponseEntity<ModuleResponse> resyncModule(@PathVariable UUID moduleUuid) {
        ModuleResponse response = moduleService.resyncModule(moduleUuid);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{moduleUuid}/activate")
    @Operation(summary = "Activate module", description = "Activate a module")
    public ResponseEntity<ModuleResponse> activateModule(@PathVariable UUID moduleUuid) {
        ModuleResponse response = moduleService.toggleModuleStatus(moduleUuid, true);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{moduleUuid}/deactivate")
    @Operation(summary = "Deactivate module", description = "Deactivate a module")
    public ResponseEntity<ModuleResponse> deactivateModule(@PathVariable UUID moduleUuid) {
        ModuleResponse response = moduleService.toggleModuleStatus(moduleUuid, false);
        return ResponseEntity.ok(response);
    }
}

