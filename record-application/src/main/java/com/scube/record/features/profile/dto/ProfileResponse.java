package com.scube.record.features.profile.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.record.features.association.dto.AssociationResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProfileResponse {

    private Long recordId;
    private UUID recordUuid;
    private String recordName;
    private String status;
    private JsonNode properties;
    private String recordTypeCode;
    private String recordTypeName;
    private JsonNode recordTypeProperties;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    private List<AssociationResponse> associations;
    private List<RelatedRecord> relatedRecords;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RelatedRecord {
        private Long recordId;
        private UUID recordUuid;
        private String recordName;
        private String recordTypeCode;
        private String recordTypeName;
        private String relationshipType;
        private String relationshipDirection;
        private int relationshipDepth;
    }
}