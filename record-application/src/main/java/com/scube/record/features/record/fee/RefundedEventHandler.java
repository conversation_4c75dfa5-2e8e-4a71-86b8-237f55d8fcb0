package com.scube.record.features.record.fee;

import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
public class RefundedEventHandler extends FanoutListener<RefundedEventHandler.RefundedEvent> {

    @Override
    public void consume(RefundedEvent event) {
        log.info("Processing RefundedEvent for orderId: {}, amount: {}", 
                event.orderId(), event.refundedAmount());
        
        
        log.info("Refund processed for order: {}", event.orderId());
    }

    public record RefundedEvent(UUID orderId, BigDecimal refundedAmount) implements IRabbitFanoutSubscriber {
    }
}
