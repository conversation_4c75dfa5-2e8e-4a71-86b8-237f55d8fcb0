package com.scube.record.features.entity_group.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityGroupRequest {

    @NotNull(message = "Group name is required")
    private String groupName;

    @NotNull(message = "Group type is required")
    private String groupType;

    private String description;

    private Boolean isActive;

    private Map<String, Object> customFields;

    private Map<String, Object> properties;
}