package com.scube.record.features.code_lookup.dto;

import com.scube.record.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CodeLookupResponseLineItem {
    private String code;
    private CodeLookupActionEnum action;
    private String entityType;
    private String entityId;
    private String realm;
}