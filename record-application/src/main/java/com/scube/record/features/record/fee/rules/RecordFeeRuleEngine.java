package com.scube.record.features.record.fee.rules;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class RecordFeeRuleEngine {
    
    private final ObjectMapper objectMapper;
    private final ExpressionParser parser = new SpelExpressionParser();
    
    public List<RecordFeeCalculationResult> calculateFees(Record record) {
        RecordType recordType = record.getRecordType();
        
        if (recordType == null) {
            log.warn("Record {} has no record type", record.getRecordUuid());
            return List.of();
        }
        
        List<FeeRule> feeRules = extractFeeRules(recordType);
        
        if (feeRules.isEmpty()) {
            log.debug("No fee rules configured for record type: {}", recordType.getTypeCode());
            return List.of();
        }
        
        log.info("Calculating fees for record {} using {} fee rules", 
                record.getRecordUuid(), feeRules.size());
        
        StandardEvaluationContext context = buildEvaluationContext(record, recordType);
        
        List<RecordFeeCalculationResult> results = new ArrayList<>();
        
        for (FeeRule rule : feeRules) {
            try {
                RecordFeeCalculationResult result = evaluateFeeRule(rule, context);
                results.add(result);
                
                if (result.isApplied()) {
                    log.debug("Fee {} applied: ${}", result.getFeeCode(), result.getAmount());
                } else {
                    log.debug("Fee {} not applied (condition: {})", 
                            result.getFeeCode(), result.getConditionEvaluated());
                }
            } catch (Exception e) {
                log.error("Error evaluating fee rule {}: {}", rule.getFeeCode(), e.getMessage(), e);
            }
        }
        
        return results;
    }
    
    private List<FeeRule> extractFeeRules(RecordType recordType) {
        JsonNode configJson = recordType.getConfigJson();
        
        if (configJson == null || !configJson.has("feeRules")) {
            return List.of();
        }
        
        try {
            JsonNode feeRulesNode = configJson.get("feeRules");
            
            if (!feeRulesNode.isArray()) {
                log.warn("feeRules is not an array in record type: {}", recordType.getTypeCode());
                return List.of();
            }
            
            List<FeeRule> rules = new ArrayList<>();
            for (JsonNode ruleNode : feeRulesNode) {
                FeeRule rule = objectMapper.treeToValue(ruleNode, FeeRule.class);
                rules.add(rule);
            }
            
            return rules;
        } catch (Exception e) {
            log.error("Failed to parse fee rules for record type {}: {}", 
                    recordType.getTypeCode(), e.getMessage(), e);
            return List.of();
        }
    }
    
    private StandardEvaluationContext buildEvaluationContext(Record record, RecordType recordType) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        
        context.setVariable("record", record);
        context.setVariable("recordType", recordType);
        
        if (record.getProperties() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> propertiesMap = objectMapper.convertValue(
                        record.getProperties(), Map.class);
                context.setVariable("properties", propertiesMap);
            } catch (Exception e) {
                log.warn("Failed to convert properties to Map: {}", e.getMessage());
                context.setVariable("properties", Map.of());
            }
        } else {
            context.setVariable("properties", Map.of());
        }
        
        context.setVariable("Math", Math.class);
        
        try {
            context.registerFunction("coalesce", 
                RecordFeeRuleEngine.class.getMethod("coalesce", Object.class, Object.class));
            context.registerFunction("equalsIgnoreCase", 
                RecordFeeRuleEngine.class.getMethod("equalsIgnoreCase", String.class, String.class));
        } catch (NoSuchMethodException e) {
            log.error("Failed to register helper functions", e);
        }
        
        return context;
    }
    
    private RecordFeeCalculationResult evaluateFeeRule(FeeRule rule, StandardEvaluationContext context) {
        RecordFeeCalculationResult.RecordFeeCalculationResultBuilder resultBuilder = 
                RecordFeeCalculationResult.builder()
                        .feeCode(rule.getFeeCode())
                        .feeName(rule.getFeeName())
                        .description(rule.getDescription())
                        .conditionEvaluated(rule.getCondition());
        
        boolean conditionMet = evaluateCondition(rule.getCondition(), context);
        resultBuilder.applied(conditionMet);
        
        if (!conditionMet) {
            resultBuilder.amount(BigDecimal.ZERO);
            return resultBuilder.build();
        }
        
        BigDecimal amount = calculateAmount(rule, context);
        resultBuilder.amount(amount);
        
        return resultBuilder.build();
    }
    
    private boolean evaluateCondition(String condition, StandardEvaluationContext context) {
        if (condition == null || condition.trim().isEmpty() || condition.equalsIgnoreCase("true")) {
            return true; // No condition means always apply
        }
        
        try {
            Boolean result = parser.parseExpression(condition).getValue(context, Boolean.class);
            return result != null && result;
        } catch (Exception e) {
            log.error("Failed to evaluate condition: {} - Error: {}", condition, e.getMessage());
            return false;
        }
    }
    
    private BigDecimal calculateAmount(FeeRule rule, StandardEvaluationContext context) {
        if (rule.isAmountExpression()) {
            String expression = rule.getAmountAsExpression();
            try {
                Object result = parser.parseExpression(expression).getValue(context);
                
                if (result instanceof Number) {
                    return BigDecimal.valueOf(((Number) result).doubleValue());
                } else {
                    log.error("Amount expression did not return a number: {}", expression);
                    return BigDecimal.ZERO;
                }
            } catch (Exception e) {
                log.error("Failed to evaluate amount expression: {} - Error: {}", 
                        expression, e.getMessage());
                return BigDecimal.ZERO;
            }
        } else {
            BigDecimal amount = rule.getAmountAsDecimal();
            return amount != null ? amount : BigDecimal.ZERO;
        }
    }
    
    public static Object coalesce(Object value, Object defaultValue) {
        return value != null ? value : defaultValue;
    }
    
    public static boolean equalsIgnoreCase(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return str1 == str2;
        }
        return str1.equalsIgnoreCase(str2);
    }
}

