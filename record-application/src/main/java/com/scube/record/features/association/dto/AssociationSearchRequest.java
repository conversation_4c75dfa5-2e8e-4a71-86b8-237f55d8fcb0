package com.scube.record.features.association.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssociationSearchRequest {

    private String parentType;
    private String childType;
    private Long parentId;
    private Long childId;
    private String associationTypeId;
    private String sortBy = "createdAt";
    private String sortDirection = "DESC";
    private Integer page = 0;
    private Integer size = 20;
}