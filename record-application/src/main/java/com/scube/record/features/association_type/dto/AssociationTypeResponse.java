package com.scube.record.features.association_type.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssociationTypeResponse {

    private Long associationTypeId;
    private String associationTypeUuid;
    private String associationName;
    private String typeCode;
    private String description;
    private List<String> parentRecordTypes;
    private List<String> childRecordTypes;
    private JsonNode properties;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
}