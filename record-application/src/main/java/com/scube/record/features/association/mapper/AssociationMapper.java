package com.scube.record.features.association.mapper;

import com.scube.record.infrastructure.db.entity.record.Association;
import com.scube.record.features.association.dto.AssociationResponse;
import com.scube.record.features.association.dto.CreateAssociationRequest;
import com.scube.record.features.association.dto.UpdateAssociationRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE
)
public interface AssociationMapper {

    @Mapping(source = "createdBy", target = "lastModifiedBy")
    Association toEntity(CreateAssociationRequest request);

    AssociationResponse toResponse(Association association);

    void updateEntity(@MappingTarget Association association, UpdateAssociationRequest request);
}