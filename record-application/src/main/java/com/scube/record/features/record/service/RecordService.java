package com.scube.record.features.record.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.dto.UpdateRecordRequest;
import com.scube.record.features.record.exception.InvalidRecordTypeException;
import com.scube.record.features.record.exception.RecordNotFoundException;
import com.scube.record.features.record.mapper.RecordMapper;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RecordService {

    private final RecordRepository recordRepository;
    private final RecordTypeRepository recordTypeRepository;
    private final RecordMapper recordMapper;
    private final RecordAssociationService recordAssociationService;
    private final ObjectMapper objectMapper;

    private JsonNode normalizeProperties(JsonNode recordTypeProperties, JsonNode requestProperties) {
        if (recordTypeProperties == null || recordTypeProperties.isNull() || !recordTypeProperties.isObject()) {
            return requestProperties;
        }

        ObjectNode normalizedProperties = objectMapper.createObjectNode();
        Iterator<String> fieldNames = recordTypeProperties.fieldNames();

        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            if (requestProperties != null && requestProperties.has(fieldName)) {
                normalizedProperties.set(fieldName, requestProperties.get(fieldName));
            } else {
                normalizedProperties.putNull(fieldName);
            }
        }

        return normalizedProperties;
    }

    @Transactional
    public RecordResponse createRecord(CreateRecordRequest request) {
        RecordType recordType = recordTypeRepository.findByTypeCode(request.getRecordTypeCode())
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + request.getRecordTypeCode()));

        Record record = recordMapper.toEntity(request);
        record.setRecordType(recordType);

        JsonNode normalizedProperties = normalizeProperties(
            recordType.getProperties(),
            request.getProperties()
        );
        record.setProperties(convertJsonNodeToMap(normalizedProperties));

        Record savedRecord = recordRepository.save(record);
        recordAssociationService.createAssociations(savedRecord, request.getAssociations(), request.getCreatedBy());

        return recordMapper.toResponse(savedRecord);
    }

    @Transactional
    public RecordResponse updateRecord(UUID recordUuid, UpdateRecordRequest request) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));

        recordMapper.updateEntity(record, request);

        if (request.getProperties() != null && record.getRecordType() != null) {
            JsonNode normalizedProperties = normalizeProperties(
                record.getRecordType().getProperties(),
                request.getProperties()
            );
            record.setProperties(convertJsonNodeToMap(normalizedProperties));
        }

        Record updatedRecord = recordRepository.save(record);
        return recordMapper.toResponse(updatedRecord);
    }

    public RecordResponse getRecordByUuid(UUID recordUuid) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));
        return recordMapper.toResponse(record);
    }

    public Optional<RecordResponse> findRecordByUuid(UUID recordUuid) {
        return recordRepository.findByRecordUuid(recordUuid)
            .map(recordMapper::toResponse);
    }

    public Optional<Record> findRecordEntityByUuid(UUID recordUuid) {
        return recordRepository.findByRecordUuid(recordUuid);
    }

    public Page<RecordResponse> getAllRecords(Pageable pageable) {
        Page<Record> records = recordRepository.findAll(pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    public List<RecordResponse> getRecordsByType(String recordTypeCode) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        return recordRepository.findByRecordType(recordType).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    public List<RecordResponse> getRecordsByStatus(String status) {
        return recordRepository.findByStatus(status).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    public Page<RecordResponse> getRecordsByTypeAndStatus(String recordTypeCode, String status, Pageable pageable) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        Page<Record> records = recordRepository.findByRecordTypeAndStatus(recordType, status, pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    public Page<RecordResponse> searchRecords(String keyword, Pageable pageable) {
        Page<Record> records = recordRepository.searchByKeyword(keyword, pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    public List<RecordResponse> searchRecordsByName(String recordName) {
        return recordRepository.findByRecordNameContaining(recordName).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    public List<String> getRecordNameSuggestions(String query, int limit) {
        return recordRepository.findRecordNameSuggestions(query, limit);
    }

    public List<String> getRecordNameSuggestionsByType(String query, String recordTypeCode, int limit) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        return recordRepository.findRecordNameSuggestionsByType(query, recordType.getRecordTypeId(), limit);
    }

    @Transactional
    public void deleteRecord(UUID recordUuid) {
        if (!recordRepository.existsByRecordUuid(recordUuid)) {
            throw new RecordNotFoundException("Record not found with UUID: " + recordUuid);
        }
        recordRepository.deleteByRecordUuid(recordUuid);
    }

    public boolean existsByUuid(UUID recordUuid) {
        return recordRepository.existsByRecordUuid(recordUuid);
    }

    public long countRecords() {
        return recordRepository.count();
    }

    public long countRecordsByType(String recordTypeCode) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));
        return recordRepository.findByRecordType(recordType).size();
    }

    public long countRecordsByStatus(String status) {
        return recordRepository.findByStatus(status).size();
    }

    public List<RecordResponse> getRecordFees(UUID recordUuid) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));

        return List.of();
    }

    private java.util.Map<String, Object> convertJsonNodeToMap(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }
        return objectMapper.convertValue(jsonNode, java.util.Map.class);
    }
}