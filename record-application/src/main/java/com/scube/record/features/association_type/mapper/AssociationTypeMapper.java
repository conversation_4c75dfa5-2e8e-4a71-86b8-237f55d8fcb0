package com.scube.record.features.association_type.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.record.features.association_type.dto.AssociationTypeResponse;
import com.scube.record.features.association_type.dto.CreateAssociationTypeRequest;
import com.scube.record.features.association_type.dto.UpdateAssociationTypeRequest;
import com.scube.record.infrastructure.db.entity.record.AssociationType;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE
)
public abstract class AssociationTypeMapper {

    @Autowired
    protected ObjectMapper objectMapper;

    @Mapping(target = "properties", ignore = true)
    @Mapping(source = "createdBy", target = "lastModifiedBy")
    public abstract AssociationType toEntity(CreateAssociationTypeRequest request);

    @AfterMapping
    protected void buildProperties(@MappingTarget AssociationType associationType, CreateAssociationTypeRequest request) {
        ObjectNode properties = objectMapper.createObjectNode();
        properties.put("type_code", request.getTypeCode());
        properties.put("description", request.getDescription());

        ArrayNode parentTypes = objectMapper.createArrayNode();
        request.getParentRecordTypes().forEach(parentTypes::add);
        properties.set("parent_record_types", parentTypes);

        ArrayNode childTypes = objectMapper.createArrayNode();
        request.getChildRecordTypes().forEach(childTypes::add);
        properties.set("child_record_types", childTypes);

        if (request.getAdditionalProperties() != null && request.getAdditionalProperties().isObject()) {
            request.getAdditionalProperties().fields().forEachRemaining(entry -> {
                properties.set(entry.getKey(), entry.getValue());
            });
        }

        associationType.setProperties(properties);
    }

    @Mapping(target = "typeCode", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "parentRecordTypes", ignore = true)
    @Mapping(target = "childRecordTypes", ignore = true)
    public abstract AssociationTypeResponse toResponse(AssociationType associationType);

    @AfterMapping
    protected void extractPropertiesFromJson(@MappingTarget AssociationTypeResponse response, AssociationType associationType) {
        List<String> parentRecordTypes = new ArrayList<>();
        List<String> childRecordTypes = new ArrayList<>();
        String typeCode = null;
        String description = null;

        if (associationType.getProperties() != null) {
            if (associationType.getProperties().has("type_code")) {
                typeCode = associationType.getProperties().get("type_code").asText();
            }

            if (associationType.getProperties().has("description")) {
                description = associationType.getProperties().get("description").asText();
            }

            if (associationType.getProperties().has("parent_record_types")) {
                associationType.getProperties().get("parent_record_types").forEach(node -> {
                    parentRecordTypes.add(node.asText());
                });
            }

            if (associationType.getProperties().has("child_record_types")) {
                associationType.getProperties().get("child_record_types").forEach(node -> {
                    childRecordTypes.add(node.asText());
                });
            }
        }

        response.setTypeCode(typeCode);
        response.setDescription(description);
        response.setParentRecordTypes(parentRecordTypes);
        response.setChildRecordTypes(childRecordTypes);
    }

    @Mapping(target = "properties", ignore = true)
    public abstract void updateEntity(@MappingTarget AssociationType associationType, UpdateAssociationTypeRequest request);

    @AfterMapping
    protected void updateProperties(@MappingTarget AssociationType associationType, UpdateAssociationTypeRequest request) {
        ObjectNode properties = (ObjectNode) associationType.getProperties();
        if (properties == null) {
            properties = objectMapper.createObjectNode();
        }

        final ObjectNode finalProperties = properties;

        if (request.getDescription() != null) {
            finalProperties.put("description", request.getDescription());
        }

        if (request.getParentRecordTypes() != null) {
            ArrayNode parentTypes = objectMapper.createArrayNode();
            request.getParentRecordTypes().forEach(parentTypes::add);
            finalProperties.set("parent_record_types", parentTypes);
        }

        if (request.getChildRecordTypes() != null) {
            ArrayNode childTypes = objectMapper.createArrayNode();
            request.getChildRecordTypes().forEach(childTypes::add);
            finalProperties.set("child_record_types", childTypes);
        }

        if (request.getAdditionalProperties() != null && request.getAdditionalProperties().isObject()) {
            request.getAdditionalProperties().fields().forEachRemaining(entry -> {
                finalProperties.set(entry.getKey(), entry.getValue());
            });
        }

        associationType.setProperties(finalProperties);
    }
}
