package com.scube.record.features.module.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.record.infrastructure.db.entity.module.ModuleDefinition;
import com.scube.record.infrastructure.db.entity.record.AssociationType;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import com.scube.record.infrastructure.db.repository.module.ModuleRepository;
import com.scube.record.infrastructure.db.repository.record.AssociationTypeRepository;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class ModuleSyncService {

    private final RecordTypeRepository recordTypeRepository;
    private final AssociationTypeRepository associationTypeRepository;
    private final ModuleRepository moduleRepository;
    private final ObjectMapper objectMapper;

    @Transactional
    public void syncModuleToDatabase(ModuleDefinition module) {
        log.info("Starting sync for module: {}", module.getModuleCode());

        JsonNode config = module.getConfig();

        if (config.has("recordTypes")) {
            syncRecordTypes(config.get("recordTypes"), module.getModuleCode());
        }

        if (config.has("associationTypes")) {
            syncAssociationTypes(config.get("associationTypes"), module.getModuleCode());
        }

        log.info("Successfully synced module: {}", module.getModuleCode());
    }

    private void syncRecordTypes(JsonNode recordTypesNode, String moduleCode) {
        if (recordTypesNode == null || !recordTypesNode.isArray()) {
            log.warn("No record types found in module: {}", moduleCode);
            return;
        }

        log.info("Syncing {} record types for module: {}", recordTypesNode.size(), moduleCode);

        Map<String, RecordType> recordTypeMap = new HashMap<>();

        for (JsonNode rtNode : recordTypesNode) {
            String typeCode = rtNode.get("typeCode").asText();

            RecordType recordType = recordTypeRepository.findByTypeCode(typeCode)
                    .orElse(new RecordType());

            recordType.setTypeCode(typeCode);
            recordType.setTypeName(rtNode.get("typeName").asText());
            recordType.setModuleCode(moduleCode);

            if (rtNode.has("description") && !rtNode.get("description").isNull()) {
                recordType.setDescription(rtNode.get("description").asText());
            }

            if (rtNode.has("properties")) {
                recordType.setProperties(rtNode.get("properties"));
            }

            if (rtNode.has("config_json")) {
                recordType.setConfigJson(rtNode.get("config_json"));
            }

            if (recordType.getRecordTypeId() == null) {
                recordType.setCreatedBy("module-system");
                recordType.setLastModifiedBy("module-system");
            } else {
                recordType.setLastModifiedBy("module-system");
            }

            recordType = recordTypeRepository.save(recordType);
            recordTypeMap.put(typeCode, recordType);

            log.debug("Synced record type: {}", typeCode);
        }

        for (JsonNode rtNode : recordTypesNode) {
            if (rtNode.has("parentTypeCode") && !rtNode.get("parentTypeCode").isNull()) {
                String typeCode = rtNode.get("typeCode").asText();
                String parentCode = rtNode.get("parentTypeCode").asText();

                RecordType recordType = recordTypeMap.get(typeCode);
                RecordType parent = recordTypeMap.get(parentCode);

                if (parent == null) {
                    parent = recordTypeRepository.findByTypeCode(parentCode).orElse(null);
                }

                if (parent != null) {
                    recordType.setParent(parent);
                    recordTypeRepository.save(recordType);
                    log.debug("Set parent {} for record type: {}", parentCode, typeCode);
                } else {
                    log.warn("Parent record type not found: {} for child: {}", parentCode, typeCode);
                }
            }
        }

        log.info("Successfully synced {} record types", recordTypeMap.size());
    }

    private void syncAssociationTypes(JsonNode associationTypesNode, String moduleCode) {
        if (associationTypesNode == null || !associationTypesNode.isArray()) {
            log.warn("No association types found in module: {}", moduleCode);
            return;
        }

        log.info("Syncing {} association types for module: {}", associationTypesNode.size(), moduleCode);

        for (JsonNode atNode : associationTypesNode) {
            String associationName = atNode.get("associationName").asText();

            AssociationType associationType = associationTypeRepository
                    .findByAssociationName(associationName)
                    .orElse(new AssociationType());

            associationType.setAssociationName(associationName);

            ObjectNode properties = objectMapper.createObjectNode();

            if (atNode.has("description")) {
                properties.put("description", atNode.get("description").asText());
            }
            if (atNode.has("parentType")) {
                properties.put("parentType", atNode.get("parentType").asText());
            }
            if (atNode.has("childType")) {
                properties.put("childType", atNode.get("childType").asText());
            }
            if (atNode.has("cardinality")) {
                properties.put("cardinality", atNode.get("cardinality").asText());
            }
            if (atNode.has("required")) {
                properties.put("required", atNode.get("required").asBoolean());
            }

            properties.put("moduleCode", moduleCode);

            associationType.setProperties(properties);

            if (associationType.getAssociationTypeId() == null) {
                associationType.setCreatedBy("module-system");
                associationType.setLastModifiedBy("module-system");
            } else {
                associationType.setLastModifiedBy("module-system");
            }

            associationTypeRepository.save(associationType);
            log.debug("Synced association type: {}", associationName);
        }

        log.info("Successfully synced association types");
    }

    @Transactional
    public void deleteModuleData(String moduleCode) {
        log.info("Deleting all data for module: {}", moduleCode);

        var recordTypes = recordTypeRepository.findByModuleCode(moduleCode);

        log.info("Found {} record types to delete for module: {}", recordTypes.size(), moduleCode);

        for (RecordType recordType : recordTypes) {
            log.debug("Deleting record type: {}", recordType.getTypeCode());
            recordTypeRepository.delete(recordType);
        }


        log.info("Successfully deleted data for module: {}", moduleCode);
    }

    @Transactional
    public void resyncModule(String moduleCode) {
        log.info("Re-syncing module: {}", moduleCode);

        ModuleDefinition module = moduleRepository.findByModuleCode(moduleCode)
                .orElseThrow(() -> new IllegalArgumentException("Module not found: " + moduleCode));

        syncModuleToDatabase(module);

        log.info("Successfully re-synced module: {}", moduleCode);
    }

    public Map<String, Object> getModuleSyncStats(String moduleCode) {
        Map<String, Object> stats = new HashMap<>();

        long recordTypeCount = recordTypeRepository.countByModuleCode(moduleCode);
        stats.put("recordTypeCount", recordTypeCount);
        stats.put("moduleCode", moduleCode);

        return stats;
    }
}

