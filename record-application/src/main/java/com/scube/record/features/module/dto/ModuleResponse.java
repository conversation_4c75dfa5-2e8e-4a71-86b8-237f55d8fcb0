package com.scube.record.features.module.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModuleResponse {

    private Long moduleId;
    private UUID moduleUuid;
    private String moduleCode;
    private String moduleName;
    private String description;
    private String version;
    private JsonNode config;
    private Boolean isActive;
    private Instant createdAt;
    private String createdBy;
    private Instant lastModifiedAt;
    private String lastModifiedBy;
    
    private Long recordTypeCount;
}

