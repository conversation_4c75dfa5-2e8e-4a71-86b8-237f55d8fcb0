package com.scube.record.features.entity_group.controller;

import com.scube.record.features.entity_group.dto.EntityGroupDto;
import com.scube.record.features.entity_group.dto.EntityGroupRequest;
import com.scube.record.features.entity_group.service.EntityGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/entity-groups")
@RequiredArgsConstructor
public class EntityGroupController {

    private final EntityGroupService entityGroupService;

    @PostMapping
    public ResponseEntity<EntityGroupDto> createEntityGroup(
        @Valid @RequestBody EntityGroupRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityGroupDto created = entityGroupService.createEntityGroup(request, userId);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @GetMapping("/{entityGroupId}")
    public ResponseEntity<EntityGroupDto> getEntityGroupById(@PathVariable Long entityGroupId) {
        return entityGroupService.findById(entityGroupId)
            .map(entityGroup -> ResponseEntity.ok(entityGroup))
            .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/name/{groupName}")
    public ResponseEntity<EntityGroupDto> getEntityGroupByName(@PathVariable String groupName) {
        return entityGroupService.findByGroupName(groupName)
            .map(entityGroup -> ResponseEntity.ok(entityGroup))
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{entityGroupId}")
    public ResponseEntity<EntityGroupDto> updateEntityGroup(
        @PathVariable Long entityGroupId,
        @Valid @RequestBody EntityGroupRequest request,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        EntityGroupDto updated = entityGroupService.updateEntityGroup(entityGroupId, request, userId);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{entityGroupId}")
    public ResponseEntity<Void> deleteEntityGroup(@PathVariable Long entityGroupId) {
        entityGroupService.deleteEntityGroup(entityGroupId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    public ResponseEntity<Page<EntityGroupDto>> getAllActiveEntityGroups(Pageable pageable) {
        Page<EntityGroupDto> entityGroups = entityGroupService.findAllActiveEntityGroups(pageable);
        return ResponseEntity.ok(entityGroups);
    }

    @GetMapping("/type/{groupType}")
    public ResponseEntity<List<EntityGroupDto>> getEntityGroupsByType(@PathVariable String groupType) {
        List<EntityGroupDto> entityGroups = entityGroupService.findByGroupType(groupType);
        return ResponseEntity.ok(entityGroups);
    }
}