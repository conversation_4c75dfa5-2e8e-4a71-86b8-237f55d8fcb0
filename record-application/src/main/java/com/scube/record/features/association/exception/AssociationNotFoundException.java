package com.scube.record.features.association.exception;

public class AssociationNotFoundException extends RuntimeException {

    public AssociationNotFoundException(String message) {
        super(message);
    }

    public AssociationNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public static AssociationNotFoundException byId(Long id) {
        return new AssociationNotFoundException("Association not found with id: " + id);
    }

    public static AssociationNotFoundException byUuid(String uuid) {
        return new AssociationNotFoundException("Association not found with uuid: " + uuid);
    }
}