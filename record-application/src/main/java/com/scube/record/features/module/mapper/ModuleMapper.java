package com.scube.record.features.module.mapper;

import com.scube.record.features.module.dto.CreateModuleRequest;
import com.scube.record.features.module.dto.ModuleResponse;
import com.scube.record.infrastructure.db.entity.module.ModuleDefinition;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE
)
public interface ModuleMapper {

    @Mapping(target = "version", defaultValue = "1.0.0")
    @Mapping(target = "isActive", defaultValue = "true")
    @Mapping(target = "createdBy", constant = "api")
    @Mapping(target = "lastModifiedBy", constant = "api")
    ModuleDefinition toEntity(CreateModuleRequest request);

    ModuleResponse toResponse(ModuleDefinition module);

    default ModuleResponse toResponse(ModuleDefinition module, Long recordTypeCount) {
        ModuleResponse response = toResponse(module);
        response.setRecordTypeCount(recordTypeCount);
        return response;
    }
}