package com.scube.record.features.record.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecordAssociationRequest {

    @NotBlank(message = "Association type code is required")
    private String associationTypeCode;

    @NotBlank(message = "Associated record UUID is required")
    private String associatedRecordUuid;

    private String role;
}
