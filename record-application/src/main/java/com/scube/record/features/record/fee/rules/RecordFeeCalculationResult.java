package com.scube.record.features.record.fee.rules;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecordFeeCalculationResult {
    
    private String feeCode;
    private String feeName;
    private String description;
    private BigDecimal amount;
    private boolean applied;
    private String conditionEvaluated;
}

