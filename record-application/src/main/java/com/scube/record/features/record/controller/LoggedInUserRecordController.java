package com.scube.record.features.record.controller;

import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.dto.RecordSearchRequest;
import com.scube.record.features.record.dto.UpdateRecordRequest;
import com.scube.record.permission.Permissions;
import com.scube.record.features.record.service.RecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/me/records")
@RequiredArgsConstructor
@Tag(name = "Logged In User Record Management", description = "Record management operations for logged-in users")
public class LoggedInUserRecordController {

    private final RecordService recordService;

    @PostMapping
    @Operation(summary = "Create a new record", description = "Creates a new record of any type based on the record type definition")
    @RolesAllowed(Permissions.LoggedInUserRecord.CREATE_RECORD)
    public ResponseEntity<RecordResponse> createRecord(@Valid @RequestBody CreateRecordRequest request) {
        RecordResponse response = recordService.createRecord(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/{uuid}")
    @Operation(summary = "Get record by UUID", description = "Retrieves a record by its UUID")
    @RolesAllowed(Permissions.LoggedInUserRecord.GET_RECORD_BY_UUID)
    public ResponseEntity<RecordResponse> getRecordByUuid(
            @Parameter(description = "Record UUID") @PathVariable UUID uuid) {
        RecordResponse response = recordService.getRecordByUuid(uuid);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/type/{recordTypeCode}")
    @Operation(summary = "Get records by typecode", description = "Retrieves all records of a specific typecode")
    @RolesAllowed(Permissions.LoggedInUserRecord.GET_RECORD_TYPECODE)
    public ResponseEntity<List<RecordResponse>> getRecordsByType(
            @Parameter(description = "Record type code") @PathVariable String recordTypeCode) {
        List<RecordResponse> responses = recordService.getRecordsByType(recordTypeCode);
        return ResponseEntity.ok(responses);
    }

    @PutMapping("/{uuid}")
    @Operation(summary = "Update record by UUID", description = "Updates a record by its UUID")
    @RolesAllowed(Permissions.LoggedInUserRecord.UPDATE_RECORD_BY_UUID)
    public ResponseEntity<RecordResponse> updateRecordByUuid(
            @Parameter(description = "Record UUID") @PathVariable UUID uuid,
            @Valid @RequestBody UpdateRecordRequest request) {
        RecordResponse response = recordService.updateRecord(uuid, request);
        return ResponseEntity.ok(response);
    }


    @GetMapping("/status/{status}")
    @Operation(summary = "Get records by status", description = "Retrieves all records of a specific status")
    @RolesAllowed(Permissions.LoggedInUserRecord.GET_RECORD_STATUS)
    public ResponseEntity<List<RecordResponse>> getRecordsByStatus(
            @Parameter(description = "Record status") @PathVariable String status) {
        List<RecordResponse> responses = recordService.getRecordsByStatus(status);
        return ResponseEntity.ok(responses);
    }

@GetMapping("/fees/{recordUuid}")
    @Operation(summary = "Get record fees", description = "Retrieves fees for a record by UUID")
    @RolesAllowed(Permissions.LoggedInUserRecord.GET_RECORD_FEE)
    public ResponseEntity<List<RecordResponse>> getRecordFees(
            @Parameter(description = "Record UUID") @PathVariable UUID recordUuid) {
        List<RecordResponse> responses = recordService.getRecordFees(recordUuid);
        return ResponseEntity.ok(responses);
    }
}