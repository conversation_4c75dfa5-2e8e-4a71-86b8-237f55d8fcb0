package com.scube.record.features.search.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdvancedSearchRequest {

    private String recordTypeCode;
    private Map<String, Object> fieldFilters;
    private JsonNode jsonFilters;
    private List<String> statuses;
    private Instant createdAfter;
    private Instant createdBefore;
    private Instant modifiedAfter;
    private Instant modifiedBefore;
    private String createdBy;
    private String lastModifiedBy;
    private String sortBy = "createdAt";
    private String sortDirection = "DESC";
    private Integer page = 0;
    private Integer size = 20;
}