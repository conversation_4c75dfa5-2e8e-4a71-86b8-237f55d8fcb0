package com.scube.record.features.document.controller;

import com.scube.record.features.document.dto.DocumentTypeDto;
import com.scube.record.features.document.service.DocumentTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/document-types")
@RequiredArgsConstructor
public class DocumentTypeController {

    private final DocumentTypeService documentTypeService;

    @PostMapping
    public ResponseEntity<DocumentTypeDto> createDocumentType(
        @Valid @RequestBody DocumentTypeDto documentTypeDto,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        DocumentTypeDto created = documentTypeService.createDocumentType(documentTypeDto, userId);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @GetMapping("/{typeKey}")
    public ResponseEntity<DocumentTypeDto> getDocumentTypeByKey(@PathVariable String typeKey) {
        return documentTypeService.findByTypeKey(typeKey)
            .map(documentType -> ResponseEntity.ok(documentType))
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{typeKey}")
    public ResponseEntity<DocumentTypeDto> updateDocumentType(
        @PathVariable String typeKey,
        @Valid @RequestBody DocumentTypeDto documentTypeDto,
        @RequestHeader(value = "X-User-Id", defaultValue = "system") String userId) {

        DocumentTypeDto updated = documentTypeService.updateDocumentType(typeKey, documentTypeDto, userId);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{typeKey}")
    public ResponseEntity<Void> deleteDocumentType(@PathVariable String typeKey) {
        documentTypeService.deleteDocumentType(typeKey);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    public ResponseEntity<Page<DocumentTypeDto>> getAllActiveDocumentTypes(Pageable pageable) {
        Page<DocumentTypeDto> documentTypes = documentTypeService.findAllActiveDocumentTypes(pageable);
        return ResponseEntity.ok(documentTypes);
    }

    @GetMapping("/group/{groupName}")
    public ResponseEntity<Page<DocumentTypeDto>> getDocumentTypesByGroupName(
        @PathVariable String groupName,
        Pageable pageable) {

        Page<DocumentTypeDto> documentTypes = documentTypeService.findAllDocumentTypesByGroupName(groupName, pageable);
        return ResponseEntity.ok(documentTypes);
    }
}