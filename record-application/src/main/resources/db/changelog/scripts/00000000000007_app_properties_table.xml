<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- App Properties Table -->
    <changeSet author="Karthik" id="create_app_properties_table">
        <createTable tableName="app_properties" schemaName="record">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" primaryKey="true" primaryKeyName="app_properties_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>

            <!-- App properties specific fields -->
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="property_value" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(250)"/>
            <column name="property_type" type="VARCHAR(50)" defaultValue="STRING"/>
            <column name="is_encrypted" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="is_system" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- Add unique constraint and indexes -->
    <changeSet author="Karthik" id="add_app_properties_constraints_and_indexes">
        <addUniqueConstraint
            columnNames="name"
            constraintName="uk_app_properties_name"
            tableName="app_properties"
            schemaName="record"/>

        <createIndex indexName="idx_app_properties_name" tableName="app_properties" schemaName="record">
            <column name="name"/>
        </createIndex>
        <createIndex indexName="idx_app_properties_property_type" tableName="app_properties" schemaName="record">
            <column name="property_type"/>
        </createIndex>
        <createIndex indexName="idx_app_properties_is_system" tableName="app_properties" schemaName="record">
            <column name="is_system"/>
        </createIndex>
    </changeSet>

    <!-- Add check constraints -->
    <changeSet author="Karthik" id="add_app_properties_check_constraints">
        <sql>
            ALTER TABLE record.app_properties
            ADD CONSTRAINT chk_app_properties_property_type
            CHECK (property_type IN ('STRING', 'INTEGER', 'BOOLEAN', 'DECIMAL', 'DATE', 'JSON'));
        </sql>
    </changeSet>

    <!-- Insert default app properties -->
    <changeSet author="Karthik" id="insert_default_app_properties">
        <sql>
            INSERT INTO record.app_properties (name, property_value, description, property_type, is_system, created_by, last_modified_by)
            VALUES
            ('record.search.page.size.default', '20', 'Default page size for record search results', 'INTEGER', true, 'SYSTEM', 'SYSTEM'),
            ('record.search.page.size.max', '100', 'Maximum page size for record search results', 'INTEGER', true, 'SYSTEM', 'SYSTEM'),
            ('record.association.max.depth', '5', 'Maximum depth for recursive association queries', 'INTEGER', true, 'SYSTEM', 'SYSTEM'),
            ('record.code.generation.enabled', 'true', 'Enable automatic code generation for records', 'BOOLEAN', true, 'SYSTEM', 'SYSTEM'),
            ('record.audit.retention.days', '2555', 'Number of days to retain audit logs (7 years)', 'INTEGER', true, 'SYSTEM', 'SYSTEM'),
            ('record.file.upload.max.size.mb', '50', 'Maximum file upload size in megabytes', 'INTEGER', true, 'SYSTEM', 'SYSTEM')
            ON CONFLICT ON CONSTRAINT uk_app_properties_name DO NOTHING;
        </sql>
    </changeSet>

    <!-- Audit Log Table for App Properties -->
    <changeSet author="Karthik" id="create_audit_log_app_properties">
        <createTable tableName="audit_log_app_properties" schemaName="record">
            <column name="id" type="UUID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_app_properties_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_app_properties_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="name" type="VARCHAR(100)"/>
            <column name="property_value" type="VARCHAR(250)"/>
            <column name="description" type="VARCHAR(250)"/>
            <column name="property_type" type="VARCHAR(50)"/>
            <column name="is_encrypted" type="BOOLEAN"/>
            <column name="is_system" type="BOOLEAN"/>
        </createTable>
    </changeSet>


</databaseChangeLog>