<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Entity Note Table -->
    <changeSet author="Karthik" id="create_entity_note_table">
        <createTable tableName="entity_note" schemaName="record">
            <column autoIncrement="true" name="entity_note_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entity_note_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="entity_note_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>

            <!-- Entity note specific fields -->
            <column name="note" type="TEXT"/>
            <column name="note_type" type="VARCHAR(100)"/>
            <column name="priority" type="VARCHAR(20)" defaultValue="NORMAL"/>
            <column name="is_private" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="is_system_generated" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>

            <!-- Association support -->
            <column name="dummy_column" type="VARCHAR(50)" defaultValue="ENTITY_NOTE"/>
        </createTable>
    </changeSet>

    <!-- Add indexes for entity note -->
    <changeSet author="Karthik" id="add_entity_note_indexes">
        <createIndex indexName="idx_entity_note_uuid" tableName="entity_note" schemaName="record">
            <column name="entity_note_uuid"/>
        </createIndex>
        <createIndex indexName="idx_entity_note_type" tableName="entity_note" schemaName="record">
            <column name="note_type"/>
        </createIndex>
        <createIndex indexName="idx_entity_note_priority" tableName="entity_note" schemaName="record">
            <column name="priority"/>
        </createIndex>
        <createIndex indexName="idx_entity_note_is_private" tableName="entity_note" schemaName="record">
            <column name="is_private"/>
        </createIndex>
        <createIndex indexName="idx_entity_note_is_system_generated" tableName="entity_note" schemaName="record">
            <column name="is_system_generated"/>
        </createIndex>
        <createIndex indexName="idx_entity_note_created_at" tableName="entity_note" schemaName="record">
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <!-- Add check constraints -->
    <changeSet author="Karthik" id="add_entity_note_check_constraints">
        <sql>
            ALTER TABLE record.entity_note
            ADD CONSTRAINT chk_entity_note_priority
            CHECK (priority IN ('LOW', 'NORMAL', 'HIGH', 'CRITICAL'));
        </sql>
    </changeSet>

    <!-- Audit Log Table for Entity Note -->
    <changeSet author="Karthik" id="create_audit_log_entity_note">
        <createTable tableName="audit_log_entity_note" schemaName="record">
            <column name="entity_note_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_note_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_note_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="last_modified_by" type="VARCHAR(1000)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="entity_note_uuid" type="UUID"/>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>
            <column name="note" type="TEXT"/>
            <column name="note_type" type="VARCHAR(100)"/>
            <column name="priority" type="VARCHAR(20)"/>
            <column name="is_private" type="BOOLEAN"/>
            <column name="is_system_generated" type="BOOLEAN"/>
            <column name="dummy_column" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>


</databaseChangeLog>