<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Entity Fee Table -->
    <changeSet author="Karthik" id="create_entity_fee_table">
        <createTable tableName="entity_fee" schemaName="record">
            <column autoIncrement="true" name="entity_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entity_fee_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="entity_fee_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>

            <!-- Entity fee specific fields -->
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="amount" type="DECIMAL(19,2)" defaultValueNumeric="0.00">
                <constraints nullable="false"/>
            </column>
            <column name="payment_status" type="VARCHAR(50)" defaultValue="UNPAID">
                <constraints nullable="false"/>
            </column>
            <column name="order_id" type="UUID"/>
            <column name="paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="comment" type="TEXT"/>

            <!-- Association support -->
            <column name="dummy_column" type="VARCHAR(50)" defaultValue="ENTITY_FEE"/>
        </createTable>
    </changeSet>

    <!-- Add indexes for entity fee -->
    <changeSet author="Karthik" id="add_entity_fee_indexes">
        <createIndex indexName="idx_entity_fee_uuid" tableName="entity_fee" schemaName="record">
            <column name="entity_fee_uuid"/>
        </createIndex>
        <createIndex indexName="idx_entity_fee_payment_status" tableName="entity_fee" schemaName="record">
            <column name="payment_status"/>
        </createIndex>
        <createIndex indexName="idx_entity_fee_order_id" tableName="entity_fee" schemaName="record">
            <column name="order_id"/>
        </createIndex>
        <createIndex indexName="idx_entity_fee_fee_code" tableName="entity_fee" schemaName="record">
            <column name="fee_code"/>
        </createIndex>
        <createIndex indexName="idx_entity_fee_paid_date" tableName="entity_fee" schemaName="record">
            <column name="paid_date"/>
        </createIndex>
    </changeSet>

    <!-- Add check constraints -->
    <changeSet author="Karthik" id="add_entity_fee_check_constraints">
        <sql>
            ALTER TABLE record.entity_fee
            ADD CONSTRAINT chk_entity_fee_payment_status
            CHECK (payment_status IN ('UNPAID', 'PAID', 'PARTIALLY_PAID'));
        </sql>
        <sql>
            ALTER TABLE record.entity_fee
            ADD CONSTRAINT chk_entity_fee_amount_non_negative
            CHECK (amount >= 0);
        </sql>
    </changeSet>

    <!-- Audit Log Table for Entity Fee -->
    <changeSet author="Karthik" id="create_audit_log_entity_fee">
        <createTable tableName="audit_log_entity_fee" schemaName="record">
            <column name="entity_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_fee_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_fee_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="last_modified_by" type="VARCHAR(1000)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="entity_fee_uuid" type="UUID"/>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="amount" type="DECIMAL(19,2)"/>
            <column name="payment_status" type="VARCHAR(50)"/>
            <column name="order_id" type="UUID"/>
            <column name="paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="comment" type="TEXT"/>
            <column name="dummy_column" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>


</databaseChangeLog>