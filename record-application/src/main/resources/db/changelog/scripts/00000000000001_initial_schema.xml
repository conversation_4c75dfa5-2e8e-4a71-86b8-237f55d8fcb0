<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- ============================================ -->
    <!-- MAIN TABLES CREATION -->
    <!-- ============================================ -->

    <changeSet id="00000000000001_record_type" author="system">
        <comment>Create record_type table</comment>

        <createTable tableName="record_type">
            <column name="record_type_id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="record_type_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="type_code" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="type_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="parent_id" type="BIGINT"/>
            <column name="description" type="TEXT"/>
            <column name="properties" type="JSONB"/>
            <column name="config_json" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseTableName="record_type"
                                baseColumnNames="parent_id"
                                referencedTableName="record_type"
                                referencedColumnNames="record_type_id"
                                constraintName="fk_record_type_parent"/>
    </changeSet>

    <changeSet id="00000000000002_association_type" author="system">
        <comment>Create association_type table</comment>

        <createTable tableName="association_type">
            <column name="association_type_id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="association_type_uuid" type="VARCHAR(36)">
                <constraints nullable="true"/>
            </column>
            <column name="association_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="00000000000003_record" author="system">
        <comment>Create record table</comment>

        <createTable tableName="record">
            <column name="record_id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="record_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="record_type_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="record_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(50)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseTableName="record"
                                baseColumnNames="record_type_id"
                                referencedTableName="record_type"
                                referencedColumnNames="record_type_id"
                                constraintName="fk_record_record_type"/>
    </changeSet>

    <changeSet id="00000000000004_association" author="system">
        <comment>Create association table</comment>

        <createTable tableName="association">
            <column name="association_id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="association_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false"/>
            </column>
            <column name="parent_type" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="child_type" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="parent_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="child_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="association_type_id" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- ============================================ -->
    <!-- AUDIT TABLES CREATION -->
    <!-- ============================================ -->

    <changeSet id="00000000000005_audit_record_type" author="system">
        <comment>Create audit_log_record_type table</comment>

        <createTable tableName="audit_log_record_type">
            <column name="record_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_record_type_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_record_type_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="record_type_uuid" type="UUID"/>
            <column name="type_code" type="VARCHAR(100)"/>
            <column name="type_name" type="VARCHAR(255)"/>
            <column name="parent_id" type="BIGINT"/>
            <column name="description" type="TEXT"/>
            <column name="properties" type="JSONB"/>
            <column name="config_json" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="created_by" type="VARCHAR(100)"/>
            <column name="last_modified_at" type="TIMESTAMP"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="00000000000006_audit_association_type" author="system">
        <comment>Create audit_log_association_type table</comment>

        <createTable tableName="audit_log_association_type">
            <column name="association_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_association_type_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_association_type_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="association_type_uuid" type="VARCHAR(36)"/>
            <column name="association_name" type="VARCHAR(255)"/>
            <column name="properties" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="created_by" type="VARCHAR(100)"/>
            <column name="last_modified_at" type="TIMESTAMP"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="00000000000007_audit_record" author="system">
        <comment>Create audit_log_record table</comment>

        <createTable tableName="audit_log_record">
            <column name="record_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_record_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_record_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="record_uuid" type="UUID"/>
            <column name="record_type_id" type="BIGINT"/>
            <column name="record_name" type="VARCHAR(255)"/>
            <column name="status" type="VARCHAR(50)"/>
            <column name="properties" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="created_by" type="VARCHAR(100)"/>
            <column name="last_modified_at" type="TIMESTAMP"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="00000000000008_audit_association" author="system">
        <comment>Create audit_log_association table</comment>

        <createTable tableName="audit_log_association">
            <column name="association_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_association_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_association_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="association_uuid" type="UUID"/>
            <column name="parent_type" type="VARCHAR(20)"/>
            <column name="child_type" type="VARCHAR(20)"/>
            <column name="parent_id" type="BIGINT"/>
            <column name="child_id" type="BIGINT"/>
            <column name="association_type_id" type="VARCHAR(50)"/>
            <column name="properties" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="created_by" type="VARCHAR(100)"/>
            <column name="last_modified_at" type="TIMESTAMP"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>

    <!-- ============================================ -->
    <!-- INDEXES FOR PERFORMANCE -->
    <!-- ============================================ -->

    <changeSet id="00000000000009_indexes" author="system">
        <comment>Create indexes for performance optimization</comment>

        <!-- Record Type Indexes -->
        <createIndex indexName="idx_record_type_uuid" tableName="record_type">
            <column name="record_type_uuid"/>
        </createIndex>
        <createIndex indexName="idx_record_type_code" tableName="record_type">
            <column name="type_code"/>
        </createIndex>
        <createIndex indexName="idx_record_type_parent" tableName="record_type">
            <column name="parent_id"/>
        </createIndex>

        <!-- Association Type Indexes -->
        <createIndex indexName="idx_association_type_uuid" tableName="association_type">
            <column name="association_type_uuid"/>
        </createIndex>
        <createIndex indexName="idx_association_type_name" tableName="association_type">
            <column name="association_name"/>
        </createIndex>

        <!-- Record Indexes -->
        <createIndex indexName="idx_record_uuid" tableName="record">
            <column name="record_uuid"/>
        </createIndex>
        <createIndex indexName="idx_record_type_id" tableName="record">
            <column name="record_type_id"/>
        </createIndex>
        <createIndex indexName="idx_record_status" tableName="record">
            <column name="status"/>
        </createIndex>
        <createIndex indexName="idx_record_name" tableName="record">
            <column name="record_name"/>
        </createIndex>

        <!-- Association Indexes -->
        <createIndex indexName="idx_association_uuid" tableName="association">
            <column name="association_uuid"/>
        </createIndex>
        <createIndex indexName="idx_association_parent" tableName="association">
            <column name="parent_type"/>
            <column name="parent_id"/>
        </createIndex>
        <createIndex indexName="idx_association_child" tableName="association">
            <column name="child_type"/>
            <column name="child_id"/>
        </createIndex>
        <createIndex indexName="idx_association_type_id" tableName="association">
            <column name="association_type_id"/>
        </createIndex>

        <!-- Composite Indexes for Common Queries -->
        <createIndex indexName="idx_record_type_status" tableName="record">
            <column name="record_type_id"/>
            <column name="status"/>
        </createIndex>
        <createIndex indexName="idx_association_parent_child" tableName="association">
            <column name="parent_type"/>
            <column name="parent_id"/>
            <column name="child_type"/>
            <column name="child_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>