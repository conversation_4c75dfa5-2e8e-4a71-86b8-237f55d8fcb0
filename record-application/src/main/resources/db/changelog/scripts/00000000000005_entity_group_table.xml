<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Entity Group Table -->
    <changeSet author="Karthik" id="create_entity_group_table">
        <createTable tableName="entity_group" schemaName="record">
            <column autoIncrement="true" name="entity_group_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entity_group_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="entity_group_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>

            <!-- Entity group specific fields -->
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="group_type" type="VARCHAR(100)"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>

            <!-- Association support -->
            <column name="dummy_column" type="VARCHAR(50)" defaultValue="ENTITY_GROUP"/>
        </createTable>
    </changeSet>

    <!-- Add indexes for entity group -->
    <changeSet author="Karthik" id="add_entity_group_indexes">
        <createIndex indexName="idx_entity_group_uuid" tableName="entity_group" schemaName="record">
            <column name="entity_group_uuid"/>
        </createIndex>
        <createIndex indexName="idx_entity_group_name" tableName="entity_group" schemaName="record">
            <column name="name"/>
        </createIndex>
        <createIndex indexName="idx_entity_group_type" tableName="entity_group" schemaName="record">
            <column name="group_type"/>
        </createIndex>
        <createIndex indexName="idx_entity_group_is_active" tableName="entity_group" schemaName="record">
            <column name="is_active"/>
        </createIndex>
    </changeSet>

    <!-- Audit Log Table for Entity Group -->
    <changeSet author="Karthik" id="create_audit_log_entity_group">
        <createTable tableName="audit_log_entity_group" schemaName="record">
            <column name="entity_group_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_group_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_group_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="last_modified_by" type="VARCHAR(1000)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="entity_group_uuid" type="UUID"/>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>
            <column name="name" type="TEXT"/>
            <column name="description" type="TEXT"/>
            <column name="group_type" type="VARCHAR(100)"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="dummy_column" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>


</databaseChangeLog>