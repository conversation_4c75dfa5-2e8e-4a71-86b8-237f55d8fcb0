<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- Add conversion_reference column to all tables that extend AuditableBaseWithProperties -->

    <changeSet id="00000000000010-1" author="system">
        <comment>Add conversion_reference column to code_lookup table</comment>
        <addColumn schemaName="record" tableName="code_lookup">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-2" author="system">
        <comment>Add conversion_reference column to entity_fee table</comment>
        <addColumn schemaName="record" tableName="entity_fee">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-3" author="system">
        <comment>Add conversion_reference column to entity_group table</comment>
        <addColumn schemaName="record" tableName="entity_group">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-4" author="system">
        <comment>Add conversion_reference column to entity_note table</comment>
        <addColumn schemaName="record" tableName="entity_note">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-5" author="system">
        <comment>Add conversion_reference column to record table</comment>
        <addColumn schemaName="record" tableName="record">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-6" author="system">
        <comment>Add conversion_reference column to setting table</comment>
        <addColumn schemaName="record" tableName="setting">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Tables without explicit schema (using default schema) -->

    <changeSet id="00000000000010-7" author="system">
        <comment>Add conversion_reference column to document table</comment>
        <addColumn tableName="document">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-8" author="system">
        <comment>Add conversion_reference column to association table</comment>
        <addColumn tableName="association">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-9" author="system">
        <comment>Add conversion_reference column to record_type table</comment>
        <addColumn tableName="record_type">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-10" author="system">
        <comment>Add conversion_reference column to association_type table</comment>
        <addColumn tableName="association_type">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-11" author="system">
        <comment>Add conversion_reference column to document_type table</comment>
        <addColumn tableName="document_type">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000010-12" author="system">
        <comment>Add conversion_reference column to module_definition table</comment>
        <addColumn tableName="module_definition">
            <column name="conversion_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
