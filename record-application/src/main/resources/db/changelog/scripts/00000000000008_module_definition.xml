<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- ============================================ -->
    <!-- MODULE DEFINITION TABLE -->
    <!-- ============================================ -->

    <changeSet id="00000000000008_module_definition" author="system">
        <comment>Create module_definition table for storing module configurations</comment>

        <createTable tableName="module_definition">
            <column name="module_id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="module_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="module_code" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="module_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="version" type="VARCHAR(50)"/>
            <column name="config" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- ============================================ -->
    <!-- INDEXES FOR MODULE_DEFINITION -->
    <!-- ============================================ -->

    <changeSet id="00000000000009_module_definition_indexes" author="system">
        <comment>Create indexes for module_definition table</comment>

        <createIndex indexName="idx_module_code" tableName="module_definition">
            <column name="module_code"/>
        </createIndex>

        <createIndex indexName="idx_module_uuid" tableName="module_definition">
            <column name="module_uuid"/>
        </createIndex>

        <createIndex indexName="idx_module_active" tableName="module_definition">
            <column name="is_active"/>
        </createIndex>

        <createIndex indexName="idx_module_name" tableName="module_definition">
            <column name="module_name"/>
        </createIndex>
    </changeSet>

    <!-- ============================================ -->
    <!-- ADD MODULE_CODE TO RECORD_TYPE -->
    <!-- ============================================ -->

    <changeSet id="00000000000010_record_type_module_code" author="system">
        <comment>Add module_code column to record_type for tracking which module created each type</comment>

        <addColumn tableName="record_type">
            <column name="module_code" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- ============================================ -->
    <!-- FOREIGN KEY CONSTRAINT -->
    <!-- ============================================ -->

    <changeSet id="00000000000011_record_type_module_fk" author="system">
        <comment>Add foreign key constraint from record_type to module_definition</comment>

        <addForeignKeyConstraint 
            baseTableName="record_type"
            baseColumnNames="module_code"
            referencedTableName="module_definition"
            referencedColumnNames="module_code"
            constraintName="fk_record_type_module"
            onDelete="SET NULL"/>
    </changeSet>

    <!-- ============================================ -->
    <!-- INDEX FOR MODULE_CODE IN RECORD_TYPE -->
    <!-- ============================================ -->

    <changeSet id="00000000000012_record_type_module_code_index" author="system">
        <comment>Create index on module_code in record_type</comment>

        <createIndex indexName="idx_record_type_module_code" tableName="record_type">
            <column name="module_code"/>
        </createIndex>
    </changeSet>

    <!-- ============================================ -->
    <!-- AUDIT TABLE FOR MODULE_DEFINITION -->
    <!-- ============================================ -->

    <changeSet id="00000000000013_audit_module_definition" author="system">
        <comment>Create audit_log_module_definition table for audit trail</comment>

        <createTable tableName="audit_log_module_definition">
            <column name="module_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_module_definition_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_module_definition_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="module_uuid" type="UUID"/>
            <column name="module_code" type="VARCHAR(100)"/>
            <column name="module_name" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
            <column name="version" type="VARCHAR(50)"/>
            <column name="config" type="JSONB"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="created_at" type="TIMESTAMP"/>
            <column name="created_by" type="VARCHAR(100)"/>
            <column name="last_modified_at" type="TIMESTAMP"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>

    <!-- ============================================ -->
    <!-- UPDATE AUDIT TABLE FOR RECORD_TYPE -->
    <!-- ============================================ -->

    <changeSet id="00000000000014_audit_record_type_module_code" author="system">
        <comment>Add module_code column to audit_log_record_type</comment>

        <addColumn tableName="audit_log_record_type">
            <column name="module_code" type="VARCHAR(100)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>

