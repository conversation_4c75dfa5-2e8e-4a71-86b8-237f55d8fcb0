<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Code Lookup Table -->
    <changeSet author="Karthik" id="create_code_lookup_table">
        <createTable tableName="code_lookup" schemaName="record">
            <column autoIncrement="true" name="code_lookup_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="code_lookup_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="code_lookup_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="properties" type="JSONB"/>

            <!-- Code lookup specific fields -->
            <column name="code" type="VARCHAR(30)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="entity_type" type="VARCHAR(255)"/>
            <column name="entity_id" type="VARCHAR(255)"/>
            <column name="realm" type="VARCHAR(255)"/>
            <column name="action" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <!-- Add indexes for code lookup -->
    <changeSet author="Karthik" id="add_code_lookup_indexes">
        <createIndex indexName="idx_code_lookup_code" tableName="code_lookup" schemaName="record">
            <column name="code"/>
        </createIndex>
        <createIndex indexName="idx_code_lookup_entity_type_and_entity_id" tableName="code_lookup" schemaName="record">
            <column name="entity_type"/>
            <column name="entity_id"/>
        </createIndex>
        <createIndex indexName="idx_code_lookup_entity_type_and_entity_id_and_action" tableName="code_lookup" schemaName="record">
            <column name="entity_type"/>
            <column name="entity_id"/>
            <column name="action"/>
        </createIndex>
        <createIndex indexName="idx_code_lookup_realm" tableName="code_lookup" schemaName="record">
            <column name="realm"/>
        </createIndex>
    </changeSet>

    <!-- Audit Log Table for Code Lookup -->
    <changeSet author="Karthik" id="create_audit_log_code_lookup">
        <createTable tableName="audit_log_code_lookup" schemaName="record">
            <column name="code_lookup_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_code_lookup_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_code_lookup_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="last_modified_by" type="VARCHAR(1000)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="code_lookup_uuid" type="UUID"/>
            <column name="properties" type="JSONB"/>
            <column name="code" type="VARCHAR(30)"/>
            <column name="entity_type" type="VARCHAR(255)"/>
            <column name="entity_id" type="VARCHAR(255)"/>
            <column name="realm" type="VARCHAR(255)"/>
            <column name="action" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>


</databaseChangeLog>