<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- ============================================ -->
    <!-- CREATE SCHEMA -->
    <!-- ============================================ -->

    <changeSet id="00000000000000_create_record_schema" author="system">
        <comment>Create record schema if it doesn't exist</comment>
        
        <sql>
            CREATE SCHEMA IF NOT EXISTS record;
        </sql>
        
        <rollback>
            DROP SCHEMA IF EXISTS record CASCADE;
        </rollback>
    </changeSet>

</databaseChangeLog>

