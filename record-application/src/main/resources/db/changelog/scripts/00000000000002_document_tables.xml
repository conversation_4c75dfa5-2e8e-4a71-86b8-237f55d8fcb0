<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Document Type Table -->
    <changeSet author="Karthik" id="create_document_type_table">
        <createTable tableName="document_type" schemaName="record">
            <column autoIncrement="true" name="document_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="document_type_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="group_name" type="VARCHAR(255)"/>
            <column name="type_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="type_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- Document Table -->
    <changeSet author="Karthik" id="create_document_table">
        <createTable tableName="document" schemaName="record">
            <column autoIncrement="true" name="document_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="document_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="document_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>

            <!-- Document specific fields -->
            <column name="content_type" type="VARCHAR(255)"/>
            <column name="file_name" type="VARCHAR(255)"/>
            <column name="file_size" type="BIGINT"/>
            <column name="url" type="VARCHAR(500)"/>
            <column name="document_service_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="deleted_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="is_deleted" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>

            <!-- Foreign key to document type -->
            <column name="document_type_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <!-- Association support -->
            <column name="dummy_column" type="VARCHAR(50)" defaultValue="DOCUMENT"/>
        </createTable>
    </changeSet>

    <!-- Add unique constraints -->
    <changeSet author="Karthik" id="add_document_type_constraints">
        <addUniqueConstraint columnNames="type_key" constraintName="uk_document_type_key" tableName="document_type" schemaName="record"/>
    </changeSet>

    <!-- Add indexes -->
    <changeSet author="Karthik" id="add_document_indexes">
        <createIndex indexName="idx_document_document_uuid" tableName="document" schemaName="record">
            <column name="document_uuid"/>
        </createIndex>
        <createIndex indexName="idx_document_document_type_id" tableName="document" schemaName="record">
            <column name="document_type_id"/>
        </createIndex>
        <createIndex indexName="idx_document_is_deleted" tableName="document" schemaName="record">
            <column name="is_deleted"/>
        </createIndex>
    </changeSet>

    <!-- Add foreign key constraints -->
    <changeSet author="Karthik" id="add_document_foreign_keys">
        <addForeignKeyConstraint
            baseColumnNames="document_type_id"
            baseTableName="document"
            baseTableSchemaName="record"
            constraintName="fk_document_document_type"
            deferrable="false"
            initiallyDeferred="false"
            onDelete="RESTRICT"
            onUpdate="CASCADE"
            referencedColumnNames="document_type_id"
            referencedTableName="document_type"
            referencedTableSchemaName="record"
            validate="true"/>
    </changeSet>

    <!-- Audit Log Tables -->
    <changeSet author="Karthik" id="create_audit_log_document_type">
        <createTable tableName="audit_log_document_type" schemaName="record">
            <column name="document_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_type_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_type_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="last_modified_by" type="VARCHAR(1000)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="group_name" type="VARCHAR(255)"/>
            <column name="type_key" type="VARCHAR(255)"/>
            <column name="type_name" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
            <column name="is_active" type="BOOLEAN"/>
        </createTable>
    </changeSet>

    <changeSet author="Karthik" id="create_audit_log_document">
        <createTable tableName="audit_log_document" schemaName="record">
            <column name="document_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="last_modified_by" type="VARCHAR(1000)"/>
            <column name="last_modified_at" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="document_uuid" type="UUID"/>
            <column name="properties" type="JSONB"/>
            <column name="events" type="JSONB"/>
            <column name="content_type" type="VARCHAR(255)"/>
            <column name="file_name" type="VARCHAR(255)"/>
            <column name="file_size" type="BIGINT"/>
            <column name="url" type="VARCHAR(500)"/>
            <column name="document_service_uuid" type="UUID"/>
            <column name="deleted_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="is_deleted" type="BOOLEAN"/>
            <column name="document_type_id" type="BIGINT"/>
            <column name="dummy_column" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>


</databaseChangeLog>