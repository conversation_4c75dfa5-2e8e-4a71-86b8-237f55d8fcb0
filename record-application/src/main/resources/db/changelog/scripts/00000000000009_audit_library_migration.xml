<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- Tables that extend BaseEntity - rename columns and add properties column -->

    <changeSet id="00000000000009-1" author="system">
        <comment>Rename audit columns in code_lookup table</comment>
        <renameColumn schemaName="record"
                      tableName="code_lookup"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn schemaName="record"
                      tableName="code_lookup"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-2" author="system">
        <comment>Rename audit columns in entity_fee table</comment>
        <renameColumn schemaName="record"
                      tableName="entity_fee"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn schemaName="record"
                      tableName="entity_fee"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-3" author="system">
        <comment>Rename audit columns in entity_group table</comment>
        <renameColumn schemaName="record"
                      tableName="entity_group"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn schemaName="record"
                      tableName="entity_group"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-4" author="system">
        <comment>Rename audit columns in entity_note table</comment>
        <renameColumn schemaName="record"
                      tableName="entity_note"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn schemaName="record"
                      tableName="entity_note"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-5" author="system">
        <comment>Rename audit columns in record table</comment>
        <renameColumn schemaName="record"
                      tableName="record"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn schemaName="record"
                      tableName="record"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <!-- Tables with manual audit fields (don't extend BaseEntity) -->

    <changeSet id="00000000000009-6" author="system">
        <comment>Rename audit columns in document table</comment>
        <renameColumn tableName="document"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn tableName="document"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-7" author="system">
        <comment>Rename audit columns in association table</comment>
        <renameColumn tableName="association"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn tableName="association"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-8" author="system">
        <comment>Rename audit columns in record_type table</comment>
        <renameColumn tableName="record_type"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn tableName="record_type"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-9" author="system">
        <comment>Rename audit columns in association_type table</comment>
        <renameColumn tableName="association_type"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn tableName="association_type"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-10" author="system">
        <comment>Rename audit columns in document_type table</comment>
        <renameColumn tableName="document_type"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn tableName="document_type"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

    <changeSet id="00000000000009-11" author="system">
        <comment>Rename audit columns in module_definition table</comment>
        <renameColumn tableName="module_definition"
                      oldColumnName="created_at"
                      newColumnName="created_date"
                      columnDataType="TIMESTAMP"/>
        <renameColumn tableName="module_definition"
                      oldColumnName="last_modified_at"
                      newColumnName="last_modified_date"
                      columnDataType="TIMESTAMP"/>
    </changeSet>

</databaseChangeLog>
