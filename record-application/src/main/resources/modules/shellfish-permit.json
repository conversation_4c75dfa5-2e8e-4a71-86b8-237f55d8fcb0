{"moduleCode": "SHELLFISH_PERMIT", "moduleName": "Shellfish Harvesting Permit System", "description": "Commercial and recreational shellfish harvesting permits with area management", "version": "1.0.0", "recordTypes": [{"typeCode": "SHELLFISH_HARVESTER", "typeName": "Shellfish Harvester", "description": "Individual or business entity harvesting shellfish", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"firstName": {"type": "string", "label": "First Name", "required": true, "maxLength": 100}, "lastName": {"type": "string", "label": "Last Name", "required": true, "maxLength": 100}, "businessName": {"type": "string", "label": "Business Name", "required": false, "maxLength": 200}, "harvesterType": {"type": "string", "label": "Harvester Type", "required": true, "enum": ["COMMERCIAL", "RECREATIONAL"], "default": "RECREATIONAL"}, "email": {"type": "string", "label": "Email Address", "required": true, "format": "email"}, "phone": {"type": "string", "label": "Phone Number", "required": true, "pattern": "^\\d{10}$"}, "address": {"type": "object", "label": "Mailing Address", "properties": {"street": {"type": "string", "label": "Street"}, "city": {"type": "string", "label": "City"}, "state": {"type": "string", "label": "State"}, "zipCode": {"type": "string", "label": "ZIP", "pattern": "^\\d{5}$"}}}, "commercialLicenseNumber": {"type": "string", "label": "Commercial License Number", "required": false, "maxLength": 50}, "vesselName": {"type": "string", "label": "Vessel Name", "required": false, "maxLength": 100}, "vesselRegistration": {"type": "string", "label": "Vessel Registration", "required": false, "maxLength": 50}}}}, "config_json": {"displayFields": ["firstName", "lastName", "businessName", "harvesterType"], "searchableFields": ["firstName", "lastName", "businessName", "email"], "sortableFields": ["lastName", "businessName"], "defaultStatus": "ACTIVE"}}, {"typeCode": "HARVEST_AREA", "typeName": "Shellfish Harvest Area", "description": "Designated area for shellfish harvesting", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"areaCode": {"type": "string", "label": "Area Code", "required": true, "pattern": "^[A-Z]{2}-\\d{3}$", "example": "MA-001"}, "areaName": {"type": "string", "label": "Area Name", "required": true, "maxLength": 200}, "waterBody": {"type": "string", "label": "Water Body", "required": true, "maxLength": 200}, "coordinates": {"type": "object", "label": "GPS Coordinates", "properties": {"latitude": {"type": "number", "label": "Latitude"}, "longitude": {"type": "number", "label": "Longitude"}}}, "areaStatus": {"type": "string", "label": "Area Status", "required": true, "enum": ["OPEN", "CLOSED", "CONDITIONAL", "RESTRICTED"], "default": "OPEN"}, "closureReason": {"type": "string", "label": "Closure Reason", "required": false, "maxLength": 500}, "allowedSpecies": {"type": "array", "label": "Allowed Species", "items": {"type": "string"}, "enum": ["CLAMS", "OYSTERS", "MUSSELS", "SCALLOPS", "QUAHOGS"]}, "maxHarvestersPerDay": {"type": "integer", "label": "Max Harvesters Per Day", "minimum": 0}}}}, "config_json": {"displayFields": ["areaCode", "areaName", "waterBody", "areaStatus"], "searchableFields": ["areaCode", "areaName", "waterBody"], "sortableFields": ["areaCode", "areaName"], "defaultStatus": "ACTIVE"}}, {"typeCode": "SHELLFISH_PERMIT", "typeName": "Shellfish Harvesting Permit", "description": "Annual permit to harvest shellfish", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"permitNumber": {"type": "string", "label": "Permit Number", "required": true, "pattern": "^SF-\\d{4}-\\d{6}$", "example": "SF-2024-000001"}, "permitYear": {"type": "integer", "label": "Permit Year", "required": true, "minimum": 2020, "maximum": 2030}, "permitType": {"type": "string", "label": "Permit Type", "required": true, "enum": ["COMMERCIAL", "RECREATIONAL"], "default": "RECREATIONAL"}, "speciesAllowed": {"type": "array", "label": "Species Allowed", "items": {"type": "string"}, "enum": ["CLAMS", "OYSTERS", "MUSSELS", "SCALLOPS", "QUAHOGS"], "required": true}, "harvestAreas": {"type": "array", "label": "Authorized Harvest Areas", "items": {"type": "string"}, "description": "List of area codes where harvesting is allowed"}, "maxBushelsPerDay": {"type": "number", "label": "<PERSON> Per Day", "required": true, "minimum": 0, "maximum": 100}, "maxBushelsPerWeek": {"type": "number", "label": "<PERSON> Per Week", "required": false, "minimum": 0}, "issueDate": {"type": "date", "label": "Issue Date", "required": true}, "expirationDate": {"type": "date", "label": "Expiration Date", "required": true}, "status": {"type": "string", "label": "Permit Status", "required": true, "enum": ["PENDING", "ACTIVE", "SUSPENDED", "REVOKED", "EXPIRED"], "default": "PENDING"}, "suspensionReason": {"type": "string", "label": "Suspension Reason", "required": false, "maxLength": 1000}, "notes": {"type": "string", "label": "Notes", "required": false, "maxLength": 2000}}}}, "config_json": {"displayFields": ["permitNumber", "permitType", "permitYear", "status"], "searchableFields": ["permitNumber"], "sortableFields": ["permitNumber", "issueDate", "expirationDate"], "defaultStatus": "PENDING", "workflowStates": {"PENDING": {"label": "Pending Approval", "allowedTransitions": ["ACTIVE", "REVOKED"]}, "ACTIVE": {"label": "Active", "allowedTransitions": ["SUSPENDED", "REVOKED", "EXPIRED"]}, "SUSPENDED": {"label": "Suspended", "allowedTransitions": ["ACTIVE", "REVOKED"]}, "REVOKED": {"label": "Revoked", "allowedTransitions": []}, "EXPIRED": {"label": "Expired", "allowedTransitions": ["ACTIVE"]}}, "feeRules": [{"feeCode": "COMMERCIAL_PERMIT_FEE", "feeName": "Commercial Shellfish Permit Fee", "description": "Annual fee for commercial harvesting", "amount": 300.0, "condition": "record.properties.permitType == 'COMMERCIAL'"}, {"feeCode": "RECREATIONAL_PERMIT_FEE", "feeName": "Recreational Shellfish Permit Fee", "description": "Annual fee for recreational harvesting", "amount": 50.0, "condition": "record.properties.permitType == 'RECREATIONAL'"}, {"feeCode": "LATE_RENEWAL_FEE", "feeName": "Late <PERSON><PERSON>e", "description": "Additional fee for late renewal", "amount": 25.0, "condition": "record.properties.issueDate > record.properties.previousExpirationDate"}, {"feeCode": "MULTI_SPECIES_FEE", "feeName": "Multi-Species Endorsement", "description": "Additional fee for harvesting multiple species", "amount": 15.0, "condition": "record.properties.speciesAllowed.length > 2"}], "validationRules": [{"field": "expirationDate", "rule": "must_be_after", "compareField": "issueDate", "message": "Expiration date must be after issue date"}, {"field": "maxBushelsPerWeek", "rule": "must_be_greater_than", "compareField": "maxBushelsPerDay", "message": "Weekly limit must be greater than daily limit"}], "autoActions": [{"trigger": "30_days_before_expiration", "action": "send_renewal_reminder", "params": {"emailTemplate": "shellfish_permit_renewal_reminder"}}, {"trigger": "on_expiration", "action": "update_status", "params": {"newStatus": "EXPIRED"}}]}}], "associationTypes": [{"associationName": "HARVESTER_TO_PERMIT", "description": "Links harvester to their permits", "parentType": "SHELLFISH_HARVESTER", "childType": "SHELLFISH_PERMIT", "cardinality": "ONE_TO_MANY", "required": true}, {"associationName": "PERMIT_TO_AREA", "description": "Links permit to authorized harvest areas", "parentType": "SHELLFISH_PERMIT", "childType": "HARVEST_AREA", "cardinality": "MANY_TO_MANY", "required": true}], "documentTypes": [{"documentTypeCode": "COMMERCIAL_LICENSE", "documentTypeName": "Commercial Fishing License", "description": "State-issued commercial fishing license", "required": true, "requiredFor": ["COMMERCIAL"], "allowedFormats": ["PDF", "JPG", "PNG"], "maxSizeMB": 5}, {"documentTypeCode": "VESSEL_REGISTRATION", "documentTypeName": "Vessel Registration", "description": "Proof of vessel registration", "required": false, "allowedFormats": ["PDF", "JPG", "PNG"], "maxSizeMB": 5}, {"documentTypeCode": "INSURANCE_CERTIFICATE", "documentTypeName": "Liability Insurance Certificate", "description": "Proof of liability insurance for commercial harvesters", "required": true, "requiredFor": ["COMMERCIAL"], "allowedFormats": ["PDF"], "maxSizeMB": 5}], "permissions": {"CREATE_SHELLFISH_PERMIT": ["CLERK", "ADMIN"], "UPDATE_SHELLFISH_PERMIT": ["CLERK", "ADMIN"], "DELETE_SHELLFISH_PERMIT": ["ADMIN"], "VIEW_SHELLFISH_PERMIT": ["CLERK", "ADMIN", "PUBLIC"], "APPROVE_SHELLFISH_PERMIT": ["CLERK", "ADMIN"], "SUSPEND_SHELLFISH_PERMIT": ["ADMIN"], "REVOKE_SHELLFISH_PERMIT": ["ADMIN"], "MANAGE_HARVEST_AREAS": ["ADMIN"]}, "settings": {"autoGeneratePermitNumber": true, "permitNumberPrefix": "SF", "permitNumberFormat": "SF-{YEAR}-{SEQUENCE:6}", "defaultPermitDurationMonths": 12, "renewalWindowDays": 60, "gracePeriodDays": 30, "enableEmailNotifications": true, "enableSMSNotifications": true, "requireBackgroundCheck": false, "commercialRequiresInspection": true, "maxPermitsPerHarvester": 5}}