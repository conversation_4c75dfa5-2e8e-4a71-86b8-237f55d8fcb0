{"moduleCode": "DOG_LICENSE", "moduleName": "Dog Licensing Module", "description": "Comprehensive dog registration and licensing system", "version": "1.0.0", "config": {"recordTypes": [{"typeCode": "DOG_OWNER", "typeName": "Dog Owner", "description": "Individual or organization that owns a dog", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"firstName": {"type": "string", "label": "First Name", "required": true, "maxLength": 100}, "lastName": {"type": "string", "label": "Last Name", "required": true, "maxLength": 100}, "email": {"type": "string", "label": "Email Address", "required": true, "format": "email"}, "phone": {"type": "string", "label": "Phone Number", "required": true, "pattern": "^\\d{10}$"}, "address": {"type": "object", "label": "Address", "properties": {"street": {"type": "string", "label": "Street Address"}, "city": {"type": "string", "label": "City"}, "state": {"type": "string", "label": "State"}, "zipCode": {"type": "string", "label": "ZIP Code", "pattern": "^\\d{5}$"}}}, "isSenior": {"type": "boolean", "label": "Senior Citizen (65+)", "default": false}}}}, "config_json": {"displayFields": ["firstName", "lastName", "email", "phone"], "searchableFields": ["firstName", "lastName", "email"], "sortableFields": ["lastName", "firstName"], "defaultStatus": "ACTIVE"}}, {"typeCode": "DOG", "typeName": "Dog", "description": "Dog being registered", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"dogName": {"type": "string", "label": "Dog Name", "required": true, "maxLength": 100}, "breed": {"type": "string", "label": "Breed", "required": true, "maxLength": 100}, "color": {"type": "string", "label": "Primary Color", "required": true, "enum": ["Black", "<PERSON>", "White", "Golden", "<PERSON>", "Mixed"]}, "sex": {"type": "string", "label": "Sex", "required": true, "enum": ["Male", "Female"]}, "birthYear": {"type": "integer", "label": "Birth Year", "required": true, "minimum": 1990, "maximum": 2024}, "weight": {"type": "number", "label": "Weight (lbs)", "required": false, "minimum": 0, "maximum": 300}, "isSpayedOrNeutered": {"type": "boolean", "label": "Spayed/Neutered", "required": true, "default": false}, "microchipNumber": {"type": "string", "label": "Microchip Number", "required": false, "maxLength": 50}, "rabiesVaccinationDate": {"type": "date", "label": "Rabies Vaccination Date", "required": true}, "rabiesVaccinationExpiry": {"type": "date", "label": "Rabies Vaccination Expiry", "required": true}, "veterinarianName": {"type": "string", "label": "Veterinarian Name", "required": false, "maxLength": 200}}}}, "config_json": {"displayFields": ["<PERSON><PERSON><PERSON>", "breed", "color", "sex", "birthYear"], "searchableFields": ["<PERSON><PERSON><PERSON>", "breed", "microchipNumber"], "sortableFields": ["<PERSON><PERSON><PERSON>", "breed"], "defaultStatus": "ACTIVE"}}, {"typeCode": "DOG_LICENSE", "typeName": "Dog License", "description": "Annual dog license record", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"licenseNumber": {"type": "string", "label": "License Number", "required": true, "pattern": "^DL-\\d{4}-\\d{6}$", "example": "DL-2024-000001"}, "licenseYear": {"type": "integer", "label": "License Year", "required": true, "minimum": 2020, "maximum": 2030}, "issueDate": {"type": "date", "label": "Issue Date", "required": true}, "expirationDate": {"type": "date", "label": "Expiration Date", "required": true}, "status": {"type": "string", "label": "License Status", "required": true, "enum": ["PENDING", "ACTIVE", "EXPIRED", "REVOKED", "SUSPENDED"], "default": "PENDING"}, "tagNumber": {"type": "string", "label": "Tag Number", "required": false, "maxLength": 50}, "notes": {"type": "string", "label": "Notes", "required": false, "maxLength": 1000}}}}, "config_json": {"displayFields": ["licenseNumber", "licenseYear", "status", "expirationDate"], "searchableFields": ["licenseNumber", "tagNumber"], "sortableFields": ["licenseNumber", "issueDate", "expirationDate"], "defaultStatus": "PENDING", "workflowStates": {"PENDING": {"label": "Pending Approval", "allowedTransitions": ["ACTIVE", "REVOKED"]}, "ACTIVE": {"label": "Active", "allowedTransitions": ["EXPIRED", "REVOKED", "SUSPENDED"]}, "EXPIRED": {"label": "Expired", "allowedTransitions": ["ACTIVE"]}, "REVOKED": {"label": "Revoked", "allowedTransitions": []}, "SUSPENDED": {"label": "Suspended", "allowedTransitions": ["ACTIVE", "REVOKED"]}}, "feeRules": [{"feeCode": "DOG_LICENSE_STANDARD", "feeName": "Standard Dog License Fee", "description": "Annual license fee for non-spayed/neutered dogs", "amount": 25.0, "condition": "!record.properties.dog.isSpayedOrNeutered && !record.properties.owner.isSenior"}, {"feeCode": "DOG_LICENSE_NEUTERED", "feeName": "Spayed/Neutered Dog License Fee", "description": "Reduced fee for spayed/neutered dogs", "amount": 15.0, "condition": "record.properties.dog.isSpayedOrNeutered && !record.properties.owner.isSenior"}, {"feeCode": "DOG_LICENSE_SENIOR", "feeName": "Senior Citizen Dog License Fee", "description": "Reduced fee for senior citizens", "amount": 10.0, "condition": "record.properties.owner.isSenior"}, {"feeCode": "LATE_FEE", "feeName": "Late <PERSON><PERSON>e", "description": "Additional fee for late renewal", "amount": 10.0, "condition": "record.properties.issueDate > record.properties.previousExpirationDate"}], "validationRules": [{"field": "expirationDate", "rule": "must_be_after", "compareField": "issueDate", "message": "Expiration date must be after issue date"}, {"field": "rabiesVaccinationExpiry", "rule": "must_be_after", "compareField": "expirationDate", "message": "Rabies vaccination must be valid through license expiration"}], "autoActions": [{"trigger": "30_days_before_expiration", "action": "send_renewal_reminder", "params": {"emailTemplate": "dog_license_renewal_reminder"}}, {"trigger": "on_expiration", "action": "update_status", "params": {"newStatus": "EXPIRED"}}]}}], "associationTypes": [{"associationName": "OWNER_TO_LICENSE", "description": "Links dog owner to their dog license", "parentType": "DOG_OWNER", "childType": "DOG_LICENSE", "cardinality": "ONE_TO_MANY", "required": true}, {"associationName": "DOG_TO_LICENSE", "description": "Links dog to its license", "parentType": "DOG", "childType": "DOG_LICENSE", "cardinality": "ONE_TO_ONE", "required": true}, {"associationName": "OWNER_TO_DOG", "description": "Links owner to their dog(s)", "parentType": "DOG_OWNER", "childType": "DOG", "cardinality": "ONE_TO_MANY", "required": true}], "documentTypes": [{"documentTypeCode": "RABIES_CERTIFICATE", "documentTypeName": "Rabies Vaccination Certificate", "description": "Proof of rabies vaccination", "required": true, "allowedFormats": ["PDF", "JPG", "PNG"], "maxSizeMB": 5}, {"documentTypeCode": "SPAY_NEUTER_CERTIFICATE", "documentTypeName": "Spay/Neuter Certificate", "description": "Proof of spay/neuter procedure", "required": false, "allowedFormats": ["PDF", "JPG", "PNG"], "maxSizeMB": 5}, {"documentTypeCode": "DOG_PHOTO", "documentTypeName": "Dog Photo", "description": "Photo of the dog", "required": false, "allowedFormats": ["JPG", "PNG"], "maxSizeMB": 2}], "permissions": {"CREATE_DOG_LICENSE": ["CLERK", "ADMIN"], "UPDATE_DOG_LICENSE": ["CLERK", "ADMIN"], "DELETE_DOG_LICENSE": ["ADMIN"], "VIEW_DOG_LICENSE": ["CLERK", "ADMIN", "PUBLIC"], "APPROVE_DOG_LICENSE": ["CLERK", "ADMIN"], "REVOKE_DOG_LICENSE": ["ADMIN"]}, "settings": {"autoGenerateLicenseNumber": true, "licenseNumberPrefix": "DL", "licenseNumberFormat": "DL-{YEAR}-{SEQUENCE:6}", "defaultLicenseDurationMonths": 12, "renewalWindowDays": 30, "gracePeriodDays": 15, "enableEmailNotifications": true, "enableSMSNotifications": false}}}