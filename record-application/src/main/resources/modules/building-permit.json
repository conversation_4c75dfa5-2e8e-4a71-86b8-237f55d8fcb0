{"moduleCode": "BUILDING_PERMIT", "moduleName": "Building Permit System", "description": "Construction, renovation, and demolition permits with contractor management", "version": "1.0.0", "recordTypes": [{"typeCode": "PROPERTY_OWNER", "typeName": "Property Owner", "description": "Owner of the property where work will be performed", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"ownerName": {"type": "string", "label": "Owner Name", "required": true, "maxLength": 200}, "ownerType": {"type": "string", "label": "Owner Type", "required": true, "enum": ["INDIVIDUAL", "COMPANY", "GOVERNMENT", "NON_PROFIT"], "default": "INDIVIDUAL"}, "email": {"type": "string", "label": "Email Address", "required": true, "format": "email"}, "phone": {"type": "string", "label": "Phone Number", "required": true, "pattern": "^\\d{10}$"}, "mailingAddress": {"type": "object", "label": "Mailing Address", "properties": {"street": {"type": "string", "label": "Street"}, "city": {"type": "string", "label": "City"}, "state": {"type": "string", "label": "State"}, "zipCode": {"type": "string", "label": "ZIP", "pattern": "^\\d{5}$"}}}}}}, "config_json": {"displayFields": ["ownerName", "ownerType", "email", "phone"], "searchableFields": ["ownerName", "email"], "sortableFields": ["ownerName"], "defaultStatus": "ACTIVE"}}, {"typeCode": "CONTRACTOR", "typeName": "Licensed Contractor", "description": "Licensed contractor performing the work", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"companyName": {"type": "string", "label": "Company Name", "required": true, "maxLength": 200}, "licenseNumber": {"type": "string", "label": "Contractor License Number", "required": true, "maxLength": 50}, "licenseType": {"type": "string", "label": "License Type", "required": true, "enum": ["GENERAL_CONTRACTOR", "ELECTRICAL", "PLUMBING", "HVAC", "ROOFING", "SPECIALTY"]}, "licenseExpiry": {"type": "date", "label": "License Expiration Date", "required": true}, "contactPerson": {"type": "string", "label": "Contact Person", "required": true, "maxLength": 100}, "phone": {"type": "string", "label": "Phone Number", "required": true, "pattern": "^\\d{10}$"}, "email": {"type": "string", "label": "Email Address", "required": true, "format": "email"}, "insurancePolicyNumber": {"type": "string", "label": "Insurance Policy Number", "required": true, "maxLength": 100}, "insuranceExpiry": {"type": "date", "label": "Insurance Expiration Date", "required": true}}}}, "config_json": {"displayFields": ["companyName", "licenseNumber", "licenseType", "<PERSON><PERSON><PERSON>"], "searchableFields": ["companyName", "licenseNumber", "<PERSON><PERSON><PERSON>"], "sortableFields": ["companyName"], "defaultStatus": "ACTIVE"}}, {"typeCode": "BUILDING_PERMIT", "typeName": "Building Permit", "description": "Permit for construction, renovation, or demolition", "parentTypeCode": null, "properties": {"schema": {"type": "object", "properties": {"permitNumber": {"type": "string", "label": "Permit Number", "required": true, "pattern": "^BP-\\d{4}-\\d{6}$", "example": "BP-2024-000001"}, "permitType": {"type": "string", "label": "Permit Type", "required": true, "enum": ["NEW_CONSTRUCTION", "RENOVATION", "ADDITION", "DEMOLITION", "REPAIR", "ALTERATION"]}, "workType": {"type": "array", "label": "Type of Work", "items": {"type": "string"}, "enum": ["STRUCTURAL", "ELECTRICAL", "PLUMBING", "HVAC", "ROOFING", "FOUNDATION", "INTERIOR", "EXTERIOR"]}, "propertyAddress": {"type": "string", "label": "Property Address", "required": true, "maxLength": 300}, "parcelNumber": {"type": "string", "label": "Parcel/Tax ID Number", "required": true, "maxLength": 50}, "zoning": {"type": "string", "label": "Zoning District", "required": false, "enum": ["RESIDENTIAL", "COMMERCIAL", "INDUSTRIAL", "MIXED_USE", "AGRICULTURAL"]}, "projectDescription": {"type": "string", "label": "Project Description", "required": true, "maxLength": 2000}, "estimatedCost": {"type": "number", "label": "Estimated Project Cost", "required": true, "minimum": 0}, "squareFootage": {"type": "number", "label": "Square Footage", "required": false, "minimum": 0}, "numberOfStories": {"type": "integer", "label": "Number of Stories", "required": false, "minimum": 1, "maximum": 50}, "numberOfUnits": {"type": "integer", "label": "Number of Units", "required": false, "minimum": 1}, "applicationDate": {"type": "date", "label": "Application Date", "required": true}, "issueDate": {"type": "date", "label": "Issue Date", "required": false}, "expirationDate": {"type": "date", "label": "Expiration Date", "required": false}, "completionDate": {"type": "date", "label": "Completion Date", "required": false}, "status": {"type": "string", "label": "Permit Status", "required": true, "enum": ["PENDING", "UNDER_REVIEW", "APPROVED", "REJECTED", "ISSUED", "IN_PROGRESS", "INSPECTIONS_REQUIRED", "COMPLETED", "EXPIRED", "REVOKED"], "default": "PENDING"}, "reviewNotes": {"type": "string", "label": "Review Notes", "required": false, "maxLength": 2000}, "rejectionReason": {"type": "string", "label": "Rejection Reason", "required": false, "maxLength": 1000}}}}, "config_json": {"displayFields": ["permitNumber", "permitType", "propertyAddress", "status"], "searchableFields": ["permitNumber", "propertyAddress", "parcelNumber"], "sortableFields": ["permitNumber", "applicationDate", "issueDate"], "defaultStatus": "PENDING", "workflowStates": {"PENDING": {"label": "Pending Review", "allowedTransitions": ["UNDER_REVIEW", "REJECTED"]}, "UNDER_REVIEW": {"label": "Under Review", "allowedTransitions": ["APPROVED", "REJECTED", "PENDING"]}, "APPROVED": {"label": "Approved", "allowedTransitions": ["ISSUED", "REJECTED"]}, "REJECTED": {"label": "Rejected", "allowedTransitions": ["PENDING"]}, "ISSUED": {"label": "Issued", "allowedTransitions": ["IN_PROGRESS", "EXPIRED", "REVOKED"]}, "IN_PROGRESS": {"label": "Work In Progress", "allowedTransitions": ["INSPECTIONS_REQUIRED", "COMPLETED", "REVOKED"]}, "INSPECTIONS_REQUIRED": {"label": "Inspections Required", "allowedTransitions": ["IN_PROGRESS", "COMPLETED", "REVOKED"]}, "COMPLETED": {"label": "Completed", "allowedTransitions": []}, "EXPIRED": {"label": "Expired", "allowedTransitions": ["PENDING"]}, "REVOKED": {"label": "Revoked", "allowedTransitions": []}}, "feeRules": [{"feeCode": "BASE_PERMIT_FEE", "feeName": "Base Permit Fee", "description": "Base fee for all permits", "amount": 100.0, "condition": "true"}, {"feeCode": "CONSTRUCTION_VALUE_FEE", "feeName": "Construction Value Fee", "description": "Fee based on estimated project cost ($5 per $1000)", "amount": "Math.max(50, record.properties.estimatedCost / 1000 * 5)", "condition": "record.properties.estimatedCost > 0"}, {"feeCode": "PLAN_REVIEW_FEE", "feeName": "Plan Review Fee", "description": "Fee for reviewing construction plans", "amount": 150.0, "condition": "record.properties.permitType == 'NEW_CONSTRUCTION' || record.properties.permitType == 'ADDITION'"}, {"feeCode": "ELECTRICAL_INSPECTION_FEE", "feeName": "Electrical Inspection Fee", "description": "Fee for electrical work inspection", "amount": 75.0, "condition": "record.properties.workType.includes('ELECTRICAL')"}, {"feeCode": "PLUMBING_INSPECTION_FEE", "feeName": "Plumbing Inspection Fee", "description": "Fee for plumbing work inspection", "amount": 75.0, "condition": "record.properties.workType.includes('PLUMBING')"}, {"feeCode": "DEMOLITION_FEE", "feeName": "Demolition Permit Fee", "description": "Additional fee for demolition permits", "amount": 200.0, "condition": "record.properties.permitType == 'DEMOLITION'"}], "validationRules": [{"field": "issueDate", "rule": "must_be_after_or_equal", "compareField": "applicationDate", "message": "Issue date cannot be before application date"}, {"field": "expirationDate", "rule": "must_be_after", "compareField": "issueDate", "message": "Expiration date must be after issue date"}, {"field": "completionDate", "rule": "must_be_before", "compareField": "expirationDate", "message": "Completion date must be before expiration date"}], "autoActions": [{"trigger": "on_approval", "action": "send_notification", "params": {"emailTemplate": "permit_approved", "recipients": ["owner", "contractor"]}}, {"trigger": "30_days_before_expiration", "action": "send_expiration_warning", "params": {"emailTemplate": "permit_expiring_soon"}}, {"trigger": "on_expiration", "action": "update_status", "params": {"newStatus": "EXPIRED"}}]}}], "associationTypes": [{"associationName": "OWNER_TO_PERMIT", "description": "Links property owner to their building permits", "parentType": "PROPERTY_OWNER", "childType": "BUILDING_PERMIT", "cardinality": "ONE_TO_MANY", "required": true}, {"associationName": "CONTRACTOR_TO_PERMIT", "description": "Links contractor to permits they are working on", "parentType": "CONTRACTOR", "childType": "BUILDING_PERMIT", "cardinality": "ONE_TO_MANY", "required": true}], "documentTypes": [{"documentTypeCode": "CONSTRUCTION_PLANS", "documentTypeName": "Construction Plans", "description": "Architectural and engineering plans", "required": true, "allowedFormats": ["PDF"], "maxSizeMB": 50}, {"documentTypeCode": "SITE_PLAN", "documentTypeName": "Site Plan", "description": "Property site plan showing building location", "required": true, "allowedFormats": ["PDF", "DWG"], "maxSizeMB": 20}, {"documentTypeCode": "CONTRACTOR_LICENSE", "documentTypeName": "Contractor License", "description": "Copy of contractor's license", "required": true, "allowedFormats": ["PDF", "JPG", "PNG"], "maxSizeMB": 5}, {"documentTypeCode": "INSURANCE_CERTIFICATE", "documentTypeName": "Insurance Certificate", "description": "Proof of contractor's liability insurance", "required": true, "allowedFormats": ["PDF"], "maxSizeMB": 5}, {"documentTypeCode": "PROPERTY_DEED", "documentTypeName": "Property Deed", "description": "Proof of property ownership", "required": false, "allowedFormats": ["PDF"], "maxSizeMB": 10}], "permissions": {"CREATE_BUILDING_PERMIT": ["CLERK", "ADMIN", "INSPECTOR"], "UPDATE_BUILDING_PERMIT": ["CLERK", "ADMIN", "INSPECTOR"], "DELETE_BUILDING_PERMIT": ["ADMIN"], "VIEW_BUILDING_PERMIT": ["CLERK", "ADMIN", "INSPECTOR", "PUBLIC"], "APPROVE_BUILDING_PERMIT": ["INSPECTOR", "ADMIN"], "REJECT_BUILDING_PERMIT": ["INSPECTOR", "ADMIN"], "ISSUE_BUILDING_PERMIT": ["CLERK", "ADMIN"], "REVOKE_BUILDING_PERMIT": ["ADMIN"], "CONDUCT_INSPECTION": ["INSPECTOR", "ADMIN"]}, "settings": {"autoGeneratePermitNumber": true, "permitNumberPrefix": "BP", "permitNumberFormat": "BP-{YEAR}-{SEQUENCE:6}", "defaultPermitDurationMonths": 12, "extensionAllowed": true, "maxExtensionMonths": 6, "enableEmailNotifications": true, "enableSMSNotifications": false, "requirePlanReview": true, "requireInspections": true, "inspectionTypes": ["FOUNDATION", "FRAMING", "ELECTRICAL", "PLUMBING", "FINAL"], "autoExpireAfterMonths": 12}}