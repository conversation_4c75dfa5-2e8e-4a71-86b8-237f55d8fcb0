server:
  port: 9013
  servlet:
    context-path: /api/record
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: always
    include-exception: true
    include-client-error-message: true
    include-server-error-message: true
    send-client-error-email: false
    send-server-error-email: false
  forward-headers-strategy: framework

logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"
    com.scube: "debug"

spring:
  application:
    name: RecordService
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB

  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    properties:
      hibernate:
        show_sql: false
        format_sql: true
        use_sql_comments: true
        generate_statistics: true
        order_inserts: true
        order_updates: true
        jdbc:
          batch_size: 25
        cache:
          use_second_level_cache: false

springdoc:
  show-actuator: true
  swagger-ui:
    filter: true

# Module System Configuration
module:
  config:
    enabled: true                          # Enable/disable module system
    auto-load: true                        # Auto-load modules on startup
    directory: "classpath:modules/"        # Directory containing module JSON files
    update-existing: false                 # Update existing modules on startup (false = skip if exists)

keycloak:
  host: ${KEYCLOAK_URL:http://localhost:8080}
  public-host: ${KEYCLOAK_PUBLIC_URL:http://localhost:3030}
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.host}
    realm: clerkXpress
    client-id: clerkXpress-swagger

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.public-host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/public/**"
      - "/actuator/**"
      - "/api/records/**"
      - "/api/v1/search/**"
      - "/api/v1/profiles/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - http://kubernetes.docker.internal:3000
          - ${keycloak.public-host}

multi-tenancy:
  enabled: true
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: ${SPRING_RABBITMQ_HOST:localhost}
    port: 5672
  scheduling:
    enabled: false
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
      default-schema: record
      liquibase-schema: public
    datasource:
      url: "jdbc:postgresql://${DATABASE_HOST:localhost}:5432/postgres?currentSchema=record"
      username: ${SPRING_DATASOURCE_USERNAME:yourusername}
      password: ${SPRING_DATASOURCE_PASSWORD:yourpassword}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

# Service client configurations
com.scube.client:
  auth: "http://${CLIENT_LIB_HOST:localhost}:9001/api/auth"
  license: "http://${CLIENT_LIB_HOST:localhost}:9004/api/license"
  document: "http://${CLIENT_LIB_HOST:localhost}:9003/api/document-service"
  calculation: "http://${CLIENT_LIB_HOST:localhost}:9002/api/calculation"
  record: "http://${CLIENT_LIB_HOST:localhost}:9011/api/record"