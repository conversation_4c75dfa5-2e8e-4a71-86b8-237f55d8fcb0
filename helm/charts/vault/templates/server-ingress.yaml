{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- if not .Values.global.openshift }}
{{ template "vault.mode" . }}
{{- if ne .mode "external" }}
{{- if .Values.server.ingress.enabled -}}
{{- $extraPaths := .Values.server.ingress.extraPaths -}}
{{- $serviceName := include "vault.fullname" . -}}
{{- template "vault.serverServiceEnabled" . -}}
{{- if .serverServiceEnabled -}}
{{- if and (eq .mode "ha" ) (eq (.Values.server.ingress.activeService | toString) "true") }}
{{- $serviceName = printf "%s-%s" $serviceName "active" -}}
{{- end }}
{{- $servicePort := .Values.server.service.port -}}
{{- $pathType := .Values.server.ingress.pathType -}}
{{- $kubeVersion := .Capabilities.KubeVersion.Version }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "vault.fullname" . }}
  namespace: hashicorp-vault
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- with .Values.server.ingress.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- template "vault.ingress.annotations" . }}
spec:
{{- if .Values.server.ingress.tls }}
  tls:
  {{- range .Values.server.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
{{- if .Values.server.ingress.ingressClassName }}
  ingressClassName: {{ .Values.server.ingress.ingressClassName }}
{{- end }}
  rules:
  {{- range .Values.server.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
{{ if $extraPaths }}
{{ toYaml $extraPaths | indent 10 }}
{{- end }}
        {{- range (.paths | default (list "/")) }}
          - path: {{ . }}
            pathType: {{ $pathType }}
            backend:
              service:
                name: {{ $serviceName }}
                port:
                  number: {{ $servicePort }}
        {{- end }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
