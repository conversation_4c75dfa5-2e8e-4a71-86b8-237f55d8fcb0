{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.injectorEnabled" . -}}
{{- if .injectorEnabled -}}
{{- if and (eq (.Values.injector.leaderElector.enabled | toString) "true") (gt (.Values.injector.replicas | int) 1) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "vault.fullname" . }}-agent-injector-leader-elector-binding
  namespace: hashicorp-vault
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-agent-injector
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "vault.fullname" . }}-agent-injector-leader-elector-role
subjects:
  - kind: ServiceAccount
    name: {{ template "vault.fullname" . }}-agent-injector
    namespace: hashicorp-vault
{{- end }}
{{- end }}
