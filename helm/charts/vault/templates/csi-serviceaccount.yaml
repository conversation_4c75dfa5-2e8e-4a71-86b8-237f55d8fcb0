{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.csiEnabled" . -}}
{{- if .csiEnabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "vault.fullname" . }}-csi-provider
  namespace: hashicorp-vault
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-csi-provider
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if  .Values.csi.serviceAccount.extraLabels -}}
      {{- toYaml .Values.csi.serviceAccount.extraLabels | nindent 4 -}}
    {{- end -}}
  {{ template "csi.serviceAccount.annotations" . }}
{{- end }}
