-- FUNCTION: license.global_search(text, text, text[], text[], integer, integer, text[])

-- DROP FUNCTION IF EXISTS license.global_search(text, text, text[], text[], integer, integer, text[]);

CREATE OR REPLACE FUNCTION license.global_search(
	search_tokens text[],
	table_filters text[] DEFAULT NULL::text[],
	column_filters text[] DEFAULT NULL::text[],
	limit_rows integer DEFAULT NULL::integer,
	offset_rows integer DEFAULT 0,
	participant_types text[] DEFAULT NULL::text[])
    RETURNS TABLE(source_table text, primary_display text, secondary_display text, third_display jsonb, entity_id text, entity_type text, status text)
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $$
DECLARE
  entry JSON;
  i INT;
  query_parts TEXT[] := '{}';
  final_query TEXT;
  table_name TEXT;
  search_config JSON;
BEGIN
  -- Fetch the config from config.json_storage
  SELECT json_data INTO search_config
  FROM config.json_storage
  WHERE properties @> '{"config": "search_specifications"}'::jsonb
  LIMIT 1;

  -- Treat empty arrays as NULL
  IF array_length(participant_types, 1) IS NULL OR array_length(participant_types, 1) = 0 THEN
    participant_types := NULL;
  END IF;

  IF array_length(table_filters, 1) IS NULL OR array_length(table_filters, 1) = 0 THEN
    table_filters := NULL;
  END IF;

  IF array_length(column_filters, 1) IS NULL OR array_length(column_filters, 1) = 0 THEN
    column_filters := NULL;
  END IF;

  FOR i IN 0 .. json_array_length(search_config) - 1 LOOP
    entry := search_config -> i;
    table_name := entry ->> 'table';

    IF table_filters IS NOT NULL AND NOT (table_name = ANY(table_filters)) THEN
      CONTINUE;
    END IF;

    IF table_name = 'license.participant' THEN
      -- Skip this config entry if participant_type does not match
      IF participant_types IS NOT NULL THEN
        IF NOT entry::jsonb ? 'participant_type' THEN
          CONTINUE;
        ELSIF NOT (entry ->> 'participant_type' = ANY(participant_types)) THEN
          CONTINUE;
        END IF;
      END IF;
    END IF;

    query_parts := array_append(query_parts, license.build_query_for_entry(entry, search_tokens, column_filters, participant_types));
  END LOOP;

  IF array_length(query_parts, 1) IS NULL THEN
    RAISE NOTICE 'No matching tables in filters. Skipping execution.';
    RETURN;
  END IF;

  final_query := array_to_string(query_parts, ' UNION ALL ');

  IF limit_rows IS NOT NULL THEN
    final_query := final_query || FORMAT(' LIMIT %s OFFSET %s', limit_rows, offset_rows);
  END IF;

  RAISE NOTICE '%', final_query;
  RETURN QUERY EXECUTE final_query;
END;
$$;
