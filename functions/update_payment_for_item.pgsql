CREATE OR REPLACE FUNCTION update_payment_for_item(
    p_unique_item_id UUID,
    p_payment_type TEXT,
    p_payment_reference TEXT
)
RETURNS TABLE (
    order_id UUID,
    old_payment_type TEXT,
    old_payment_reference TEXT,
    new_payment_type TEXT,
    new_payment_reference TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_order_id UUID;
BEGIN
    -- Step 1: Get order_id from order_item using unique_item_id
    SELECT oi.order_id INTO v_order_id
    FROM order_item oi
    WHERE oi.unique_item_id = p_unique_item_id
    LIMIT 1;

    IF v_order_id IS NULL THEN
        RAISE EXCEPTION 'No order_id found for unique_item_id: %', p_unique_item_id;
    END IF;

    -- Step 2: Return previous payment info + perform update
    RETURN QUERY
    WITH old AS (
        SELECT p.order_id, p.payment_type, p.payment_reference
        FROM payment p
        WHERE p.order_id = v_order_id
    ),
    updated AS (
        UPDATE payment p
        SET payment_type = p_payment_type,
            payment_reference = p_payment_reference
        WHERE p.order_id = v_order_id
        RETURNING p.order_id
    )
    SELECT
        old.order_id,
        old.payment_type::TEXT,
        old.payment_reference::TEXT,
        p_payment_type::TEXT,
        p_payment_reference::TEXT
    FROM old;
END;
$$;
