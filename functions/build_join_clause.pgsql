-- FUNCTION: license.build_join_clause(json, text)

-- DROP FUNCTION IF EXISTS license.build_join_clause(json, text);

CREATE OR REPLACE FUNCTION license.build_join_clause(
	entry json,
	base_alias text)
    RETURNS text
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
AS $$
DECLARE
  join_clause TEXT := '';
  join_entry JSON;
BEGIN
  IF entry::jsonb ? 'joins' THEN
    FOR join_entry IN SELECT * FROM json_array_elements(entry -> 'joins') LOOP
      IF join_entry ->> 'type' = 'simple' THEN
        join_clause := join_clause || FORMAT(
          ' %s %I.%I %s ON %s',
          join_entry ->> 'join_type',
          split_part(join_entry ->> 'table', '.', 1),
          split_part(join_entry ->> 'table', '.', 2),
          join_entry ->> 'alias',
          join_entry ->> 'on'
        );
      ELSIF join_entry ->> 'type' = 'many_to_many' THEN
        join_clause := join_clause || FORMAT(
          ' %s %I.%I %s ON %s.%I = %s.%I',
          join_entry ->> 'join_type',
          split_part(join_entry ->> 'junction_table', '.', 1),
          split_part(join_entry ->> 'junction_table', '.', 2),
          join_entry ->> 'junction_alias',
          join_entry ->> 'junction_alias',
          join_entry ->> 'junction_source_key',
          base_alias,
          join_entry ->> 'source_table_key'
        ) || FORMAT(
          ' %s %I.%I %s ON %s.%I = %s.%I',
          join_entry ->> 'join_type',
          split_part(join_entry ->> 'target_table', '.', 1),
          split_part(join_entry ->> 'target_table', '.', 2),
          join_entry ->> 'target_alias',
          join_entry ->> 'target_alias',
          join_entry ->> 'target_key',
          join_entry ->> 'junction_alias',
          join_entry ->> 'junction_target_key'
        );
      END IF;
    END LOOP;
  END IF;
  RETURN join_clause;
END;
$$;
