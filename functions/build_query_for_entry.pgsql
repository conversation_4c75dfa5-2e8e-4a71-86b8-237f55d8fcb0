CREATE OR <PERSON><PERSON>LACE FUNCTION license.build_query_for_entry(
	entry json,
	search_tokens text[],
	allowed_columns text[] DEFAULT NULL::text[],
	participant_types text[] DEFAULT NULL::text[])
    RETURNS text
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
AS $$
DECLARE
  table_name TEXT := entry ->> 'table';
  schema_name TEXT := split_part(table_name, '.', 1);
  table_only TEXT := split_part(table_name, '.', 2);
  base_alias TEXT := table_only;
  jsonb_column TEXT := 'properties';
  join_clause TEXT;
  where_clause TEXT;
  output_clause TEXT;
BEGIN
  join_clause := license.build_join_clause(entry, base_alias);
  where_clause := license.build_condition_clause(entry, base_alias, jsonb_column, search_tokens, allowed_columns, participant_types);
  output_clause := license.build_output_clause(entry, base_alias, jsonb_column);
  RETURN FORMAT(
    'SELECT %s FROM %I.%I %I %s WHERE %s',
    output_clause, schema_name, table_only, base_alias, join_clause, where_clause
  );
END;
$$;
