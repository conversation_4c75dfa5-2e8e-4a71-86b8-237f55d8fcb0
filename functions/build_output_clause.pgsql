-- FUNCTION: license.build_output_clause(json, text, text)

-- DROP FUNCTION IF EXISTS license.build_output_clause(json, text, text);

CREATE OR REPLACE FUNCTION license.build_output_clause(
	entry json,
	base_alias text,
	jsonb_column text)
    RETURNS text
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
AS $$
DECLARE
  output_fields JSON := entry -> 'output_fields';
  clause_parts TEXT[];
  column_name TEXT;
  is_property BOOLEAN;
  table_only TEXT := split_part(entry ->> 'table', '.', 2);
  third_display_items JSON;
  third_display_parts TEXT[] := '{}';
  third_item JSON;
  field_expr TEXT;
  item_type TEXT;

  -- Define the exact order we need for the function return type
  primary_display_expr TEXT := 'NULL::text';
  secondary_display_expr TEXT := 'NULL::text';
  third_display_expr TEXT := '''[]''::jsonb';
  entity_id_expr TEXT := 'NULL::text';
  entity_type_expr TEXT := 'NULL::text';
  status_expr TEXT := 'NULL::text';
BEGIN
  -- Build source_table first
  clause_parts := ARRAY[FORMAT('%L::text AS source_table', table_only)];

  -- Process primary_display
  IF output_fields::jsonb ? 'primary_display' THEN
    column_name := output_fields -> 'primary_display' ->> 'column';
    is_property := (output_fields -> 'primary_display' ->> 'is_property')::BOOLEAN;

    IF column_name IS NOT NULL THEN
      IF position('.' IN column_name) > 0 THEN
        primary_display_expr := FORMAT('%I.%I::text',
          split_part(column_name, '.', 1),
          split_part(column_name, '.', 2));
      ELSIF is_property THEN
        primary_display_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, column_name);
      ELSE
        primary_display_expr := FORMAT('%I.%I::text', base_alias, column_name);
      END IF;
    ELSIF (output_fields -> 'primary_display')::jsonb ? 'value' THEN
      primary_display_expr := FORMAT('%L::text', output_fields -> 'primary_display' ->> 'value');
    END IF;
  END IF;

  -- Process secondary_display
  IF output_fields::jsonb ? 'secondary_display' THEN
    column_name := output_fields -> 'secondary_display' ->> 'column';
    is_property := (output_fields -> 'secondary_display' ->> 'is_property')::BOOLEAN;

    IF column_name IS NOT NULL THEN
      IF position('.' IN column_name) > 0 THEN
        secondary_display_expr := FORMAT('%I.%I::text',
          split_part(column_name, '.', 1),
          split_part(column_name, '.', 2));
      ELSIF is_property THEN
        secondary_display_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, column_name);
      ELSE
        secondary_display_expr := FORMAT('%I.%I::text', base_alias, column_name);
      END IF;
    ELSIF (output_fields -> 'secondary_display')::jsonb ? 'value' THEN
      secondary_display_expr := FORMAT('%L::text', output_fields -> 'secondary_display' ->> 'value');
    END IF;
  END IF;

  -- Process third_display
  IF output_fields::jsonb ? 'third_display' THEN
    third_display_items := output_fields -> 'third_display';

    -- Check if third_display is an array
    IF json_typeof(third_display_items) = 'array' THEN
      -- Process each item in the third_display array
      FOR third_item IN SELECT * FROM json_array_elements(third_display_items) LOOP
        column_name := third_item ->> 'column';
        is_property := (third_item ->> 'is_property')::BOOLEAN;
        item_type := third_item ->> 'type';

        IF column_name IS NOT NULL THEN
          -- Build field expression
          IF position('.' IN column_name) > 0 THEN
            field_expr := FORMAT('%I.%I',
              split_part(column_name, '.', 1),
              split_part(column_name, '.', 2));
          ELSIF is_property THEN
            field_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, column_name);
          ELSE
            field_expr := FORMAT('%I.%I', base_alias, column_name);
          END IF;

          -- Add to third_display_parts as JSON object
          third_display_parts := array_append(
            third_display_parts,
            FORMAT('json_build_object(''type'', %L, ''value'', %s)', item_type, field_expr)
          );
        END IF;
      END LOOP;

      -- Build the JSON array for third_display
      IF array_length(third_display_parts, 1) > 0 THEN
        third_display_expr := FORMAT('json_build_array(%s)::jsonb', array_to_string(third_display_parts, ', '));
      END IF;
	  RAISE NOTICE '%', third_display_expr;
    ELSE
      -- Handle old single third_display format for backward compatibility
      column_name := third_display_items ->> 'column';
      is_property := (third_display_items ->> 'is_property')::BOOLEAN;
      item_type := COALESCE(third_display_items ->> 'type', 'text');

      IF column_name IS NOT NULL THEN
        IF position('.' IN column_name) > 0 THEN
          field_expr := FORMAT('%I.%I',
            split_part(column_name, '.', 1),
            split_part(column_name, '.', 2));
        ELSIF is_property THEN
          field_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, column_name);
        ELSE
          field_expr := FORMAT('%I.%I', base_alias, column_name);
        END IF;

        third_display_expr := FORMAT('json_build_array(json_build_object(''type'', %L, ''value'', %s))::jsonb', item_type, field_expr);
      END IF;
    END IF;
  END IF;

  -- Process entity_id
  IF output_fields::jsonb ? 'entity_id' THEN
    column_name := output_fields -> 'entity_id' ->> 'column';
    is_property := (output_fields -> 'entity_id' ->> 'is_property')::BOOLEAN;

    IF column_name IS NOT NULL THEN
      IF position('.' IN column_name) > 0 THEN
        entity_id_expr := FORMAT('%I.%I::text',
          split_part(column_name, '.', 1),
          split_part(column_name, '.', 2));
      ELSIF is_property THEN
        entity_id_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, column_name);
      ELSE
        entity_id_expr := FORMAT('%I.%I::text', base_alias, column_name);
      END IF;
    ELSIF (output_fields -> 'entity_id')::jsonb ? 'value' THEN
      entity_id_expr := FORMAT('%L::text', output_fields -> 'entity_id' ->> 'value');
    END IF;
  END IF;

  -- Process entity_type
  IF output_fields::jsonb ? 'entity_type' THEN
    column_name := output_fields -> 'entity_type' ->> 'column';
    is_property := (output_fields -> 'entity_type' ->> 'is_property')::BOOLEAN;

    IF column_name IS NOT NULL THEN
      IF position('.' IN column_name) > 0 THEN
        entity_type_expr := FORMAT('%I.%I::text',
          split_part(column_name, '.', 1),
          split_part(column_name, '.', 2));
      ELSIF is_property THEN
        entity_type_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, column_name);
      ELSE
        entity_type_expr := FORMAT('%I.%I::text', base_alias, column_name);
      END IF;
    ELSIF (output_fields -> 'entity_type')::jsonb ? 'value' THEN
      entity_type_expr := FORMAT('%L::text', output_fields -> 'entity_type' ->> 'value');
    END IF;
  END IF;

  -- Process status
  IF output_fields::jsonb ? 'status' THEN
    column_name := output_fields -> 'status' ->> 'column';
    is_property := (output_fields -> 'status' ->> 'is_property')::BOOLEAN;

    IF column_name IS NOT NULL THEN
      IF position('.' IN column_name) > 0 THEN
        status_expr := FORMAT('%I.%I::text',
          split_part(column_name, '.', 1),
          split_part(column_name, '.', 2));
      ELSIF is_property THEN
        status_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, column_name);
      ELSE
        status_expr := FORMAT('%I.%I::text', base_alias, column_name);
      END IF;
    ELSIF (output_fields -> 'status')::jsonb ? 'value' THEN
      status_expr := FORMAT('%L::text', output_fields -> 'status' ->> 'value');
    END IF;
  END IF;

  -- Build the final clause with exact order matching function return type
  clause_parts := array_append(clause_parts, primary_display_expr || ' AS primary_display');
  clause_parts := array_append(clause_parts, secondary_display_expr || ' AS secondary_display');
  clause_parts := array_append(clause_parts, third_display_expr || ' AS third_display');
  clause_parts := array_append(clause_parts, entity_id_expr || ' AS entity_id');
  clause_parts := array_append(clause_parts, entity_type_expr || ' AS entity_type');
  clause_parts := array_append(clause_parts, status_expr || ' AS status');

  RETURN array_to_string(clause_parts, ', ');
END;
$$;
