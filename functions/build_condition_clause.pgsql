CREATE OR REPLACE FUNCTION license.build_condition_clause(
	entry json,
	base_alias text,
	jsonb_column text,
	search_tokens text[],
	allowed_columns text[] DEFAULT NULL::text[],
	participant_types text[] DEFAULT NULL::text[])
    RETURNS text
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
AS $$
DECLARE
  entity_type_column TEXT;
  alias_name TEXT;
  field_expr TEXT;
  condition_parts TEXT[] := '{}';
  participant_type_filter TEXT[] := '{}';
  search_field JSON;
  field_name TEXT;
  is_property BOOLEAN;
  data_type TEXT;
  match_type TEXT;
  table_name TEXT := entry ->> 'table';
BEGIN
  FOR search_field IN SELECT * FROM json_array_elements(entry -> 'search_fields') LOOP
    BEGIN
      field_name := search_field ->> 'field';
      IF allowed_columns IS NOT NULL AND NOT (field_name = ANY(allowed_columns)) THEN CONTINUE; END IF;
      is_property := (search_field ->> 'is_property')::BOOLEAN;
      data_type := LOWER(COALESCE(search_field ->> 'data_type', 'text'));
      match_type := LOWER(COALESCE(search_field ->> 'match_type', 'contains'));
      IF is_property THEN
        field_expr := FORMAT('%I.%I ->> %L', base_alias, jsonb_column, field_name);
      ELSIF position('.' IN field_name) > 0 THEN
        field_expr := FORMAT('%I.%I', split_part(field_name, '.', 1), split_part(field_name, '.', 2));
      ELSE
        field_expr := FORMAT('%I.%I', base_alias, field_name);
      END IF;
      IF data_type = 'text' THEN
        DECLARE
		    token TEXT;
		    token_conditions TEXT := '';
		BEGIN
		    FOREACH token IN ARRAY search_tokens LOOP
		      IF token IS NULL OR length(token) = 0 THEN CONTINUE; END IF;
		      IF match_type = 'exact' THEN
		        token_conditions := token_conditions || FORMAT('%s = %L OR ', field_expr, token);
		      ELSE
		        token_conditions := token_conditions || FORMAT('%s::text ILIKE %L OR ', field_expr, '%%' || token || '%%');
		      END IF;
		    END LOOP;
		    -- Trim trailing OR and wrap in parentheses
		    IF token_conditions != '' THEN
		      token_conditions := '(' || left(token_conditions, length(token_conditions) - 4) || ')';
		      condition_parts := array_append(condition_parts, token_conditions);
		    END IF;
		END;
      ELSIF match_type = 'range' AND array_length(search_tokens, 1) >= 2 THEN
		  CONTINUE;
	  ELSIF match_type = 'exact' THEN
		  DECLARE
		    full_text TEXT := array_to_string(search_tokens, ' ');
		  BEGIN
		    condition_parts := array_append(
		      condition_parts,
		      FORMAT('%s = %L', field_expr, full_text)
		    );
		  END;
	  END IF;
    EXCEPTION WHEN OTHERS THEN
      CONTINUE;
    END;
  END LOOP;
  -- 🔍️ Add participant.association_type filter if needed
  IF table_name = 'license.participant' AND participant_types IS NOT NULL THEN

    entity_type_column := entry -> 'output_fields' -> 'entity_type' ->> 'column';  -- 'license_pg_dog.name'
    IF entity_type_column IS NOT NULL AND POSITION('.' IN entity_type_column) > 0 THEN
      alias_name := SPLIT_PART(entity_type_column, '.', 1);  -- 'license_pg_dog'
    END IF;

    participant_type_filter := array_append(
      participant_type_filter,
      FORMAT('%I.name IN (%s)',
        alias_name,
        array_to_string(
          ARRAY(
            SELECT quote_literal(val)
            FROM unnest(participant_types) val
          ), ', '
        )
      )
    );
  END IF;
  IF array_length(participant_type_filter, 1) IS NOT NULL THEN
    RETURN '(' || array_to_string(condition_parts, ' OR ') || ') AND (' || array_to_string(participant_type_filter, ' AND ') || ')';
  ELSE
    RETURN array_to_string(condition_parts, ' OR ');
  END IF;
END;
$$;
