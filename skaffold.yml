apiVersion: skaffold/v4beta1
kind: Config
metadata:
  name: backend
build:
  artifacts:
    - image: service_record
      jib:
        project: com.scube.record:record-application

manifests:
  rawYaml:
    - deployment.yml

profiles:
  # Local cluster - we build and deploy things locally
  - name: local
    build:
      local:
        push: false
    activation:
      - kubeContext: docker-desktop
    deploy:
      kubectl:
        defaultNamespace: backend