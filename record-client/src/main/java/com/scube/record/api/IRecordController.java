package com.scube.record.api;

import com.scube.client.annotation.AddToServiceClient;
import com.scube.client.annotation.GenerateHttpExchangeProxy;
import com.scube.client.annotation.HttpExchangeWebClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.*;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@HttpExchangeWebClient("record-service")
@AddToServiceClient("record-service")
@GenerateHttpExchangeProxy
public interface IRecordController {

    @PostExchange("/records")
    RecordResponse createRecord(@RequestBody CreateRecordRequest request);

    @GetExchange("/records/{uuid}")
    RecordResponse getRecord(@PathVariable UUID uuid);

    @GetExchange("/records")
    List<RecordResponse> getAllRecords();

    @GetExchange("/records/type/{recordType}")
    Page<RecordResponse> getRecordsByType(
            @PathVariable String recordType,
            @RequestParam(required = false) String status,
            Pageable pageable);

    @PutExchange("/records/{uuid}")
    RecordResponse updateRecord(@PathVariable UUID uuid, @RequestBody UpdateRecordRequest request);

    @DeleteExchange("/records/{uuid}")
    void deleteRecord(@PathVariable UUID uuid);

    @GetExchange("/records/search")
    List<RecordResponse> searchRecords(@RequestParam String keyword);

    @GetExchange("/records/health")
    HealthResponse health();
}