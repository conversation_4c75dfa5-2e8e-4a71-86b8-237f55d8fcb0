package com.scube.licensing.infrastructure.middleware;

import lombok.extern.slf4j.Slf4j;
import org.axonframework.messaging.InterceptorChain;
import org.axonframework.messaging.MessageHandlerInterceptor;
import org.axonframework.messaging.unitofwork.UnitOfWork;
import org.axonframework.queryhandling.QueryMessage;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nonnull;

@Component
@Order(1)
@Slf4j
public class QueryLoggingInterceptor implements MessageHandlerInterceptor<QueryMessage<?, ?>> {

    @Override
    public Object handle(@Nonnull UnitOfWork<? extends QueryMessage<?, ?>> unitOfWork, @Nonnull InterceptorChain interceptorChain) throws Exception {
        var commandName = unitOfWork.getMessage().getPayloadType().getPackageName();
        log.info("Start {}", commandName);

        Object result = interceptorChain.proceed();

        log.info("End {}", commandName);
        return result;
    }
}