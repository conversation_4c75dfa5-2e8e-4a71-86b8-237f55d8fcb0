package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddressType;
import jakarta.validation.constraints.Size;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ParticipantAddressTypeRepository extends AuditableEntityRepository<ParticipantAddressType, Long> {
    Optional<ParticipantAddressType> findByNameIgnoreCase(@Size(max = 255) String name);
}
