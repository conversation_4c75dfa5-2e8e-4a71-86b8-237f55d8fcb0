package com.scube.licensing.features.license.scheduling.service;

import com.scube.licensing.features.license.change_status.ChangeStatusCommand;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.repository.license.LicenseRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@AllArgsConstructor
@Service
@Slf4j
public class ExpirationScedulingService {
    private final LicenseRepository licenseRepository;
    private final AxonGateway axonGateway;

    @Transactional
    public void expireLicense() {
        log.info("ExpirationScedulingService.expireLicense()");
        LocalDate yesterday = LocalDate.now();

        List<License> delinquentLicenses = licenseRepository.findByValidToDateLessThanEqualToIgnoringTime(yesterday);

        for (License license : delinquentLicenses) {
            log.debug("Expiring License: {}", license.getUuid());
            axonGateway.sendAndWait(new ChangeStatusCommand(license.getUuid(), "Expired"));
        }
    }
}
