package com.scube.licensing.features.license.fee.sql_fees;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.lib.misc.MapUtils;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Data
public class FeeCalculationByDurationResponse {
    private List<FeeCalculationResponse> items;

    @JsonIgnore
    private Map<String, Object> durationResult;

    @JsonAnyGetter
    public Map<String, Object> getDurationResultFlatMap() {
        return durationResult;
    }

    public FeeCalculationByDurationResponse(List<FeeCalculationResponse> items, Map<String, Object> durationResult) {
        this.items = items.stream()
                .sorted(Comparator.comparing(FeeCalculationResponse::getLabel))
                .toList();

        this.durationResult = durationResult != null ? durationResult : new HashMap<>();

        this.durationResult = MapUtils.toObjectMapFromObjectMap(durationResult);
    }

    public String findMatchingLabel(Integer duration, Integer startYear, Integer endYear) {
        if (!ObjectUtils.isEmpty(startYear) && !ObjectUtils.isEmpty(endYear)) {
            return items.stream()
                    .filter(x -> Objects.equals(x.getStartYear(), startYear) && Objects.equals(x.getEndYear(), endYear))
                    .findFirst()
                    .map(FeeCalculationResponse::getLabel)
                    .orElse(null);
        }
        return items.stream()
                .filter(x -> Objects.equals(Optional.ofNullable(x.getDuration()).orElse(1), duration))
                .findFirst()
                .map(FeeCalculationResponse::getLabel)
                .orElse(null);
    }
}