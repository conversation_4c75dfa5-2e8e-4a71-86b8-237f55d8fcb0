package com.scube.licensing.features.license.partial_save.create_initial_license;

import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.license.exception.LicenseStatusNotFoundException;
import com.scube.licensing.features.license.exception.LicenseTypeNotFoundException;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Calendar;

@Component
@Slf4j
@RequiredArgsConstructor
public class CreateInitialLicenseHandler implements IRequestHandlerAxon<CreateInitialLicenseCommand, CreateInitialLicenseResponse> {
    private final LicenseService licenseService;
    private final ProfileService profileService;


    @Override
    @CommandHandler
    @Transactional
    public CreateInitialLicenseResponse handle(CreateInitialLicenseCommand command) {
        log.info("Creating pending license with {}", command);

        var licenseType = licenseService.findLicenseTypeByCodeOrElseThrow(command.licenseType());
        if (ObjectUtils.isEmpty(licenseType))
            throw new LicenseTypeNotFoundException(command.licenseType());

        var setting = licenseService.findSettingByLicenseTypeIdOrElseThrow(licenseType.getId());
        var licenseStatus = setting.getOnInitialFormCreateLicenseStatus();
        if (ObjectUtils.isEmpty(licenseStatus))
            throw new LicenseStatusNotFoundException("No initial license status is configured for license type " + command.licenseType());

        var licenseHolder = profileService.getProfileOrElseThrow(command.participantId(), Participant.class);

        License license = new License().setLicenseType(licenseType)
                .setLicenseStatus(licenseStatus);
        var currentYear = Calendar.getInstance().get(Calendar.YEAR);
        if (license.getLicenseType().getCode().equalsIgnoreCase("dogLicense")) {
            license.setLicenseNumber(String.format("%sPENDDL%s", currentYear, licenseService.getNextPendingDogLicenseNumber()));
        } else if (license.getLicenseType().getCode().equalsIgnoreCase("purebredDogLicense")) {
            license.setLicenseNumber(String.format("%sPENDPDL%s", currentYear, licenseService.getNextPendingPurebredDogLicenseNumber()));
        }

        //associate the dog owner with the license
        license.addBiDirectionalAssociable(licenseHolder);

        license = licenseService.save(license);

        log.info("End creating Pending license for entity: {}", license);
        return new CreateInitialLicenseResponse(license.getUuid());
    }
}