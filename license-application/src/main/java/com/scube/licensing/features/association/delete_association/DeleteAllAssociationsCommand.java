package com.scube.licensing.features.association.delete_association;

import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;

public record DeleteAllAssociationsCommand(Associable associable) implements IRequestVoidAxon {
    public DeleteAllAssociationsCommand {
        if (associable == null || associable.getId() == null)
            throw new IllegalArgumentException("association cannot be null");
    }
}