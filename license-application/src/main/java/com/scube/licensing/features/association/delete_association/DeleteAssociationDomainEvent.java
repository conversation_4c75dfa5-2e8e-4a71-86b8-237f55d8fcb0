package com.scube.licensing.features.association.delete_association;

import com.scube.licensing.infrastructure.db.entity.association.Associable;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
public class DeleteAssociationDomainEvent {
    private Associable parent;
    private Associable child;

    private String parentEntityType;
    private UUID parentEntityId;

    private String childEntityType;
    private UUID childEntityId;

    public DeleteAssociationDomainEvent(Associable parent, Associable child) {
        this.parent = parent;
        this.child = child;
    }

    public static DeleteAssociationDomainEvent child(Associable parent, String childEntityType, UUID childEntityId) {
        var event = new DeleteAssociationDomainEvent();
        event.parent = parent;
        event.childEntityType = childEntityType;
        event.childEntityId = childEntityId;
        return event;
    }

    public static DeleteAssociationDomainEvent parent(Associable child, String parentEntityType, UUID parentEntityId) {
        var event = new DeleteAssociationDomainEvent();
        event.child = child;
        event.parentEntityType = parentEntityType;
        event.parentEntityId = parentEntityId;
        return event;
    }
}
