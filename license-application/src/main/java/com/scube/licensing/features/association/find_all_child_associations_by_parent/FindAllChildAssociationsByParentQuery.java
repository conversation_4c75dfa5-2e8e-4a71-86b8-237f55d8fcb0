package com.scube.licensing.features.association.find_all_child_associations_by_parent;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import org.springframework.util.ObjectUtils;

import java.util.List;

public record FindAllChildAssociationsByParentQuery(Associable parent) implements IRequestAxon<List<Association>> {
    public FindAllChildAssociationsByParentQuery {
        if (ObjectUtils.isEmpty(parent))
            throw new IllegalArgumentException("Parent is required");

        if (ObjectUtils.isEmpty(parent.getId()))
            throw new IllegalArgumentException("Parent id is required");

        if (ObjectUtils.isEmpty(parent.getAssociationType()))
            throw new IllegalArgumentException("Parent association type is required");
    }
}