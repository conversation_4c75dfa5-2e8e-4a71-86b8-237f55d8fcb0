package com.scube.licensing.features.merge_request.events;

import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.Data;

import java.util.UUID;

@Data
public class MergeRequestApprovedEvent implements IRabbitFanoutPublisher {
    private UUID requestedUserId;
    private String searchBy;
    private String searchValue;

    public MergeRequestApprovedEvent(MergeRequest request) {
        this.requestedUserId = request.getRequestedUserId();
        this.searchBy = request.getSearchBy();
        this.searchValue = request.getSearchValue();
    }
}