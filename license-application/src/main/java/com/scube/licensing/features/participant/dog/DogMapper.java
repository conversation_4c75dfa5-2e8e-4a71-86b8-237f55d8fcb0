package com.scube.licensing.features.participant.dog;

import com.scube.config_utils.json_storage.utils.JsonStorageUtils;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.features.participant.dog.dto.PublicDogDto;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Mapper(componentModel = "spring")
@Slf4j
public abstract class DogMapper {
    @Autowired
    protected DocumentService documentService;

    @Mapping(target = "dog.customFields", source = "properties")
    @Mapping(target = "dog.uuid", source = "uuid")
    @Mapping(target = "dog.lost", expression = "java(participant.isLostDog())")
    @Mapping(target = "dog.avatar", expression = "java(downloadDocument(participant.getAvatarDocument()))")
    @Mapping(target = "license.licenseStatus", expression = "java(participant.getLicenses().getFirst().getLicenseStatus().getName())")
    @Mapping(target = "contacts", expression="java(getLostDogContactInformation())")
    public abstract PublicDogDto toPublicDogDto(Participant participant);

    public byte[] downloadDocument(Document document) {
        if (document == null || document.getDocumentServiceUuid() == null) return null;

        try {
            return documentService.download(document).getContentAsByteArray();
        } catch (Exception e) {
            log.error("Error while retrieving document", e);
            return null;
        }
    }

    public Object getLostDogContactInformation() {
        return JsonStorageUtils.get(Map.of("lostDog", "contactInformation"));
    }
}
