package com.scube.licensing.features.document.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.profile.dto.EventDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
public class DocumentDto implements IEntityDto, IAssociableDto {
    @JsonIgnore
    private Long id;
    @JsonIgnore
    private List<EventDto> events;
    private UUID entityId;
    private String contentType;
    private String fileName;
    private Long size;
    @JsonIgnore
    private String url;
    private String documentUuid;
    private String key;
    private String name;
    private String group;
    @JsonIgnore
    private String tableName;
    private String createdDate;
    private String createdBy;
    private boolean isDeleted;
    private String deletedDate;

    @JsonAnyGetter
    private Map<String, Object> customFields;

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}
