package com.scube.licensing.features.license.change_status;

import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.license.License;
import lombok.AllArgsConstructor;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@AllArgsConstructor
public class ChangeStatusHandler {
    private final LicenseService licenseService;

    @CommandHandler
    @Transactional
    public void handle(ChangeStatusCommand command) {
        licenseService.changeStatus(command.entityId(), command.status(), null, true);
    }

    @CommandHandler
    public void changeStatus(ChangeLicenseStatusWithModifierCommand command) {
        licenseService.changeStatus(command.license(), command.status(), command.modifier(), command.publishEvent());
    }

    @CommandHandler
    public void changeAllLicenseModifiers(ChangeAllLicenseModifiersCommand command) {
        licenseService.changeAllLicenseModifiers(command.profile(), command.modifier());
    }

    // @formatter:off
    public record ChangeLicenseStatusWithModifierCommand(License license, String status, String modifier, boolean publishEvent) implements IRequestVoidAxon { }
    public record ChangeAllLicenseModifiersCommand(Associable profile, String modifier) implements IRequestVoidAxon { }
}