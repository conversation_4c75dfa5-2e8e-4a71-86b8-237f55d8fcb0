drop function if exists get_profile_affiliation;

CREATE OR REPLACE FUNCTION get_profile_affiliation(
    IN fn_profile_type varchar(255),
    IN fn_entity_id uuid
)
RETURNS TABLE (
    "items.entityType" TEXT,
    "items.entityId" TEXT,
    "items.avatarUrl" TEXT,
    "items.licenseForm" TEXT,
    "items.latitude" TEXT,
    "items.longitude" TEXT,
    "items.primaryDisplay" TEXT,
    "items.secondaryDisplay" TEXT,
    "items.thirdDisplay.type" TEXT,
    "items.thirdDisplay.value" TEXT,
    "entityType" TEXT,
	"entityId" TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN

	if lower(fn_profile_type) = 'individual' then
	    RETURN QUERY
	    SELECT
	        address."entityType"::TEXT as "items.entityType",
	        address."entityId"::TEXT as "items.entityId",
	        address."avatarUrl"::TEXT as "items.avatarUrl",
	        address."licenseForm"::TEXT as "items.licenseForm",
	        address."latitude"::TEXT as "items.latitude",
	        address."longitude"::TEXT as "items.longitude",
	        (address."primaryDisplay" || ' ('|| pa.address_type || ')')::TEXT as "items.primaryDisplay",
	        address."secondaryDisplay"::TEXT as "items.secondaryDisplay",
	        address."thirdDisplay.type"::TEXT as "items.thirdDisplay.type",
	        address."thirdDisplay.value"::TEXT as "items.thirdDisplay.value",
	        'individual'::TEXT as "entityType",
	        fn_entity_id::TEXT as "entityId"
	    FROM view_participant p

	    inner join view_participant_address pa
            on pa.participant_id = p.participant_id

        inner join get_profile_header('parcel', pa.entity_id) as address
            on address."entityId" = pa.entity_id

        WHERE p.entity_id = fn_entity_id
        UNION ALL
        SELECT
            dog."entityType"::TEXT as "items.entityType",
            dog."entityId"::TEXT as "items.entityId",
            dog."avatarUrl"::TEXT as "items.avatarUrl",
            dog."licenseForm"::TEXT as "items.licenseForm",
            dog."latitude"::TEXT as "items.latitude",
            dog."longitude"::TEXT as "items.longitude",
            dog."primaryDisplay"::TEXT as "items.primaryDisplay",
            dog."secondaryDisplay"::TEXT as "items.secondaryDisplay",
            dog."thirdDisplay.type"::TEXT as "items.thirdDisplay.type",
            dog."thirdDisplay.value"::TEXT as "items.thirdDisplay.value",
            'individual'::TEXT as "entityType",
            fn_entity_id::TEXT as "entityId"
        FROM view_participant p

        inner join association a
            on a.parent_id = p.participant_id
            and a.parent_association_type = 'PARTICIPANT'
            and a.child_association_type = 'PARTICIPANT'

        inner join view_participant d
            on d.participant_id = a.child_id
            and d.group_name = 'Dog'

         inner join get_profile_header('dog',  d.entity_id) as dog
            on dog."entityId" = d.entity_id

        WHERE p.entity_id = fn_entity_id;


	END IF;
END;
$$;

-- SELECT * FROM get_profile_affiliation('individual', 'c126150b-2312-4e13-b3c8-443255f4bdde');