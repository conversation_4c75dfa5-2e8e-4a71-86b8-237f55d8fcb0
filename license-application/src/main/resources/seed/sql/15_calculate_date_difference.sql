CREATE OR REPLACE FUNCTION calculate_date_difference(start_date timestamp, end_date timestamp)
RETURNS text AS $$
DECLARE
    year_diff integer;
    day_diff integer;
    suffix text;
BEGIN
    IF end_date >= start_date THEN
        year_diff := EXTRACT(YEAR FROM age(end_date, start_date));
        day_diff := EXTRACT(DAY FROM end_date - (start_date + interval '1 year' * year_diff));
        suffix := ' remaining';
    ELSE
        year_diff := EXTRACT(YEAR FROM age(start_date, end_date));
        day_diff := EXTRACT(DAY FROM start_date - (end_date + interval '1 year' * year_diff));
        suffix := ' ago';
    END IF;

    RETURN
        CASE
            WHEN year_diff > 0 AND day_diff > 0 THEN
                year_diff || ' year' || (CASE WHEN year_diff > 1 THEN 's' ELSE '' END) || ' and ' || day_diff || ' day' || (CASE WHEN day_diff > 1 THEN 's' ELSE '' END) || suffix
            WHEN year_diff > 0 THEN
                year_diff || ' year' || (CASE WHEN year_diff > 1 THEN 's' ELSE '' END) || suffix
            WHEN day_diff > 0 THEN
                day_diff || ' day' || (CASE WHEN day_diff > 1 THEN 's' ELSE '' END) || suffix
            ELSE
                'Less than a day' || suffix
        END;
END;
$$ LANGUAGE plpgsql;
