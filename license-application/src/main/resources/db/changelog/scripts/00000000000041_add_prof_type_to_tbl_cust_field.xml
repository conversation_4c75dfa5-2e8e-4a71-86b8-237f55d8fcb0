<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addProfileTypeToTblCustField" author="Ben">
        <addColumn tableName="table_custom_field">
            <column name="profile_type_id" type="bigint"/>
        </addColumn>
        <addColumn tableName="audit_log_table_custom_field">
            <column name="profile_type_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet id="populateTblCustFieldProfileTypeId" author="Ben">
        <sql>
            update table_custom_field
            set profile_type_id = (SELECT profile_type_id FROM profile_type WHERE name = 'individual')
            where table_type_id in (select table_type_id from table_type where name = 'participant')
            and name in ('title', 'firstName',
            'lastName', 'middleName', 'suffix', 'dateOfBirth', 'mailingSameAsPrimary');

            update table_custom_field
            set profile_type_id = (SELECT profile_type_id FROM profile_type WHERE name = 'dog')
            where table_type_id in (select table_type_id from table_type where name = 'participant')
            and name not in ('title', 'firstName',
            'lastName', 'middleName', 'suffix', 'dateOfBirth', 'mailingSameAsPrimary', 'avatarUrl');
        </sql>
    </changeSet>
</databaseChangeLog>
