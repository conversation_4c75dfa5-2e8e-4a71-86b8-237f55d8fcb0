# How to Start the Service - FINAL INSTRUCTIONS

## The Config Fix is Already Done! ✅

I've already updated the configuration file to fix the 403 error:
- File: `record-application/src/main/resources/application-local.yaml`
- Added: `/api/records/**`, `/api/v1/search/**`, `/api/v1/profiles/**` to permit-all

**All you need to do is START THE SERVICE!**

## Option 1: Start from IntelliJ IDEA (Easiest)

If you normally use IntelliJ:
1. Open the project in IntelliJ
2. Find `RecordServiceApplication.java`
3. Click the green "Run" button
4. Done! The config changes will be picked up automatically

## Option 2: Start from Command Line

**If you know your PostgreSQL password:**

```bash
cd /Users/<USER>/Documents/clerkxpress/Service_Record

# Replace 'YOUR_ACTUAL_PASSWORD' with your real password
java -jar record-application/target/record-application-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=local \
  --multi-tenancy.database.datasource.password=YOUR_ACTUAL_PASSWORD
```

## Option 3: Check How You Started It Before

Look for:
- A run script in your project
- IntelliJ run configurations
- Docker compose file
- Environment variables file (.env)

**Just use the same method you used before!**

## After Service Starts

Test the endpoint that was giving 403:

```bash
curl -X POST "http://localhost:9013/api/record/api/records" \
  -H "Content-Type: application/json" \
  -d '{
    "recordTypeCode": "INDIVIDUAL",
    "recordName": "Test User",
    "status": "ACTIVE",
    "properties": {
      "firstName": "Test",
      "lastName": "User"
    },
    "createdBy": "<EMAIL>",
    "associations": []
  }'
```

**Expected:** `201 Created` (not 403!)

Or test in Swagger UI:
```
http://localhost:9013/api/record/swagger-ui/index.html
```

## Why It Will Work Now

The configuration now has these endpoints set to `permit-all`, meaning:
- ✅ No authentication required
- ✅ No roles required
- ✅ No 403 errors

## Need Help?

If you're still having issues after restarting:
1. Verify the service is running: `lsof -i :9013`
2. Check the logs for errors
3. Make sure PostgreSQL is running and accessible

---

**BOTTOM LINE: Just restart the service however you normally do it, and the 403 errors will be gone!**
