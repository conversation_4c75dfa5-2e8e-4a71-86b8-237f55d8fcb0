<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.4</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.scube.record</groupId>
    <artifactId>RecordService</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>RecordService</name>
    <description>Record Management Service for ClerkXpress</description>
    <modules>
        <module>record-application</module>
        <module>record-client</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <jib.version>3.4.5</jib.version>
        <liquibase.version>4.22.0</liquibase.version>

        <service-dependicies.version>1.1.4</service-dependicies.version>

        <registry>514329541303.dkr.ecr.us-east-1.amazonaws.com</registry>
        <imagename>service_record</imagename>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.scube.dependencies</groupId>
                <artifactId>ServiceDependiciesLibrary</artifactId>
                <version>${service-dependicies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib.version}</version>
                <configuration>
                    <from>
                        <image>eclipse-temurin:21-jre-alpine</image>
                    </from>
                    <to>
                        <image>${registry}/${imagename}</image>
                    </to>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>