package com.scube.imageprocessing.service.ocr.textract.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdResponse {
    private String firstName;
    private String middleName;
    private String lastName;
    private String suffix;
    private String dateOfBirth;
    private String phone;
    private String address;
    private String address2;
    private String city;
    private String state;
    private String zip;
    private String identificationType;
}
