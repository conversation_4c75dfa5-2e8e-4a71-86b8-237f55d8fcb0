package com.scube.report.features.licensing.dog.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.licensing.features.license.reports.dto.gen_dto.ReportQueryRequest;
import com.scube.licensing.features.qr_codes.dto.gen_dto.GenerateMultipleQrCodeCommand;
import com.scube.licensing.features.qr_codes.rabbit.gen_dto.GenerateQrCodeCommand;
import com.scube.licensing.features.qr_codes.rabbit.gen_dto.GenerateQrCodeCommandResponse;
import com.scube.report.features.base.service.IReportQueryService;
import com.scube.report.features.licensing.dog.constants.ReportConstants;
import com.scube.report.features.licensing.dog.dto.reports.DogLicenseRenewalNotice;
import com.scube.report.features.licensing.dog.dto.request.ReportRequest;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;

import static com.scube.report.features.base.util.ValidationUtil.validate;
import static com.scube.report.features.licensing.dog.service.RenewalNoticeService.REPORT_NAME;
import static com.scube.report.features.licensing.dog.util.DogLicenseReportUtil.mergeLicenses;

@Service(REPORT_NAME)
@Slf4j
@RequiredArgsConstructor
public class RenewalNoticeService implements IReportQueryService {
    public final LicenseServiceConnection licenseService;
    public static final String REPORT_NAME = "DogLicenseRenewalNotice";
    private final LicenseServiceConnection licenseServiceConnection;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Object execute(Map<String, Object> params) {
        ReportRequest request = new ReportRequest(params);
        validate(request);

        String sql;
        Object[] sqlParams;
        if (params.containsKey("entityId")) {
            sql = """
                    SELECT * FROM license.view_pivoted_license
                    WHERE license_status_code = 'ACTIVE'
                    AND entity_id = ?
                    ORDER by "owner.lastName", "owner.dateOfBirth"
                    """;
            sqlParams = new Object[]{params.get("entityId")};
        } else if (params.containsKey("ownerEntityId")) {
            sql = """
                    SELECT * FROM license.view_pivoted_license
                    WHERE license_status_code = 'ACTIVE'
                    AND "owner.entityId" = ?
                    ORDER by "owner.lastName", "owner.dateOfBirth"
                    """;
            sqlParams = new Object[]{params.get("ownerEntityId")};
        } else {
            sql = """
                    SELECT * FROM license.view_pivoted_license
                    WHERE license_status_code = 'ACTIVE'
                    AND DATE(valid_to_date) BETWEEN DATE(?) AND DATE(?)
                    AND "owner.paperless" = false
                    ORDER by "owner.lastName", "owner.dateOfBirth"
                    """;
            sqlParams = new Object[]{params.get("startDate"), params.get("endDate")};
        }

        var result = licenseService.report().getDogLicenseRenewalNoticeAsync(new ReportQueryRequest(sql, sqlParams));

        DateTimeFormatter usaDate = DateTimeFormatter.ofPattern("MM/dd/yyyy");

        List<Map<String, Object>> licenses = mergeLicenses(result.collectList().block());

        Assert.notNull(licenses, "No licenses found for the given date range.");
        for (Map<String, Object> license : licenses) {
            LocalDate currentExpirationDate = LocalDate.parse((String) license.get("valid_to_date"), usaDate);
            LocalDate renewalExpirationDate = currentExpirationDate.plusYears(1);
            LocalDate vaccineGracePeriod = currentExpirationDate.plusDays(30);
            LocalDate currentDate = LocalDate.now();

            license.put("renExpDate", renewalExpirationDate.format(usaDate));

            List<Map<String, Object>> dogs = (List<Map<String, Object>>) license.get("dog");

            for (Map<String, Object> dog : dogs) {
                try {
                    LocalDate vaccineExpirationDate = LocalDate.parse((String) dog.getOrDefault("vaccineDueDate", null), usaDate);

                    if (vaccineExpirationDate.isBefore(vaccineGracePeriod)) {
                        dog.put("renewalRequirements", "Requirements:");
                        dog.put("rabiesText", "Rabies immunization required.");
                    }

                    //check is rabies valid
                    if (vaccineExpirationDate.isAfter(currentDate)) {
                        dog.put("rabiesValid", "Valid");
                    } else {
                        dog.put("rabiesValid", "Expired");
                    }
                } catch (Exception e) {
                    log.error("Error parsing vaccine expiration date for license: " + license.get("license_number") + " dog: " + dog.get("dog_name"));
                }

                if ("yes".equalsIgnoreCase((String) dog.get("dogSpayedOrNeutered"))) {
                    if ("Female".equalsIgnoreCase((String) dog.get("dogSex"))) {
                        dog.put("dogAlteration", "Spayed");
                    } else {
                        dog.put("dogAlteration", "Neutered");
                    }
                } else {
                    if ("Female".equalsIgnoreCase((String) dog.get("dogSex"))) {
                        dog.put("dogAlteration", "Not Spayed");
                    } else {
                        dog.put("dogAlteration", "Not Neutered");
                    }
                }
            }
        }

        var start = System.currentTimeMillis();
        generateQrCodes(licenses);
        log.info("generateQrCodes: Took {}ms", System.currentTimeMillis() - start);

        for (int i = 0; i < licenses.size() - 1; i++) {
            licenses.get(i).put("pageBreak", ReportConstants.PAGE_BREAK);
        }

        return new DogLicenseRenewalNotice(licenses);
    }

    @SneakyThrows
    public void generateQrCodes(List<Map<String, Object>> licenses) {
        var licenseEntityIds = new HashMap<UUID, Map<String, Object>>();
        var ownerEntityIds = new HashMap<UUID, List<Map<String, Object>>>();
        for (Map<String, Object> license : licenses) {
            var licenseEntityId = (String) license.get("entity_id");
            Assert.notNull(licenseEntityId, "License entity id is null");
            licenseEntityIds.put(UUID.fromString(licenseEntityId), license);

            var owners = (List<Map<String, Object>>) license.get("owner");
            if (ObjectUtils.isEmpty(owners)) continue;
            for (var owner : owners) {
                var ownerEntityId = (String) owner.get("entityId");
                Assert.notNull(ownerEntityId, "Owner entity id is null");
                ownerEntityIds.computeIfAbsent(UUID.fromString(ownerEntityId), k -> new ArrayList<>()).add(owner);
            }
        }

        var commands = new ArrayList<GenerateQrCodeCommand>();
        for (var licenseEntityId : licenseEntityIds.keySet()) {
            commands.add(createQrCodeCommand("license", licenseEntityId, "lookup"));
            commands.add(createQrCodeCommand("license", licenseEntityId, "renew"));
        }

        for (var ownerEntityId : ownerEntityIds.keySet()) {
            commands.add(createQrCodeCommand("individual", ownerEntityId, "register"));
        }

        Flux<GenerateQrCodeCommandResponse> result = licenseServiceConnection.qrCode().generateBatchAsync(new GenerateMultipleQrCodeCommand(commands));
        Assert.notNull(result, "Failed to generate qr codes");

        var latch = new CountDownLatch(1);
        result.
                doOnComplete(latch::countDown)
                .subscribe(response -> processQrCode(response, licenseEntityIds, ownerEntityIds));

        latch.await();
    }

    private Object processQrCode(GenerateQrCodeCommandResponse response, HashMap<UUID, Map<String, Object>> licenseEntityIds, HashMap<UUID, List<Map<String, Object>>> ownerEntityIds) {
        var request = response.getRequest();
        var entityId = UUID.fromString(request.getEntityId());
        switch (request.getEntityType().toLowerCase()) {
            case "license" -> {
                var license = licenseEntityIds.get(entityId);
                Assert.notNull(license, "License not found for entity id: " + entityId);
                switch (request.getAction().toLowerCase()) {
                    case "lookup" -> license.put("licenseQrCode", generateQrCode(response, 60, 60));
                    case "renew" -> license.put("renewQrCode", generateQrCode(response, 80, 80));
                    default ->
                            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid action: " + request.getAction());
                }
            }
            case "individual" -> {
                var owners = ownerEntityIds.get(entityId);
                Assert.notNull(owners, "Owner not found for entity id: " + entityId);
                for (var owner : owners) {
                    owner.put("registerQrCode", generateQrCode(response, 100, 100));
                }
            }
            default ->
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid entity type: " + request.getEntityType());
        }

        return null;
    }


    private GenerateQrCodeCommand createQrCodeCommand(String entityType, UUID entityId, String action) {
        var result = new GenerateQrCodeCommand();
        result.setEntityType(entityType);
        result.setEntityId(String.valueOf(entityId));
        result.setAction(action);
        return result;
    }

    public JsonNode generateQrCode(GenerateQrCodeCommandResponse response, int length, int width) {
        Object source = response.getBytes();
        TemplateImage qrCodeImage = new TemplateImage(source, length, width);
        // add the qr code image to the license data
        return objectMapper.valueToTree(qrCodeImage);
    }

    @Data
    public static class TemplateImage {
        @JsonProperty("_type")
        private String type = "image";

        private Object source;
        private String format = "image/png";
        private int width;
        private int height;

        public TemplateImage(Object source, int length, int width) {
            this.source = source;
            this.width = width;
            this.height = length;
        }
    }
}
