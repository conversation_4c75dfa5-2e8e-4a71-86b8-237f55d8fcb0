package com.scube.report.features.licensing.dog.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReportRequest {
    @NotEmpty(message = "startDate must not be empty")
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "startDate must be a date in the format yyyy-MM-dd")
    String startDate;
    @NotEmpty(message = "endDate must not be empty")
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "startDate must be a date in the format yyyy-MM-dd")
    String endDate;

    public ReportRequest(Map<String, Object> params) {
        this.startDate = (String) params.get("startDate");
        this.endDate = (String) params.get("endDate");
    }
}
