# Record Service

The Record Service is a microservice in the ClerkXpress ecosystem that manages record operations and data.

## Features

- CRUD operations for records
- Record search and filtering
- Multi-tenant support
- Event-driven architecture with RabbitMQ
- Auto-generated service clients
- JWT-based security with role-based access control

## API Endpoints

### Record Management
- `POST /api/record/records` - Create a new record
- `GET /api/record/records/{uuid}` - Get record by UUID
- `GET /api/record/records` - Get all records
- `GET /api/record/records/type/{recordType}` - Get records by type
- `PUT /api/record/records/{uuid}` - Update record
- `DELETE /api/record/records/{uuid}` - Delete record
- `GET /api/record/records/search?keyword=` - Search records

### Health Check
- `GET /api/record/records/health` - Service health check

## Configuration

The service uses the standard ClerkXpress configuration pattern:

```yaml
server:
  port: 9011
  servlet:
    context-path: /api/record

spring:
  application:
    name: RecordService
```

## Dependencies

### Other Services
- License Service (example integration)
- Document Service (example integration)

### Infrastructure
- PostgreSQL database
- RabbitMQ for messaging
- Keycloak for authentication

## Development

### Prerequisites
- Java 21
- Maven 3.8+
- PostgreSQL 14+
- RabbitMQ (if using messaging features)

### Running Locally

1. Start the database:
```bash
docker run -d --name postgres-record \
  -e POSTGRES_DB=postgres \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 postgres:14
```

2. Run the application:
```bash
./mvnw spring-boot:run -pl record-application
```

### Testing

Run tests with:
```bash
./mvnw test
```

## Usage in Other Services

To use this service in other microservices:

1. Add dependency to your POM:
```xml
<dependency>
    <groupId>com.scube.record</groupId>
    <artifactId>record-client</artifactId>
    <version>1.0.0</version>
</dependency>
```

2. Configure service URL in application.yaml:
```yaml
com.scube.client:
  record: "http://localhost:9011/api/record"
```

3. Inject and use the service connection:
```java
@Service
public class MyService {
    private final RecordServiceConnection recordService;

    public void useRecordService() {
        var records = recordService.record().getAllRecords();
    }
}
```

## Deployment

Build Docker image:
```bash
./mvnw clean package
docker build -t service_record .
```

Deploy to Kubernetes using the provided deployment.yml configuration.