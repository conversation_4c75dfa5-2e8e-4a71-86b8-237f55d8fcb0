# Database Migration - conversion_reference Column Added

## What Was Done

Instead of ignoring the `conversion_reference` column with `@Transient`, I've created a proper Liquibase migration to add this column to all tables that need it.

## Files Created/Modified

### 1. New Liquibase Changelog ✅
**File:** `record-application/src/main/resources/db/changelog/scripts/00000000000010_add_conversion_reference.xml`

This changelog adds the `conversion_reference` column to 12 tables:

#### Tables in 'record' schema:
1. `record.code_lookup`
2. `record.entity_fee`
3. `record.entity_group`
4. `record.entity_note`
5. `record.record`
6. `record.setting`

#### Tables in default schema:
7. `document`
8. `association`
9. `record_type`
10. `association_type`
11. `document_type`
12. `module_definition`

**Column Definition:**
```sql
conversion_reference VARCHAR(255) NULL
```

### 2. Master Changelog Updated ✅
**File:** `record-application/src/main/resources/db/changelog/db.changelog-master.xml`

Added the new migration:
```xml
<!-- Add conversion_reference column to all auditable tables -->
<include file="scripts/00000000000010_add_conversion_reference.xml" relativeToChangelogFile="true"/>
```

### 3. BaseEntity.java Updated ✅
**File:** `record-application/src/main/java/com/scube/record/infrastructure/db/entity/BaseEntity.java`

**Removed:**
- `@Transient` annotation
- The local `conversionReference` field declaration

**Result:** The `conversionReference` field is now properly inherited from `AuditableBaseWithProperties` and will be mapped to the database column.

## How It Works

When the service starts, Liquibase will:

1. Check if changeset `00000000000010` has been executed
2. If not, it will execute all 12 changesets in order
3. Add the `conversion_reference` column to all 12 tables
4. Mark the changeset as executed in the `databasechangelog` table

**No manual SQL execution needed!** Liquibase handles it automatically on startup.

## Verification

After starting the service, you can verify the columns were added:

```sql
-- Check in record schema
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'record'
  AND table_name = 'record'
  AND column_name = 'conversion_reference';

-- Check in default schema
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'association'
  AND column_name = 'conversion_reference';
```

Expected result:
```
column_name          | data_type        | is_nullable
---------------------|------------------|-------------
conversion_reference | character varying| YES
```

## What This Fixes

**Before:**
```
ERROR: column r1_0.conversion_reference does not exist
```

**After:**
- ✅ Column exists in database
- ✅ Hibernate can map the field properly
- ✅ No database errors when querying entities
- ✅ Proper data modeling (not just ignoring the field)

## Migration Rollback

If needed, you can rollback this migration:

```bash
mvn liquibase:rollback -Dliquibase.rollbackCount=1
```

Or manually:
```sql
-- Tables in record schema
ALTER TABLE record.code_lookup DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE record.entity_fee DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE record.entity_group DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE record.entity_note DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE record.record DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE record.setting DROP COLUMN IF EXISTS conversion_reference;

-- Tables in default schema
ALTER TABLE document DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE association DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE record_type DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE association_type DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE document_type DROP COLUMN IF EXISTS conversion_reference;
ALTER TABLE module_definition DROP COLUMN IF EXISTS conversion_reference;
```

## Summary

✅ **Proper solution implemented**
- Added database columns (not just ignoring the field)
- Used Liquibase for versioned migrations
- Follows your existing migration pattern
- Automatic execution on service startup

✅ **All tables updated**
- 12 tables will get the new column
- No data loss
- Nullable column (safe for existing data)

✅ **Ready to deploy**
- JAR contains the migration
- Will run automatically on first startup
- Idempotent (safe to run multiple times)

---

**Just start the service and Liquibase will add the column automatically!**
