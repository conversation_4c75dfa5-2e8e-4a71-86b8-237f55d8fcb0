#!/bin/bash

# Record Service Startup Script
# This script starts the Record Service with the updated configuration that bypasses permission checks

cd "$(dirname "$0")"

# Set database credentials (update these if different)
export SPRING_DATASOURCE_USERNAME=postgres
export SPRING_DATASOURCE_PASSWORD=password
export DATABASE_HOST=localhost

# Set Keycloak credentials (if needed)
export KEYCLOAK_ADMIN_CLIENT_ID=admin-cli
export KEYCLOAK_ADMIN_CLIENT_SECRET=your-secret-here

# Set RabbitMQ host (if needed)
export SPRING_RABBITMQ_HOST=localhost

echo "Starting Record Service..."
echo "Database: ${DATABASE_HOST}:5432"
echo "Username: ${SPRING_DATASOURCE_USERNAME}"
echo ""

# Start the service
java -jar record-application/target/record-application-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=local \
  2>&1 | tee record-service.log

