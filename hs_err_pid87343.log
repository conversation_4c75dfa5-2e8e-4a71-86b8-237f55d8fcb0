#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGBUS (0xa) at pc=0x0000000100edb9f4, pid=87343, tid=4355
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.6+9-895.109-jcef (21.0.6+9) (build 21.0.6+9-b895.109)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.6+9-895.109-jcef (21.0.6+9-b895.109, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-aarch64)
# Problematic frame:
# C  [libzip.dylib+0x139f4]  newEntry+0x68
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://youtrack.jetbrains.com/issues/JBR
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:54983,suspend=y,server=n -javaagent:/Users/<USER>/Library/Caches/JetBrains/IdeaIC2025.1/captureAgent/debugger-agent.jar=file:///var/folders/wm/tpjk4z1172s7zkdzn96r4hym0000gn/T/capture2840168177584151527.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 /Users/<USER>/Documents/clerkxpress/Service_Document/document-application/target/document-application-0.0.1-SNAPSHOT.jar --spring.profiles.active=local

Host: "Mac16,12" arm64, 10 cores, 16G, Darwin 24.6.0, macOS 15.6.1 (24G90)
Time: Sat Sep  6 00:25:13 2025 IST elapsed time: 0.289545 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000000013000ce00):  JavaThread "main"             [_thread_in_native, id=4355, stack(0x000000016f204000,0x000000016f407000) (2060K)]

Stack: [0x000000016f204000,0x000000016f407000],  sp=0x000000016f405800,  free space=2054k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libzip.dylib+0x139f4]  newEntry+0x68
C  [libzip.dylib+0x13884]  ZIP_GetEntry2+0x138
C  [libzip.dylib+0x142d8]  ZIP_FindEntry+0x3c
V  [libjvm.dylib+0x25a6b8]  ClassPathZipEntry::open_entry(JavaThread*, char const*, int*, bool)+0x74
V  [libjvm.dylib+0x25a83c]  ClassPathZipEntry::open_stream(JavaThread*, char const*)+0x20
V  [libjvm.dylib+0x25d80c]  ClassLoader::load_class(Symbol*, bool, JavaThread*)+0x144
V  [libjvm.dylib+0x9dd1c0]  SystemDictionary::load_instance_class_impl(Symbol*, Handle, JavaThread*)+0x324
V  [libjvm.dylib+0x9db74c]  SystemDictionary::load_instance_class(Symbol*, Handle, JavaThread*)+0x20
V  [libjvm.dylib+0x9db000]  SystemDictionary::resolve_instance_class_or_null(Symbol*, Handle, Handle, JavaThread*)+0x3ec
V  [libjvm.dylib+0x5763bc]  JVM_FindClassFromBootLoader+0x138
C  [libjava.dylib+0x2758]  Java_java_lang_ClassLoader_findBootstrapClass+0xc0
j  java.lang.ClassLoader.findBootstrapClass(Ljava/lang/String;)Ljava/lang/Class;+0 java.base@21.0.6
j  java.lang.ClassLoader.findBootstrapClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+10 java.base@21.0.6
j  java.lang.System$2.findBootstrapClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+1 java.base@21.0.6
j  jdk.internal.loader.ClassLoaders$BootClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+4 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+86 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+86 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+3 java.base@21.0.6
j  jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+36 java.base@21.0.6
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.6
j  sun.instrument.InstrumentationImpl.loadClassAndStartAgent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V+8 java.instrument@21.0.6
j  sun.instrument.InstrumentationImpl.loadClassAndCallPremain(Ljava/lang/String;Ljava/lang/String;)V+6 java.instrument@21.0.6
v  ~StubRoutines::call_stub 0x0000000157534154
V  [libjvm.dylib+0x4b761c]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x3d8
V  [libjvm.dylib+0x524b28]  jni_invoke_nonstatic(JNIEnv_*, JavaValue*, _jobject*, JNICallType, _jmethodID*, JNI_ArgumentPusher*, JavaThread*)+0x3d0
V  [libjvm.dylib+0x529540]  jni_CallVoidMethod+0x11c
C  [libinstrument.dylib+0x3264]  invokeJavaAgentMainMethod+0x6c
C  [libinstrument.dylib+0x2b80]  processJavaStart+0x224
C  [libinstrument.dylib+0x22d8]  eventHandlerVMInit+0xe4
V  [libjvm.dylib+0x68b1cc]  JvmtiExport::post_vm_initialized()+0x2b8
V  [libjvm.dylib+0xa208ec]  Threads::create_vm(JavaVMInitArgs*, bool*)+0x6c8
V  [libjvm.dylib+0x541d74]  JNI_CreateJavaVM+0x74
C  [libjli.dylib+0xa230]  JavaMain+0x100
C  [libjli.dylib+0xd5d0]  ThreadJavaMain+0xc
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.ClassLoader.findBootstrapClass(Ljava/lang/String;)Ljava/lang/Class;+0 java.base@21.0.6
j  java.lang.ClassLoader.findBootstrapClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+10 java.base@21.0.6
j  java.lang.System$2.findBootstrapClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+1 java.base@21.0.6
j  jdk.internal.loader.ClassLoaders$BootClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+4 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+86 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+86 java.base@21.0.6
j  jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+3 java.base@21.0.6
j  jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+36 java.base@21.0.6
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.6
j  sun.instrument.InstrumentationImpl.loadClassAndStartAgent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V+8 java.instrument@21.0.6
j  sun.instrument.InstrumentationImpl.loadClassAndCallPremain(Ljava/lang/String;Ljava/lang/String;)V+6 java.instrument@21.0.6
v  ~StubRoutines::call_stub 0x0000000157534154

siginfo: si_signo: 10 (SIGBUS), si_code: 1 (BUS_ADRALN), si_addr: 0x0000000100d45ef0

Registers:
 x0=0x0000600003bf92c0  x1=0x000000000000008f  x2=0x00000000403b6e70  x3=0x0000000000000004
 x4=0x0000000000000000  x5=0x0000000097c47ffb  x6=0x0000600003bf92c0  x7=0x00000000000000f0
 x8=0x0000000100e55ed4  x9=0x0000000000115ed4 x10=0x0000000000110000 x11=0x00000000000012c0
x12=0x0000000000000050 x13=0x0000000000000001 x14=0x00000000ffffff70 x15=0x00000000000007fb
x16=0x0000000097c47ffb x17=0x000000000000003c x18=0x0000000000000000 x19=0x0000600003bf92c0
x20=0x0000000000000000 x21=0x0000600002df4000 x22=0x0000000100d45ed4 x23=0x0000000117905080
x24=0x000000004d6bd2f9 x25=0x000000000000002f x26=0x0000600003bf92e8 x27=0x00000000000000a2
x28=0x0000000130019c00  fp=0x000000016f405890  lr=0x0000000100edb9c0  sp=0x000000016f405800
pc=0x0000000100edb9f4 cpsr=0x0000000060000000

Register to memory mapping:

x0 =0x0000600003bf92c0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x1 =0x000000000000008f is an unknown value
x2 =0x00000000403b6e70 is an unknown value
x3 =0x0000000000000004 is an unknown value
x4 =0x0 is null
x5 =0x0000000097c47ffb is an unknown value
x6 =0x0000600003bf92c0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x7 =0x00000000000000f0 is an unknown value
x8 =0x0000000100e55ed4 is an unknown value
x9 =0x0000000000115ed4 is an unknown value
x10=0x0000000000110000 is an unknown value
x11=0x00000000000012c0 is an unknown value
x12=0x0000000000000050 is an unknown value
x13=0x0000000000000001 is an unknown value
x14=0x00000000ffffff70 is an unknown value
x15=0x00000000000007fb is an unknown value
x16=0x0000000097c47ffb is an unknown value
x17=0x000000000000003c is an unknown value
x18=0x0 is null
x19=0x0000600003bf92c0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x20=0x0 is null
x21=0x0000600002df4000 points into unknown readable memory: 0x0000600003cf85a0 | a0 85 cf 03 00 60 00 00
x22=0x0000000100d45ed4 points into unknown readable memory: 50 4b 01 02
x23=0x0000000117905080 points into unknown readable memory: 0x65746e692f6d6f63 | 63 6f 6d 2f 69 6e 74 65
x24=0x000000004d6bd2f9 is an unknown value
x25=0x000000000000002f is an unknown value
x26=0x0000600003bf92e8 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
x27=0x00000000000000a2 is an unknown value
x28=0x0000000130019c00 points into unknown readable memory: 0xffffffff5bbd78a2 | a2 78 bd 5b ff ff ff ff
 fp=0x000000016f405890 is pointing into the stack for thread: 0x000000013000ce00
 lr=0x0000000100edb9c0: newEntry+0x34 in /Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libzip.dylib at 0x0000000100ec8000
 sp=0x000000016f405800 is pointing into the stack for thread: 0x000000013000ce00

Top of Stack: (sp=0x000000016f405800)
0x000000016f405800:   0000000000000000 0000000000000000
0x000000016f405810:   0000000000000000 0000000000000000
0x000000016f405820:   000000016f405858 0000000000000000
0x000000016f405830:   0000000000000000 0000000000000000
0x000000016f405840:   0000000130019c00 00000000000000a2
0x000000016f405850:   0000000000000032 000000000000002f
0x000000016f405860:   000000004d6bd2f9 0000000117905080
0x000000016f405870:   0000000000000000 0000000000000000
0x000000016f405880:   0000000117905080 0000600002df4000
0x000000016f405890:   000000016f4058f0 0000000100edb884
0x000000016f4058a0:   0000000117905050 000000000000002c
0x000000016f4058b0:   000000013000d244 0000000117905080
0x000000016f4058c0:   00006000016c4210 0000000117905080
0x000000016f4058d0:   0000600002df4000 0000000117905080
0x000000016f4058e0:   000000016f405a1c 000000016f405934
0x000000016f4058f0:   000000016f405920 0000000100edc2d8
0x000000016f405900:   000000016f405a1c 00006000016c4210
0x000000016f405910:   0000000000000000 000000013000ce00
0x000000016f405920:   000000016f405a00 000000010222a6b8
0x000000016f405930:   0000000117014138 0000000000000100
0x000000016f405940:   000000016f405960 0000000102809354
0x000000016f405950:   0000000000000000 0000000000000100
0x000000016f405960:   000000016f405980 00000001025583a0
0x000000016f405970:   0000000000000000 0000000102b59c6d
0x000000016f405980:   000000016f4059d0 000000010222f0d0
0x000000016f405990:   000000016f4059b0 000000016f4059e0
0x000000016f4059a0:   0000000000000000 0000000000000000
0x000000016f4059b0:   000000013000ce00 7e00079b49ad0060
0x000000016f4059c0:   0000000000000001 0000000117905080
0x000000016f4059d0:   00006000016c4210 00006000033c4158
0x000000016f4059e0:   000000013000ce00 00000000000003d8
0x000000016f4059f0:   0000000117905040 00006000016c4210 

Instructions: (pc=0x0000000100edb9f4)
0x0000000100edb8f4:   63 05 00 94 e0 03 17 aa 61 05 00 94 d2 ff ff 17
0x0000000100edb904:   16 00 80 d2 75 01 00 34 56 01 00 b5 5f 07 00 f1
0x0000000100edb914:   6b f6 ff 54 48 07 00 51 88 4a 68 38 1f bd 00 71
0x0000000100edb924:   e1 f5 ff 54 16 00 80 d2 02 00 00 14 7f 4e 00 f9
0x0000000100edb934:   60 2a 40 f9 3a 05 00 94 e0 03 16 aa fd 7b 45 a9
0x0000000100edb944:   f4 4f 44 a9 f6 57 43 a9 f8 5f 42 a9 fa 67 41 a9
0x0000000100edb954:   fc 6f c6 a8 c0 03 5f d6 3f 00 03 6b e1 00 00 54
0x0000000100edb964:   21 04 00 71 eb 00 00 54 08 14 40 38 49 14 40 38
0x0000000100edb974:   1f 01 09 6b 60 ff ff 54 00 00 80 52 c0 03 5f d6
0x0000000100edb984:   20 00 80 52 c0 03 5f d6 ff 83 02 d1 fc 6f 04 a9
0x0000000100edb994:   fa 67 05 a9 f8 5f 06 a9 f6 57 07 a9 f4 4f 08 a9
0x0000000100edb9a4:   fd 7b 09 a9 fd 43 02 91 f4 03 02 aa f6 03 01 aa
0x0000000100edb9b4:   f5 03 00 aa 00 09 80 52 3d 05 00 94 f3 03 00 aa
0x0000000100edb9c4:   40 13 00 b4 7f 02 00 f9 fa 03 13 aa 5f 8f 02 f8
0x0000000100edb9d4:   7f 1a 00 f9 a8 c2 40 39 68 02 00 34 a8 0e 40 f9
0x0000000100edb9e4:   c9 06 40 f9 aa 16 40 f9 08 01 09 8b 16 01 0a cb
0x0000000100edb9f4:   d8 3a 40 79 d7 7a 40 39 db 7e 40 39 c8 42 40 79
0x0000000100edba04:   e8 1f 00 f9 c8 0e 40 b9 c9 1a 40 b9 68 a6 00 a9
0x0000000100edba14:   e9 17 00 f9 c8 16 40 79 88 04 00 34 c8 16 40 b9
0x0000000100edba24:   23 00 00 14 d7 06 40 f9 54 0d 00 34 a8 1e 40 f9
0x0000000100edba34:   88 02 00 b4 a9 22 40 f9 3f 01 17 eb 2c 02 00 54
0x0000000100edba44:   4a fa 83 52 2a 01 0a 8b 5f 01 17 eb ab 01 00 54
0x0000000100edba54:   2a 09 40 91 08 01 17 8b 16 01 09 cb c8 3a 40 79
0x0000000100edba64:   c9 3e 40 79 cb 42 40 79 e8 02 08 8b 08 01 09 8b
0x0000000100edba74:   08 01 0b 8b 08 b9 00 91 1f 01 0a eb ad fb ff 54
0x0000000100edba84:   e0 03 15 aa e1 03 17 aa 02 00 84 52 58 04 00 94
0x0000000100edba94:   f6 03 00 aa a0 0a 00 b4 a0 1e 40 f9 f8 04 00 94
0x0000000100edbaa4:   b6 de 03 a9 d3 ff ff 17 08 00 80 d2 fc 03 08 aa
0x0000000100edbab4:   68 0e 00 f9 c8 12 40 b9 68 22 00 b9 c9 a2 42 b8
0x0000000100edbac4:   a8 5e 40 f9 e8 0b 00 f9 e9 13 00 f9 08 01 09 8b
0x0000000100edbad4:   e8 03 08 cb 68 1e 00 f9 c8 12 40 79 68 42 00 b9
0x0000000100edbae4:   00 07 00 91 f2 04 00 94 f9 03 00 aa 60 02 00 f9 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x0 is null
stack at sp + 1 slots: 0x0 is null
stack at sp + 2 slots: 0x0 is null
stack at sp + 3 slots: 0x0 is null
stack at sp + 4 slots: 0x000000016f405858 is pointing into the stack for thread: 0x000000013000ce00
stack at sp + 5 slots: 0x0 is null
stack at sp + 6 slots: 0x0 is null
stack at sp + 7 slots: 0x0 is null


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00006000018c7d40, length=12, elements={
0x000000013000ce00, 0x0000000135009000, 0x000000013002f000, 0x0000000130031a00,
0x000000013901ca00, 0x0000000137861000, 0x0000000135809000, 0x000000011700a400,
0x000000013900be00, 0x0000000137861800, 0x000000013009ca00, 0x000000010783bc00
}
_to_delete_list=0x00006000018f6380, length=11, elements={
0x000000013000ce00, 0x0000000135009000, 0x000000013002f000, 0x0000000130031a00,
0x000000013901ca00, 0x0000000137861000, 0x0000000135809000, 0x000000011700a400,
0x000000013900be00, 0x0000000137861800, 0x000000013009ca00
}

Java Threads: ( => current thread )
=>0x000000013000ce00 JavaThread "main"                              [_thread_in_native, id=4355, stack(0x000000016f204000,0x000000016f407000) (2060K)]
  0x0000000135009000 JavaThread "Reference Handler"          daemon [_thread_blocked, id=29955, stack(0x000000017841c000,0x000000017861f000) (2060K)]
  0x000000013002f000 JavaThread "Finalizer"                  daemon [_thread_blocked, id=24835, stack(0x0000000178628000,0x000000017882b000) (2060K)]
  0x0000000130031a00 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=25091, stack(0x0000000178834000,0x0000000178a37000) (2060K)]
  0x000000013901ca00 JavaThread "Service Thread"             daemon [_thread_blocked, id=29187, stack(0x0000000178a40000,0x0000000178c43000) (2060K)]
  0x0000000137861000 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=25347, stack(0x0000000178c4c000,0x0000000178e4f000) (2060K)]
  0x0000000135809000 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=28419, stack(0x0000000178e58000,0x000000017905b000) (2060K)]
  0x000000011700a400 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=27907, stack(0x0000000179064000,0x0000000179267000) (2060K)]
  0x000000013900be00 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=25603, stack(0x0000000179270000,0x0000000179473000) (2060K)]
  0x0000000137861800 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=27395, stack(0x000000017947c000,0x000000017967f000) (2060K)]
  0x000000013009ca00 JavaThread "JDWP Event Helper Thread"   daemon [_thread_blocked, id=26371, stack(0x0000000179688000,0x000000017988b000) (2060K)]
  0x000000010783bc00 JavaThread "JDWP Command Reader"        daemon [_thread_in_native, id=26883, stack(0x0000000179894000,0x0000000179a97000) (2060K)]
Total: 12

Other Threads:
  0x0000000138106150 VMThread "VM Thread"                           [id=19459, stack(0x0000000178210000,0x0000000178413000) (2060K)]
  0x0000000136f06780 WatcherThread "VM Periodic Task Thread"        [id=20995, stack(0x0000000178004000,0x0000000178207000) (2060K)]
  0x0000000116f05e90 WorkerThread "GC Thread#0"                     [id=12547, stack(0x000000016f410000,0x000000016f613000) (2060K)]
  0x0000000117906010 ConcurrentGCThread "G1 Main Marker"            [id=14339, stack(0x000000016f61c000,0x000000016f81f000) (2060K)]
  0x0000000136e04c40 WorkerThread "G1 Conc#0"                       [id=13315, stack(0x000000016f828000,0x000000016fa2b000) (2060K)]
  0x000000013785d600 ConcurrentGCThread "G1 Refine#0"               [id=13571, stack(0x000000016fa34000,0x000000016fc37000) (2060K)]
  0x0000000117906980 ConcurrentGCThread "G1 Service"                [id=21507, stack(0x000000016fc40000,0x000000016fe43000) (2060K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000007000000000-0x0000007000d2c000-0x0000007000d2c000), size 13811712, SharedBaseAddress: 0x0000007000000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000007001000000-0x0000007041000000, reserved size: 1073741824
Narrow klass base: 0x0000007000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 10 total, 10 available
 Memory: 16384M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 9
 Concurrent Workers: 2
 Concurrent Refinement Workers: 9
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 264192K, used 3100K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 0 survivors (0K)
 Metaspace       used 252K, committed 448K, reserved 1114112K
  class space    used 9K, committed 128K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000700000000, 0x0000000700000000, 0x0000000700200000|  0%| F|  |TAMS 0x0000000700000000| PB 0x0000000700000000| Untracked 
|   1|0x0000000700200000, 0x0000000700200000, 0x0000000700400000|  0%| F|  |TAMS 0x0000000700200000| PB 0x0000000700200000| Untracked 
|   2|0x0000000700400000, 0x0000000700400000, 0x0000000700600000|  0%| F|  |TAMS 0x0000000700400000| PB 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700600000, 0x0000000700800000|  0%| F|  |TAMS 0x0000000700600000| PB 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700800000, 0x0000000700a00000|  0%| F|  |TAMS 0x0000000700800000| PB 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700a00000, 0x0000000700c00000|  0%| F|  |TAMS 0x0000000700a00000| PB 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700c00000, 0x0000000700e00000|  0%| F|  |TAMS 0x0000000700c00000| PB 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000700e00000, 0x0000000701000000|  0%| F|  |TAMS 0x0000000700e00000| PB 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x0000000701000000, 0x0000000701200000|  0%| F|  |TAMS 0x0000000701000000| PB 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x0000000701200000, 0x0000000701400000|  0%| F|  |TAMS 0x0000000701200000| PB 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701400000, 0x0000000701600000|  0%| F|  |TAMS 0x0000000701400000| PB 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x0000000701600000, 0x0000000701800000|  0%| F|  |TAMS 0x0000000701600000| PB 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x0000000701800000, 0x0000000701a00000|  0%| F|  |TAMS 0x0000000701800000| PB 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701a00000, 0x0000000701c00000|  0%| F|  |TAMS 0x0000000701a00000| PB 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701c00000, 0x0000000701e00000|  0%| F|  |TAMS 0x0000000701c00000| PB 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000701e00000, 0x0000000702000000|  0%| F|  |TAMS 0x0000000701e00000| PB 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702000000, 0x0000000702200000|  0%| F|  |TAMS 0x0000000702000000| PB 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702200000, 0x0000000702400000|  0%| F|  |TAMS 0x0000000702200000| PB 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702400000, 0x0000000702600000|  0%| F|  |TAMS 0x0000000702400000| PB 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702600000, 0x0000000702800000|  0%| F|  |TAMS 0x0000000702600000| PB 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|  25|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  49|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  50|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  55|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  56|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  57|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  58|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  59|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  60|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  61|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  62|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  64|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  67|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  68|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  71|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  72|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  75|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  76|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  77|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  83|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  84|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  86|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  87|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  92|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  93|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  94|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
| 104|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
| 105|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
| 106|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
| 107|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
| 115|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
| 116|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
| 117|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
| 118|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
| 119|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 120|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
| 121|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
| 122|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
| 123|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
| 124|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
| 125|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
| 126|0x000000070fc00000, 0x000000070fd51f50, 0x000000070fe00000| 66%| E|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Complete 
| 127|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| E|CS|TAMS 0x000000070fe00000| PB 0x000000070fe00000| Complete 
|2047|0x00000007ffe00000, 0x00000007fff07270, 0x0000000800000000| 51%| O|  |TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Untracked 

Card table byte_map: [0x0000000110000000,0x0000000110800000] _byte_map_base: 0x000000010c800000

Marking Bits: (CMBitMap*) 0x000000013001ac10
 Bits: [0x0000000110800000, 0x0000000114800000)

Polling page: 0x0000000100d18000

Metaspace:

Usage:
  Non-class:    242.22 KB used.
      Class:      9.84 KB used.
       Both:    252.05 KB used.

Virtual space:
  Non-class space:       64.00 MB reserved,     320.00 KB ( <1%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     128.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,     448.00 KB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.74 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 6.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 7.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 7.
num_chunk_merges: 0.
num_chunk_splits: 5.
num_chunks_enlarged: 2.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120032Kb used=46Kb max_used=46Kb free=119985Kb
 bounds [0x0000000157ac8000, 0x0000000157d38000, 0x000000015f000000]
CodeHeap 'profiled nmethods': size=120016Kb used=104Kb max_used=104Kb free=119911Kb
 bounds [0x0000000150000000, 0x0000000150270000, 0x0000000157534000]
CodeHeap 'non-nmethods': size=5712Kb used=1249Kb max_used=1261Kb free=4462Kb
 bounds [0x0000000157534000, 0x00000001577a4000, 0x0000000157ac8000]
 total_blobs=474 nmethods=116 adapters=267
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.113 Thread 0x000000011700a400 nmethod 80 0x0000000157ad2290 code [0x0000000157ad2400, 0x0000000157ad24a8]
Event: 0.113 Thread 0x000000011700a400   96       1       java.lang.module.ModuleDescriptor::provides (5 bytes)
Event: 0.113 Thread 0x000000011700a400 nmethod 96 0x0000000157ad2590 code [0x0000000157ad2700, 0x0000000157ad27a8]
Event: 0.113 Thread 0x000000011700a400  111       1       java.lang.module.ModuleDescriptor$Provides::service (5 bytes)
Event: 0.113 Thread 0x000000011700a400 nmethod 111 0x0000000157ad2890 code [0x0000000157ad2a00, 0x0000000157ad2aa8]
Event: 0.115 Thread 0x0000000135809000   95       4       java.util.ImmutableCollections$SetN$SetNIterator::next (90 bytes)
Event: 0.116 Thread 0x0000000135809000 nmethod 95 0x0000000157ad2b90 code [0x0000000157ad2d40, 0x0000000157ad2e90]
Event: 0.116 Thread 0x0000000135809000  103       4       java.util.concurrent.ConcurrentHashMap::spread (10 bytes)
Event: 0.116 Thread 0x0000000135809000 nmethod 103 0x0000000157ad2f90 code [0x0000000157ad3100, 0x0000000157ad3170]
Event: 0.116 Thread 0x0000000135809000   93       4       java.util.ImmutableCollections$SetN$SetNIterator::hasNext (13 bytes)
Event: 0.116 Thread 0x0000000135809000 nmethod 93 0x0000000157ad3290 code [0x0000000157ad3400, 0x0000000157ad3490]
Event: 0.116 Thread 0x0000000135809000   98       4       java.util.HashMap::hash (20 bytes)
Event: 0.117 Thread 0x0000000135809000 nmethod 98 0x0000000157ad3590 code [0x0000000157ad3740, 0x0000000157ad3850]
Event: 0.121 Thread 0x000000011700a400  112       3       jdk.internal.util.ArraysSupport::signedHashCode (37 bytes)
Event: 0.121 Thread 0x000000011700a400 nmethod 112 0x0000000150018d90 code [0x0000000150018f40, 0x00000001500190f8]
Event: 0.285 Thread 0x000000011700a400  116       3       java.lang.StringLatin1::indexOfChar (33 bytes)
Event: 0.285 Thread 0x000000011700a400 nmethod 116 0x0000000150019210 code [0x00000001500193c0, 0x00000001500195c0]
Event: 0.285 Thread 0x000000011700a400  113       3       java.lang.Class::getClassLoader (28 bytes)
Event: 0.285 Thread 0x000000011700a400 nmethod 113 0x0000000150019710 code [0x00000001500198c0, 0x0000000150019b38]
Event: 0.285 Thread 0x000000011700a400  114       3       java.lang.AbstractStringBuilder::isLatin1 (19 bytes)

GC Heap History (0 events):
No events

Dll operation events (3 events):
Event: 0.020 Loaded shared library /Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libjava.dylib
Event: 0.021 Loaded shared library /Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libzip.dylib
Event: 0.285 Loaded shared library /Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libinstrument.dylib

Deoptimization events (0 events):
No events

Classes loaded (13 events):
Event: 0.106 Loading class jdk/internal/module/ModuleReferenceImpl$CachedHash
Event: 0.106 Loading class jdk/internal/module/ModuleReferenceImpl$CachedHash done
Event: 0.113 Loading class jdk/internal/vm/VMSupport
Event: 0.113 Loading class jdk/internal/vm/VMSupport done
Event: 0.269 Loading class sun/instrument/InstrumentationImpl
Event: 0.269 Loading class java/lang/instrument/Instrumentation
Event: 0.270 Loading class java/lang/instrument/Instrumentation done
Event: 0.270 Loading class sun/instrument/InstrumentationImpl done
Event: 0.285 Loading class sun/instrument/TransformerManager
Event: 0.285 Loading class sun/instrument/TransformerManager done
Event: 0.285 Loading class sun/instrument/TransformerManager$TransformerInfo
Event: 0.285 Loading class sun/instrument/TransformerManager$TransformerInfo done
Event: 0.285 Loading class com/intellij/rt/debugger/agent/DebuggerAgent

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (4 events):
Event: 0.274 Executing VM operation: ChangeBreakpoints
Event: 0.274 Executing VM operation: ChangeBreakpoints done
Event: 0.278 Executing VM operation: ChangeBreakpoints
Event: 0.278 Executing VM operation: ChangeBreakpoints done

Memory protections (12 events):
Event: 0.020 Protecting memory [0x000000016f204000,0x000000016f210000] with protection modes 0
Event: 0.084 Protecting memory [0x000000017841c000,0x0000000178428000] with protection modes 0
Event: 0.084 Protecting memory [0x0000000178628000,0x0000000178634000] with protection modes 0
Event: 0.085 Protecting memory [0x0000000178834000,0x0000000178840000] with protection modes 0
Event: 0.085 Protecting memory [0x0000000178a40000,0x0000000178a4c000] with protection modes 0
Event: 0.085 Protecting memory [0x0000000178c4c000,0x0000000178c58000] with protection modes 0
Event: 0.086 Protecting memory [0x0000000178e58000,0x0000000178e64000] with protection modes 0
Event: 0.088 Protecting memory [0x0000000179064000,0x0000000179070000] with protection modes 0
Event: 0.100 Protecting memory [0x0000000179270000,0x000000017927c000] with protection modes 0
Event: 0.124 Protecting memory [0x000000017947c000,0x0000000179488000] with protection modes 0
Event: 0.125 Protecting memory [0x0000000179688000,0x0000000179694000] with protection modes 0
Event: 0.128 Protecting memory [0x0000000179894000,0x00000001798a0000] with protection modes 0

Nmethod flushes (0 events):
No events

Events (12 events):
Event: 0.060 Thread 0x000000013000ce00 Thread added: 0x000000013000ce00
Event: 0.084 Thread 0x000000013000ce00 Thread added: 0x0000000135009000
Event: 0.084 Thread 0x000000013000ce00 Thread added: 0x000000013002f000
Event: 0.085 Thread 0x000000013000ce00 Thread added: 0x0000000130031a00
Event: 0.085 Thread 0x000000013000ce00 Thread added: 0x000000013901ca00
Event: 0.085 Thread 0x000000013000ce00 Thread added: 0x0000000137861000
Event: 0.085 Thread 0x000000013000ce00 Thread added: 0x0000000135809000
Event: 0.086 Thread 0x000000013000ce00 Thread added: 0x000000011700a400
Event: 0.098 Thread 0x000000013000ce00 Thread added: 0x000000013900be00
Event: 0.123 Thread 0x000000013000ce00 Thread added: 0x0000000137861800
Event: 0.125 Thread 0x000000013000ce00 Thread added: 0x000000013009ca00
Event: 0.127 Thread 0x0000000137861800 Thread added: 0x000000010783bc00


Dynamic libraries:
0x0000000100cc4000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libjli.dylib
0x00000001b9cc7000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x00000001a1477000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x00000001a4767000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000019eb03000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00000001ab6ff000 	/usr/lib/libSystem.B.dylib
0x00000001a2908000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000024b596000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00000001a9288000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00000001adecb000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x00000001ae222000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x0000000279f20000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x0000000203fb9000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000027efa6000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000027dfea000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000019e766000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00000001ad32e000 	/usr/lib/libspindump.dylib
0x00000001a2aba000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x00000001aaaea000 	/usr/lib/libbsm.0.dylib
0x00000001a6d0a000 	/usr/lib/libapp_launch_measurement.dylib
0x00000001a60b3000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00000001a6d0e000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x00000001a88a2000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00000001a9ac6000 	/usr/lib/liblangid.dylib
0x00000001a928e000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00000001a34df000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x00000001a3a06000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00000001b33d8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00000001ad171000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x00000001a887f000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x00000001a60e4000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x00000001ab649000 	/usr/lib/libz.1.dylib
0x00000001b72ed000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x00000001a9273000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00000001a0cdc000 	/usr/lib/libicucore.A.dylib
0x00000001af2ab000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00000001ae1d3000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001c9d74000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x00000001a342a000 	/usr/lib/libMobileGestalt.dylib
0x00000001a8f6c000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x00000001a65eb000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00000001a08d2000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00000001b2d35000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x00000001a6a11000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x00000001a01a0000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x00000001a61d2000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00000001ad797000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00000001a3428000 	/usr/lib/libenergytrace.dylib
0x00000001be318000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x00000001a1327000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00000001b312c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x00000001a6c9b000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001fd97a000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00000001a6d58000 	/usr/lib/libxml2.2.dylib
0x00000001aa9ce000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000019d0a8000 	/usr/lib/libobjc.A.dylib
0x000000019d3b6000 	/usr/lib/libc++.1.dylib
0x00000001b30ad000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x00000001a4137000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000019d512000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00000001a9648000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000019ff81000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001feec6000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001ff44b000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001ff44e000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x00000001a92c9000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x0000000204b7c000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001f1128000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x00000001ab704000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001dc9e5000 	/usr/lib/swift/libswiftAccelerate.dylib
0x00000001aec40000 	/usr/lib/swift/libswiftCore.dylib
0x00000001c6417000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001c6471000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001c3780000 	/usr/lib/swift/libswiftDarwin.dylib
0x0000000285bc3000 	/usr/lib/swift/libswiftDataDetection.dylib
0x00000001b4c25000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001c6472000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001d2fdc000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001e1f7d000 	/usr/lib/swift/libswiftOSLog.dylib
0x00000001b78a7000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x0000000285bf0000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001d837b000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001dc9d6000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001c6429000 	/usr/lib/swift/libswiftXPC.dylib
0x0000000285cd6000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x0000000285cd9000 	/usr/lib/swift/libswift_Concurrency.dylib
0x0000000285e38000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x0000000285ecb000 	/usr/lib/swift/libswift_errno.dylib
0x0000000285ecd000 	/usr/lib/swift/libswift_math.dylib
0x0000000285ed0000 	/usr/lib/swift/libswift_signal.dylib
0x0000000285ed1000 	/usr/lib/swift/libswift_stdio.dylib
0x0000000285ed2000 	/usr/lib/swift/libswift_time.dylib
0x00000001b78ab000 	/usr/lib/swift/libswiftos.dylib
0x00000001c9ccb000 	/usr/lib/swift/libswiftsimd.dylib
0x0000000285ed3000 	/usr/lib/swift/libswiftsys_time.dylib
0x0000000285ed4000 	/usr/lib/swift/libswiftunistd.dylib
0x00000001ab92d000 	/usr/lib/libcompression.dylib
0x00000001ade2b000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x00000001ace10000 	/usr/lib/libate.dylib
0x00000001ab6f9000 	/usr/lib/system/libcache.dylib
0x00000001ab6b4000 	/usr/lib/system/libcommonCrypto.dylib
0x00000001ab6df000 	/usr/lib/system/libcompiler_rt.dylib
0x00000001ab6d4000 	/usr/lib/system/libcopyfile.dylib
0x000000019d203000 	/usr/lib/system/libcorecrypto.dylib
0x000000019d2ea000 	/usr/lib/system/libdispatch.dylib
0x000000019d4aa000 	/usr/lib/system/libdyld.dylib
0x00000001ab6ef000 	/usr/lib/system/libkeymgr.dylib
0x00000001ab697000 	/usr/lib/system/libmacho.dylib
0x00000001aaac3000 	/usr/lib/system/libquarantine.dylib
0x00000001ab6ec000 	/usr/lib/system/libremovefile.dylib
0x00000001a34a4000 	/usr/lib/system/libsystem_asl.dylib
0x000000019d198000 	/usr/lib/system/libsystem_blocks.dylib
0x000000019d334000 	/usr/lib/system/libsystem_c.dylib
0x00000001ab6e3000 	/usr/lib/system/libsystem_collections.dylib
0x00000001a9ab3000 	/usr/lib/system/libsystem_configuration.dylib
0x00000001a884e000 	/usr/lib/system/libsystem_containermanager.dylib
0x00000001ab1d9000 	/usr/lib/system/libsystem_coreservices.dylib
0x00000001a0fa8000 	/usr/lib/system/libsystem_darwin.dylib
0x000000028600b000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x00000001ab6f0000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000028600f000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000019d331000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000019d4e2000 	/usr/lib/system/libsystem_info.dylib
0x00000001ab658000 	/usr/lib/system/libsystem_m.dylib
0x000000019d2a2000 	/usr/lib/system/libsystem_malloc.dylib
0x00000001a340d000 	/usr/lib/system/libsystem_networkextension.dylib
0x00000001a140a000 	/usr/lib/system/libsystem_notify.dylib
0x00000001a9ab8000 	/usr/lib/system/libsystem_sandbox.dylib
0x0000000286017000 	/usr/lib/system/libsystem_sanitizers.dylib
0x00000001ab6e8000 	/usr/lib/system/libsystem_secinit.dylib
0x000000019d461000 	/usr/lib/system/libsystem_kernel.dylib
0x000000019d4da000 	/usr/lib/system/libsystem_platform.dylib
0x000000019d49d000 	/usr/lib/system/libsystem_pthread.dylib
0x00000001a5001000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000019d1e7000 	/usr/lib/system/libsystem_trace.dylib
0x00000001ab6c2000 	/usr/lib/system/libunwind.dylib
0x000000019d19c000 	/usr/lib/system/libxpc.dylib
0x000000019d443000 	/usr/lib/libc++abi.dylib
0x0000000284be0000 	/usr/lib/libRosetta.dylib
0x00000001a12a6000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x00000001ab6cc000 	/usr/lib/liboah.dylib
0x00000001ab701000 	/usr/lib/libfakelink.dylib
0x00000001b6da0000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001c32a9000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00000001a3043000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x00000001a6cd2000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x00000001a0fb3000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00000001a6147000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00000001ab1e1000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00000001ab84e000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x00000001a4f7c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000019da51000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00000001acc65000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00000001a6ce0000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x00000001ab8e4000 	/usr/lib/libapple_nghttp2.dylib
0x00000001a4bdd000 	/usr/lib/libsqlite3.dylib
0x00000001af924000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00000001a4f13000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00000001a500a000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00000001ab69b000 	/usr/lib/system/libkxld.dylib
0x0000000247341000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000284a38000 	/usr/lib/libCoreEntitlements.dylib
0x000000026240c000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x00000001a4bc2000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00000001ab1c0000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00000001aaad2000 	/usr/lib/libcoretls.dylib
0x00000001accdb000 	/usr/lib/libcoretls_cfhelpers.dylib
0x00000001ab927000 	/usr/lib/libpam.2.dylib
0x00000001acd4f000 	/usr/lib/libxar.1.dylib
0x00000001ab756000 	/usr/lib/libarchive.2.dylib
0x00000001b1027000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000024b5ba000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000026b46c000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000026c143000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x0000000285c67000 	/usr/lib/swift/libswiftSystem.dylib
0x00000001a9ac1000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001c6302000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00000001b40e6000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x0000000203e7e000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x00000001a6ee3000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x00000001a2f71000 	/usr/lib/libboringssl.dylib
0x00000001a4ff0000 	/usr/lib/libdns_services.dylib
0x00000001c54b1000 	/usr/lib/libquic.dylib
0x00000001aebcf000 	/usr/lib/libusrtcp.dylib
0x00000001e9df2000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x0000000285bc4000 	/usr/lib/swift/libswiftDistributed.dylib
0x0000000285c5d000 	/usr/lib/swift/libswiftSynchronization.dylib
0x00000001a3042000 	/usr/lib/libnetwork.dylib
0x00000001d6fb2000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x00000001ab8bf000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00000001b53bb000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001e5abb000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00000001b5397000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x0000000284903000 	/usr/lib/libAppleArchive.dylib
0x00000001ab1cc000 	/usr/lib/libbz2.1.0.dylib
0x00000001accbc000 	/usr/lib/liblzma.5.dylib
0x00000001aa7be000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001c638b000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00000001aceaa000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000019e55d000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000025a74c000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00000001b4ebd000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x00000001aa9fa000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x00000001aab5c000 	/usr/lib/libgermantok.dylib
0x00000001a9be5000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00000001a4e2e000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00000001b4f8a000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x00000001aa9eb000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00000001b6bda000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x00000001a8c99000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00000001a34bc000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00000001ba5d2000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00000001ad795000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000019faa8000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x00000001a8b3d000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x00000001a8898000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00000001a6e42000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x00000001ab925000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000026a7aa000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x00000001ad7dd000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00000001a9abf000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00000001accdd000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00000001acd5e000 	/usr/lib/libutil.dylib
0x00000002756cf000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x00000001a61db000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00000001b3107000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00000001acd96000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000019dec9000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00000001af4fb000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x00000001a42ed000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00000001addb4000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00000001af8ee000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00000001af8e5000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00000001af4cd000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00000001aa78b000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x0000000251811000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00000001ad2e3000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00000001a69bf000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x0000000284fc5000 	/usr/lib/libhvf.dylib
0x00000002666b0000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x0000000285c0d000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x0000000285d95000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00000001ad65e000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x00000001ad0ff000 	/usr/lib/libexpat.1.dylib
0x00000001adc8a000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00000001adcb5000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x00000001add9f000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00000001ad6a4000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00000001add46000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00000001add3d000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x00000002564e0000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00000002518dc000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001fd96c000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x0000000252451000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x0000000251812000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00000001b89ea000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x0000000269917000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00000001ad2d7000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001fd9ca000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001fd98e000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001fdb56000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001fd997000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001fd98b000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001fd974000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00000001a9a03000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x00000001ab12b000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00000001aab74000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00000001aafab000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x00000001aadc0000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00000001aafdd000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x000000020126c000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x000000020124e000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000019dd44000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001cb074000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001d8770000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001c63f2000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x00000001add71000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00000001af879000 	/usr/lib/libcups.2.dylib
0x00000001af913000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00000001af579000 	/usr/lib/libresolv.9.dylib
0x00000001ab73b000 	/usr/lib/libiconv.2.dylib
0x00000001ad335000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00000001b77fe000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x00000001ad11a000 	/usr/lib/libheimdal-asn1.dylib
0x00000001a6ca5000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x00000001af976000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00000001a6cb3000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00000001ab696000 	/usr/lib/libcharset.1.dylib
0x00000001fdef2000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001bfcde000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000025e084000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x00000001af4b4000 	/usr/lib/libAudioStatistics.dylib
0x00000001a8b15000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000019fbc9000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00000001b9af4000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x00000001af456000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00000001b0d8f000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00000001ad204000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x0000000278193000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000024c077000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001dd0a8000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001c63f6000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x00000001ad324000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x00000001af8f7000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001c55aa000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x00000001af798000 	/usr/lib/libSMC.dylib
0x00000001adc54000 	/usr/lib/libAudioToolboxUtility.dylib
0x00000001af905000 	/usr/lib/libperfcheck.dylib
0x0000000247ebf000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001e9bb9000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001c63bd000 	/usr/lib/libmis.dylib
0x00000001af264000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x00000001adda5000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001ff59b000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00000001b54c8000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x00000001acffe000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00000001b3fef000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00000001b77ff000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00000001aa84f000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000024f02a000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00000001b3428000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001cea14000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x0000000284881000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001bfd9a000 	/usr/lib/libAccessibility.dylib
0x00000001aa699000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x00000001aba03000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00000001aab5f000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x00000001ab8fe000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x00000001ab9fe000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00000001a9bec000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000019e66a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000025f7fb000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x00000001add38000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x00000001add18000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00000001add40000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001e9fc0000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001befb8000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000027b9d5000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x00000001ad0b6000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00000001b5037000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x00000001afae9000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001b41d7000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00000001b4122000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x00000001af8d8000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x00000001a6ea3000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00000001ad124000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x00000001acea1000 	/usr/lib/libIOReport.dylib
0x0000000247acf000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x0000000284c43000 	/usr/lib/libTLE.dylib
0x00000001fa9c8000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x00000001aaafc000 	/usr/lib/libmecab.dylib
0x000000019e7fa000 	/usr/lib/libCRFSuite.dylib
0x00000001a9ac8000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x00000001ab8b6000 	/usr/lib/libThaiTokenizer.dylib
0x00000001aaac6000 	/usr/lib/libCheckFix.dylib
0x00000001a60e6000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000025b030000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x00000001a12e6000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001d887a000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00000001acd62000 	/usr/lib/libxslt.1.dylib
0x00000001aaa89000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000001b6f5e000 	/usr/lib/libcurl.4.dylib
0x0000000284e77000 	/usr/lib/libcrypto.46.dylib
0x00000002859a1000 	/usr/lib/libssl.48.dylib
0x00000001b6c36000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00000001b6c72000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x00000001af596000 	/usr/lib/libsasl2.2.dylib
0x00000001c377f000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00000001b3a57000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001fa452000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001e626c000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000026c23a000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00000001b2d22000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x0000000101fd0000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/server/libjvm.dylib
0x0000000100d2c000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libjimage.dylib
0x0000000100d88000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libjdwp.dylib
0x0000000100dd0000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libjava.dylib
0x0000000100d58000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libinstrument.dylib
0x0000000100ec8000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libzip.dylib
0x0000000284873000 	/usr/lib/i18n/libiconv_std.dylib
0x0000000284869000 	/usr/lib/i18n/libUTF8.dylib
0x0000000284878000 	/usr/lib/i18n/libmapper_none.dylib
0x0000000100eb4000 	/Applications/IntelliJ IDEA CE.app/Contents/jbr/Contents/Home/lib/libdt_socket.dylib


VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:54983,suspend=y,server=n -javaagent:/Users/<USER>/Library/Caches/JetBrains/IdeaIC2025.1/captureAgent/debugger-agent.jar=file:///var/folders/wm/tpjk4z1172s7zkdzn96r4hym0000gn/T/capture2840168177584151527.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 
java_command: /Users/<USER>/Documents/clerkxpress/Service_Document/document-application/target/document-application-0.0.1-SNAPSHOT.jar --spring.profiles.active=local
java_class_path (initial): /Users/<USER>/Documents/clerkxpress/Service_Document/document-application/target/document-application-0.0.1-SNAPSHOT.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 9                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {ergonomic}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839564                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909338                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909338                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/usr/local/share/dotnet:~/.dotnet/tools
SHELL=/bin/zsh
LC_CTYPE=en_US.UTF-8
TMPDIR=/var/folders/wm/tpjk4z1172s7zkdzn96r4hym0000gn/T/

Active Locale:
LC_ALL=C/en_US.UTF-8/C/C/C/C
LC_COLLATE=C
LC_CTYPE=en_US.UTF-8
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled

JNI global refs:
JNI global refs: 29, weak refs: 694

JNI global refs memory usage: 7219, weak refs: 7217

Process memory usage:
Resident Set Size: 43376K (0% of 16777216K total physical memory with 66288K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 252K

---------------  S Y S T E M  ---------------

OS:
uname: Darwin 24.6.0 Darwin Kernel Version 24.6.0: Mon Jul 14 11:30:40 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T8132 arm64
OS uptime: 4 days 13:34 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 10240/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 1.91 2.16 2.61

CPU: total 10 (initial active 10) 0x61:0x0:0x6f5129ac:0, fp, asimd, aes, pmull, sha1, sha256, crc32, lse, sha3, sha512
machdep.cpu.brand_string:Apple M4
hw.cachelinesize:128
hw.l1icachesize:131072
hw.l1dcachesize:65536
hw.l2cachesize:4194304

Memory: 16k page, physical 16777216k(66288k free), swap 10485760k(1246208k free)

vm_info: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109) for bsd-aarch64 JRE (21.0.6+9-b895.109), built on 2025-03-26 by "builduser" with clang Apple LLVM 14.0.3 (clang-1403.0.22.14.1)

END.
