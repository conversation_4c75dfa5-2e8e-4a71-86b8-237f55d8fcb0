{"id": "5f32666e-18cc-43df-845e-7d887dc7cdde", "realm": "clerkXpress", "displayName": "Clerk <PERSON><PERSON>", "displayNameHtml": "", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 2592000, "accessTokenLifespanForImplicitFlow": 2592000, "ssoSessionIdleTimeout": 2592000, "ssoSessionMaxLifespan": 2592000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "none", "registrationAllowed": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": false, "loginWithEmailAllowed": false, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "53772edf-547b-4f4b-a046-f5c1af7c8dc3", "name": "notification-service-notification-delete", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "768bac66-e6b2-4ed9-93e8-cdccb698102a", "name": "license-service-code-lookup-create", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b1f9d06f-3923-4d03-b733-b6f78b8343d6", "name": "document-service--delete", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d967e7ee-47af-41a2-a76c-6266c0908339", "name": "license-service-me-profile-remove-from-rejected-field-list", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b9f924ed-ebef-4136-b115-867146e53801", "name": "license-service-entity-remove-association", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "dfa4f3e7-adcf-45bf-bade-7363110cd973", "name": "payment-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4ee4165c-289b-4971-a5e8-9cb4108ececf", "name": "license-service-json-storage-get-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "02bd0c6d-2a23-4804-92a0-8a70e5939b62", "name": "scheduling-library-scheduler-reschedule-task", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2d30ea7b-e6f9-4f7e-9292-c562ff8bbb68", "name": "report-service-test-get-templates", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5599a113-d2a7-4f01-a940-dd744a976699", "name": "config-service-tenant-logged-in-user-leave-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4cc139b8-8800-4df4-832f-0f02db20cb6f", "name": "calculation-service-me-cart-clear-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bdacc8a2-da4b-4ece-b45e-1e61b43040d4", "name": "coordinator-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8ca6a979-4045-485b-bd37-140cdba04a48", "name": "image-processing-service-me-textract-analyze-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "aa4880f7-8551-4501-8d4e-c91fbfab44a9", "name": "coordinator-service-cart-infer-payee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bcf96ee7-e5b8-482a-9c3f-f85aa534d0ca", "name": "report-service-report-get-report", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6f1db41a-2d67-451f-a553-49e3d650131b", "name": "license-service-me-merge-requests-create-merge-request", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f2e4c21c-118a-4a65-9fbf-6fefe5372489", "name": "license-service-me-license-calculate-license-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f9b316e9-ff85-40c2-9610-0166c4889df8", "name": "auth-service-me-user-delete-user", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "798e3255-ce02-4499-a3d3-4edabc8b3d18", "name": "config-service-app-property-get-property", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "08f34863-2570-48c4-b74f-3328cb396479", "name": "document-service--handle-file-update", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a4a82118-d972-43f0-92f8-3505a8fdbc8d", "name": "license-service-document-get-document-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a3b39178-b0cf-4473-9a08-9566716fa33f", "name": "license-service-multi-form-builder-create-multi-form-builder", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "479e9669-c914-4974-960b-e04c65b3e895", "name": "ai-service-MapText-parse-ocr-text", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0927b57d-09b5-4f4b-8128-b1a28e339004", "name": "ai-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4ef870bb-3f04-40c1-8ea3-7e487696eee8", "name": "auth-service-admin-roles", "composite": true, "composites": {"realm": ["auth-service-realm-add-role-to-realm", "rabbit-library-task-fanout-create-task", "rabbit-library-task-fanout-get-task", "auth-service-public-realm-get-all-realm-names", "auth-service-realm-create-realm", "rabbit-library-task-fanout-concurrent-create-task", "auth-service-user-get-user", "auth-service-user-delete-user", "auth-service-permissions-seed-roles-by-realm", "auth-service-realm-get-realm-representation", "auth-service-permissions-seed-roles-to-all-realms", "rabbit-library-task-direct-concurrent-create-task", "auth-service-user-get-all-users", "auth-service-realm-create-composite-role", "auth-service-public-realm-get-all-realms", "rabbit-library-task-direct-create-task", "rabbit-library-task-direct-get-task", "auth-service-public-realm-get-realm", "auth-service-public-realm-get-realm-name"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "57b3bee1-4587-4740-acf8-ef97a9cfdb24", "name": "notification-service-notification-create", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a570196d-db70-4e09-b243-97b9a65bb308", "name": "license-service-event-add-individual-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a183892a-fa03-4359-be88-d79c0cd8ccdc", "name": "coordinator-service-me-payment-get-receipts", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "96ed2405-8e02-4255-8aa7-39187b7b0013", "name": "calculation-service-me-roles", "composite": true, "composites": {"realm": ["calculation-service-me-fees-get-all-fees", "calculation-service-me-cart-get-active-cart", "calculation-service-me-order-roll-back-to-cart", "calculation-service-me-cart-remove-item", "calculation-service-me-order-list-orders", "calculation-service-me-cart-create-cart", "calculation-service-me-cart-remove-cart-item-by", "calculation-service-me-cart-get-cart-invoice", "calculation-service-me-order-create-order-from-cart", "calculation-service-me-fees-get-fees-by-keys", "calculation-service-me-cart-clear-cart", "calculation-service-me-cart-add-item", "calculation-service-me-order-get-order", "calculation-service-me-cart-list-cart", "calculation-service-me-order-cancel-order"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "37c56d82-879b-4e54-8bb1-877ac632b7c8", "name": "license-service-admin-roles", "composite": true, "composites": {"realm": ["license-service-public-dog-report-sighting", "license-service-merge-requests-reject-merge-request", "license-service-entity-fee-add-file", "license-service-entity-add-association", "license-service-license-create-license-by-form-builder", "license-service-license-get-outstanding-approvals", "license-service-license-mark-license-as-approved", "license-service-config-license-type-get-all-license-types", "license-service-tenant-delete-tenant", "license-service-entity-update-entity", "license-service-event-type-create-event-type", "license-service-participant-add-fee", "license-service-license-is-vaccination-expired", "license-service-merge-requests-get-merge-requests", "license-service-config-license-type-delete-license-type", "license-service-license-get-license-actions", "license-service-reminders-run-expiration-reminders", "license-service-license-query", "license-service-event-add-license-event", "scheduling-library-scheduler-refresh-scheduler", "license-service-permissions-seed-roles-to-all-realms", "license-service-app-properties-get-property-by-name", "license-service-json-storage-delete-json-storage", "license-service-document-file-upload", "license-service-search-search-parcel", "license-service-app-properties-get-property-by-uuid", "rabbit-library-task-direct-get-task", "license-service-event-type-get-event-type", "license-service-code-lookup-create-or-lookup", "license-service-entity-fee-get-fees", "license-service-tenant-update-tenant", "scheduling-library-scheduler-get-all", "rabbit-library-task-fanout-get-task", "license-service-profile-remove-from-rejected-field-list", "license-service-license-re-process-fee", "license-service-event-type-get-event-types-by-profile-type", "license-service-entity-fee-get-fee", "license-service-profile-get-address-entity-and-all-associations", "license-service-public-code-lookup-get-by-code", "license-service-license-create-pending-license", "license-service-profile-get-rejected-field-list", "license-service-entity-fee-delete-fee", "license-service-license-create-license", "license-service-license-check-not-more-than-three-licenses", "license-service-license-get-house-hold-count-by-license", "license-service-license-remove-participant", "license-service-participant-get-fee", "license-service-participant-mark-offline-resident", "license-service-profile-get-license-entity-and-all-associations", "license-service-participant-get-fees", "license-service-merge-by-code-merge-by-code", "license-service-license-mark-license-as-pending-approval", "license-service-config-license-status-delete-license-status-by-id", "license-service-json-storage-get-json-storage", "license-service-code-lookup-create", "license-service-permissions-seed-roles-by-realm", "license-service-config-license-status-create-license-status", "license-service-document-get-documents", "license-service-profile-get-document-entity-and-all-associations", "license-service-license-save-license-draft", "license-service-search-search-individual", "license-service-entity-remove-association", "rabbit-library-task-fanout-create-task", "license-service-merge-requests-create-merge-request", "license-service-participant-mark-approved", "license-service-event-type-get-event-type-for-drop-down", "license-service-license-get-license-fees", "license-service-report-get-dog-license-renewal-notice", "rabbit-library-task-fanout-concurrent-create-task", "license-service-multi-form-builder-delete-multi-form-builder", "scheduling-library-scheduler-reschedule-task", "license-service-merge-requests-approve-merge-request-by-entity-id", "license-service-qr-codes-generate-batch", "license-service-license-get-license-form", "license-service-participant-delete-fee", "license-service-json-storage-create-update-json-storage", "rabbit-library-task-direct-concurrent-create-task", "license-service-profile-clear-rejected-field-list", "license-service-license-create-final-license", "license-service-qr-codes-generate-with-text", "license-service-tenant-get-all-tenants", "license-service-event-add-dog-event", "license-service-json-storage-update-json-storage", "license-service-entity-fee-update-fee", "license-service-config-license-type-create-license-type", "license-service-app-properties-get-cache", "license-service-participant-delete-participant", "license-service-search-search-organization", "license-service-document-get-document-types", "license-service-profile-get-dog-entity-and-all-associations", "license-service-public-profile-get-dog", "license-service-merge-requests-get-merge-request", "license-service-merge-requests-get-merge-requests-by-request-user", "license-service-json-storage-get-json-storage-by-id", "license-service-report-get-dog-license-online-announcement-letter", "license-service-license-calculate-license-fees", "license-service-code-lookup-get-by-entity-type-and-entity-id", "license-service-document-get-document-history", "license-service-code-lookup-batch-tag-create", "license-service-event-add-individual-event", "license-service-license-delete-license-draft", "license-service-participant-update-participant", "license-service-multi-form-builder-update-multi-form-builder", "license-service-document-save-document-type", "scheduling-library-scheduler-stop-scheduler", "license-service-event-add-address-event", "license-service-merge-by-code-exists-by-registration-code", "license-service-participant-patch-participant-contact", "license-service-multi-form-builder-get-all-multi-form-builder", "license-service-license-mark-license-as-denied", "license-service-license-add-dog-to-license", "license-service-app-properties-delete-property-by-uuid", "license-service-document-delete", "license-service-qr-codes-generate-by-code", "license-service-profile-get-business-entity-and-all-associations", "license-service-entity-create-entity", "rabbit-library-task-direct-create-task", "license-service-app-properties-update-property", "license-service-tenant-create-tenant", "license-service-merge-requests-approve-merge-request", "license-service-participant-create-resident", "license-service-multi-form-builder-create-multi-form-builder", "license-service-search-search-dog", "scheduling-library-scheduler-start-scheduler", "license-service-config-license-type-get-license-type-by-id", "license-service-report-get-adhoc-report", "license-service-config-license-status-get-license-status-by-id", "license-service-document-get-document", "license-service-qr-codes-generate-qr-code-no-upload", "license-service-participant-mark-online-resident", "license-service-participant-create-online-resident", "license-service-qr-codes-generate-and-upload", "license-service-search-search-email-exists", "license-service-reminders-run-delinquent-reminders", "license-service-license-change-license-status", "license-service-document-get-document-type", "license-service-participant-mark-unapproved", "license-service-app-properties-get-all-properties", "license-service-entity-delete-entity", "license-service-license-renew-license", "license-service-license-update-license-fee", "license-service-event-type-get-event-types", "license-service-multi-form-builder-get-multi-form-builder-by-name", "license-service-profile-get-individual-entity-and-all-associations", "license-service-entity-fee-add-fee", "license-service-multi-form-builder-get-multi-form-builder-by-entity-id", "license-service-profile-add-to-rejected-field-list", "license-service-license-add-license-fee", "license-service-tenant-get-tenant-by-uuid", "license-service-event-add-document-event", "license-service-search-search-global", "license-service-license-get-house-hold-count-by-participant", "license-service-profile-get-contact", "license-service-reminders-run-expire-licenses", "license-service-config-license-status-get-all-license-status", "license-service-config-license-status-update-license-status", "license-service-json-storage-create-json-storage", "license-service-config-license-type-update-license-type", "license-service-license-remove-license-fee"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3db84aa9-f4ec-4ac6-9d7b-b789bd225172", "name": "license-service-multi-form-builder-get-all-multi-form-builder", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1c74a401-8c81-4ee9-a6d7-25a2110d2f39", "name": "document-service-me-get-file", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bf5a9ad0-4be1-4186-a4a7-9b8631569c2b", "name": "license-service-profile-add-to-rejected-field-list", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b6f79ad3-ea40-465d-9bf5-fc797cea3cb4", "name": "config-service-me-tenant-get-roles", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6abe921a-d2d8-4e9d-94a9-87e084ad3aa0", "name": "license-service-entity-fee-get-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "441e7431-e835-416f-81eb-05145a22830a", "name": "license-service-merge-requests-create-merge-request", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "66757a1d-51a6-41dd-a4eb-9a800f060f96", "name": "config-service-me-tenant-make-active-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "11f0655b-e296-4ef3-921e-1891ae03bbb1", "name": "document-templates-service-templates-get-templates-by-category", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c384bd01-3d81-4085-aafc-ab373541fde2", "name": "license-service-participant-delete-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a1cb6ef8-a089-46c2-8677-2ca36aba8999", "name": "calculation-service-cart-change-price-on-cart-item-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b4a1f16b-0ca2-4690-9d57-53769040b960", "name": "auth-service-public-realm-get-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "504349b2-9aef-4868-a269-b8a5592f8858", "name": "image-processing-service-mock-extract-text-from-image", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c13f7cc8-978e-4e30-b6b2-c50fb6948f40", "name": "calculation-service-cart-remove-cart-item-by", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1d489e3b-02c5-4d2e-9f96-ff2575d49fff", "name": "report-service-type-delete-report-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ca2b17bb-abd5-4d67-88bb-a1544a1e0c3b", "name": "document-templates-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6e646784-6613-4df2-9a7a-82a99680e593", "name": "license-service-entity-fee-get-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d706748c-0732-4035-a0ca-ad3eca542229", "name": "config-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "41990466-b86e-4342-b847-9993f3287a64", "name": "license-service-license-get-outstanding-approvals", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "24fe8117-5234-4443-93de-3f20c3db136f", "name": "report-service-form-section-get-section-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "19fbcc92-d88e-4b72-9dfd-38f4f4e685ea", "name": "license-service-license-remove-license-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6e44d263-fdb4-4765-9a5e-870c5d829119", "name": "license-service-me-license-get-license-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b7788e89-1e9b-4d2e-9ceb-3d3951f3a905", "name": "config-service-tenant-get-user-roles", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "147e41d6-0691-4098-aefb-8c52b770c917", "name": "license-service-entity-delete-entity", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c17ea192-2cb8-48bd-bdc9-d3043229fb3d", "name": "license-service-license-create-final-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4eb1bc49-98d4-42ea-ab7d-2aa5b9b57972", "name": "coordinator-service-history-get-transactions", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4543ac5c-c85c-4e43-b4b4-03a3e2540801", "name": "payment-service-me-get-payments-by-order-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2b3bdfec-1370-41c5-bca2-3fe34403ac34", "name": "license-service-config-license-type-delete-license-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "edf0fc13-b692-4a86-9cc2-5640c56c767c", "name": "report-service-type-get-report-type-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8b9ea9ba-bf30-4db2-b38e-2f743aa0174d", "name": "config-service-app-property-get-properties", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e3fda169-b282-4d61-94ee-045fdb492f5c", "name": "calculation-service-Fee-get-fees-by-keys", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b5e46e80-2045-4580-899e-4d01f29355ec", "name": "license-service-report-get-dog-license-renewal-notice", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5a24a53e-0c14-4a15-ad13-ef79552463c2", "name": "notification-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ac2ebf9b-6b18-4607-8560-a5062030653d", "name": "config-service-app-property-set-properties", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "71dbfb26-4b47-47b4-b3d7-e4b4de62f71d", "name": "config-service-tenant-logged-in-user-get-tenants", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b6d75e5e-d8be-44cd-8cf6-8586e472eb08", "name": "license-service-license-mark-license-as-denied", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4c3d255f-2ac3-413a-a5c8-1defbca620e3", "name": "license-service-app-properties-get-all-properties", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b1775e34-874e-47fd-a6c3-886b5c071757", "name": "calculation-service-cart-remove-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7366c219-bfc0-4b3e-b6c0-d9e423adc090", "name": "license-service-json-storage-create-update-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "baefa06f-2ed4-4d30-8fe7-0aa9f34affa2", "name": "license-service-entity-fee-add-file", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fd1974cb-3807-4aba-a1cc-a98fffd6c3d3", "name": "document-service--get-file-preview", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "eafa7748-7322-480b-8b59-5572edf9f752", "name": "coordinator-service-fees-cartItem-update-fee-on-cart-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0936c4aa-2c38-4748-b2b7-06dedc1b6781", "name": "calculation-service-order-create-order-from-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "01727b74-9f39-43f7-b52d-9ed2fbc8cc34", "name": "notification-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e5f58cc4-082e-4b09-93e1-47ff4c940cf6", "name": "payment-service-providers-get-payment-providers", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a6e51e86-659d-446c-8bd3-745eaad98f7d", "name": "config-service-me-roles", "composite": true, "composites": {"realm": ["config-service-me-json-storage-get-json-storage", "config-service-me-tenant-get-tenants", "config-service-me-tenant-make-active-tenant", "config-service-me-tenant-get-active-tenant", "config-service-me-tenant-leave-tenant", "config-service-me-rule-evaluate", "config-service-me-tenant-join-tenant", "config-service-me-tenant-get-roles"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ebea8a02-9046-4d0f-9f06-ede504358206", "name": "license-service-participant-get-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b1e17683-a942-4797-971e-df73d7ea4a96", "name": "license-service-profile-get-dog-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3e2c217c-374f-462a-96c9-f728cfa75d6f", "name": "coordinator-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7c56e641-b1bf-4d80-91b8-0c99ef5ca02d", "name": "license-service-me-license-get-license-form", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "94d41c9b-8b6f-439f-916d-02bd6d005200", "name": "coordinator-service-me-participant-delete-participant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "50e73009-8dd5-47e6-a934-a07e47e74bcb", "name": "payment-service-authorizedotnet-webhooks-delete", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f84f3cbc-d4b5-4257-a281-3d4c07095c04", "name": "calculation-service-fees-upsert-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3b72989c-0f31-43db-b93b-4086ef76172e", "name": "license-service-me-entity-fees-get-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "98bfff79-a7e8-4046-8fcd-277b0e6247fc", "name": "license-service-participant-get-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9cf364d5-e628-4642-a9fb-b570dbc4cdf6", "name": "license-service-multi-form-builder-get-multi-form-builder-by-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "17148a88-4a31-4d00-861c-df1907b25bf3", "name": "license-service-reminders-run-delinquent-reminders", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f3dcbf30-6bbe-45e3-bccd-163a258dcdf4", "name": "auth-service-realm-create-composite-role", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7883bec3-eba8-482f-aa43-4d0f34a0888d", "name": "license-service-merge-requests-get-merge-request", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a733f48f-76ce-4aae-8ead-f88988679bb5", "name": "license-service-document-file-upload", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a2a79fcd-5015-4401-89b5-82ec79ebb4db", "name": "license-service-me-roles", "composite": true, "composites": {"realm": ["license-service-me-participant-mark-online-resident", "license-service-me-license-renew-license", "license-service-me-license-calculate-license-fees", "license-service-me-profile-get-individual-entity-and-all-associations", "license-service-me-event-type-get-event-type-for-drop-down", "license-service-me-merge-requests-get-merge-requests", "license-service-me-merge-by-code-exists-by-registration-code", "license-service-me-profile-get-license-entity-and-all-associations", "license-service-me-profile-remove-from-rejected-field-list", "license-service-me-license-delete-license-draft", "license-service-me-profile-get-document-entity-and-all-associations", "license-service-me-profile-get-rejected-field-list", "license-service-me-event-type-get-event-types-by-profile-type", "license-service-me-license-get-license-fees", "license-service-me-merge-requests-create-merge-request", "license-service-me-participant-get-fees", "license-service-me-event-add-dog-event", "license-service-me-participant-update-individual", "license-service-me-profile-clear-rejected-field-list", "license-service-me-event-add-individual-event", "license-service-me-merge-requests-get-merge-request", "license-service-me-participant-update-online-participant-contact", "license-service-me-entity-fees-get-fee", "license-service-me-participant-create-online-resident", "license-service-me-license-mark-license-as-pending-approval", "license-service-me-profile-get-dog-entity-and-all-associations", "license-service-me-event-add-address-event", "license-service-me-profile-get-address-entity-and-all-associations", "license-service-me-event-add-license-event", "license-service-me-license-add-dog-to-license", "license-service-me-participant-get-participant", "license-service-me-license-create-license", "license-service-me-event-add-document-event", "license-service-me-license-create-pending-license", "license-service-me-license-create-final-license", "license-service-me-participant-mark-offline-resident", "license-service-me-entity-fees-get-fees", "license-service-me-merge-by-code-merge-by-code", "license-service-me-event-type-get-event-types", "license-service-me-participant-update-dog", "license-service-me-participant-get-fee", "license-service-me-license-get-license-form", "license-service-me-event-type-get-event-type", "license-service-me-license-check-not-more-than-three-licenses"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "36394523-f250-4692-bf18-f05f2a266918", "name": "scheduling-library-scheduler-refresh-scheduler", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "05d2d669-15f8-4717-8a4e-5517a81ece19", "name": "license-service-me-event-type-get-event-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "70c14bf1-24eb-4fe1-8e83-2c70b48cf21f", "name": "config-service-json-storage-get-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d397c0ca-6316-499f-b2e9-a6f1a59138f1", "name": "config-service-sql-storage-get-by-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a273b9c4-83be-487c-806b-4354f368d7b1", "name": "report-service-report-generate-sync-report-by-report-type-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "11a9c535-8545-4396-8148-a80b6254228f", "name": "payment-service-authorizedotnet-webhooks-create", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "efc728b9-6e3c-440d-85af-eb1a0fb65129", "name": "config-service-me-tenant-get-tenants", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d1026a34-945c-48f3-9fac-644658abc3c7", "name": "auth-service-public-realm-get-all-realm-names", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4bd3c649-64e2-4143-86cf-816ff492c315", "name": "create-tenant", "description": "", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "71fa94ef-ece4-416e-8dbe-1cd59746352b", "name": "notification-service-processing-process-all", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f8298355-6b19-420e-ba17-6aff21106ca5", "name": "super-admin", "description": "", "composite": true, "composites": {"realm": ["auth-service-admin-roles", "coordinator-service-admin-roles", "documenttemplate-service-admin-roles", "document-service-admin-roles", "notification-service-admin-roles", "payment-service-admin-roles", "calculation-service-admin-roles", "license-service-admin-roles", "report-service-admin-roles", "ai-service-admin-roles", "imageprocessing-service-admin-roles", "config-service-admin-roles"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6f990023-601c-48a5-9802-9a966c5405d8", "name": "report-service-test-generate-pdf-report-by-template-key", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "22f1b74b-b805-4eb3-9cf4-ac7eac19a054", "name": "calculation-service-Fee-create-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "46dd3294-9cd0-4fe2-bf5c-3167e48bda72", "name": "notification-service-app-properties-get-property-by-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2c985517-01ef-40e9-b22b-322d7548a525", "name": "calculation-service-me-cart-get-cart-invoice", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "dc92869c-dfea-4fd4-8c66-8d084df16cc4", "name": "license-service-app-properties-delete-property-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fbc8d9cb-aec1-4b40-a9bb-672f3bc9dee6", "name": "license-service-me-participant-get-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "75f015f9-86b3-4d9e-ad4e-2b7a7b72770b", "name": "license-service-license-create-license-by-form-builder", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a011abea-e6a9-4536-a6af-e3a94e1be406", "name": "license-service-json-storage-create-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5c10fc0c-a6ba-4a9d-97ee-60e046a0fae9", "name": "license-service-license-create-pending-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "220d0979-12fc-4a9d-9874-bed32a4b8bfe", "name": "license-service-tenant-update-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f38dba44-8777-4bc5-bdac-637562c84c9e", "name": "license-service-license-is-vaccination-expired", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "39c270c0-337e-47d5-9499-8bc33a3f9800", "name": "license-service-license-get-house-hold-count-by-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1a43f5e6-a726-4fd7-af65-e018e06e840e", "name": "license-service-report-get-dog-license-online-announcement-letter", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "019fe07f-8aa8-4dfa-93b5-f76caf4331c6", "name": "calculation-service-cart-create-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e9901e88-1938-45c9-93e0-43f244572ec5", "name": "auth-service-realm-get-realm-representation", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b115d34e-29de-4953-8e17-66fb630edd17", "name": "auth-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b53c921a-58ed-4ae6-8651-a854b4f9a7ee", "name": "calculation-service-fees-get-manual-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c7feb45d-55e9-4d6e-9c29-bf9d3dc0bc60", "name": "payment-service-admin-roles", "composite": true, "composites": {"realm": ["scheduling-library-scheduler-start-scheduler", "payment-service-authorizedotnet-webhooks-get", "payment-service-Payment-test-log", "payment-service-Payment-submit-payment", "payment-service-authorizedotnet-webhooks-create", "payment-service-inbox-get-webhook-inbox-by-id", "payment-service-authorizedotnet-webhooks-get-all", "payment-service-providers-update-payment-provider", "payment-service-Payment-get-payments-by-order-id", "scheduling-library-scheduler-refresh-scheduler", "payment-service-authorizedotnet-webhooks-delete", "payment-service-permissions-seed-roles-by-realm", "payment-service-providers-create-payment-provider", "payment-service-authorizedotnet-webhooks-put", "payment-service-providers-get-payment-providers", "rabbit-library-task-direct-get-task", "rabbit-library-task-fanout-create-task", "payment-service-permissions-seed-roles-to-all-realms", "scheduling-library-scheduler-get-all", "rabbit-library-task-fanout-get-task", "payment-service-authorizedotnet-accept-webhook", "scheduling-library-scheduler-stop-scheduler", "rabbit-library-task-fanout-concurrent-create-task", "payment-service-providers-get-payment-provider-by-name", "scheduling-library-scheduler-reschedule-task", "payment-service-authorizedotnet-config-configure", "payment-service-Payment-get-payment-token", "rabbit-library-task-direct-concurrent-create-task", "rabbit-library-task-direct-create-task", "payment-service-Payment-get-all-payments", "payment-service-Payment-delete-payment", "payment-service-inbox-process-webhook-inbox-by-id"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5035c359-21ab-4e4c-b334-c22ad420b2c1", "name": "config-service-me-tenant-leave-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "20ed0486-23d2-4586-9241-5349161488df", "name": "notification-service-admin-roles", "composite": true, "composites": {"realm": ["scheduling-library-scheduler-start-scheduler", "notification-service-app-properties-get-property-by-name", "notification-service-status-get", "notification-service-notification-get", "notification-service-app-properties-delete-property-by-uuid", "notification-service-app-properties-get-cache", "notification-service-status-get-all", "notification-service-notification-cancel", "notification-service-app-properties-get-property-by-uuid", "scheduling-library-scheduler-refresh-scheduler", "rabbit-library-task-direct-get-task", "rabbit-library-task-fanout-create-task", "scheduling-library-scheduler-get-all", "notification-service-permissions-seed-roles-by-realm", "rabbit-library-task-fanout-get-task", "notification-service-status-update", "scheduling-library-scheduler-stop-scheduler", "rabbit-library-task-fanout-concurrent-create-task", "notification-service-processing-process-all", "scheduling-library-scheduler-reschedule-task", "notification-service-status-create", "notification-service-processing-process", "notification-service-app-properties-get-all-properties", "notification-service-notification-get-all", "notification-service-notification-delete", "notification-service-app-properties-create-property", "rabbit-library-task-direct-concurrent-create-task", "notification-service-app-properties-update-property", "notification-service-status-delete", "rabbit-library-task-direct-create-task", "notification-service-error-send-email", "notification-service-notification-create", "notification-service-permissions-seed-roles-to-all-realms"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "463b77a8-4eeb-42b3-bfc4-35e25598b9b4", "name": "notification-service-app-properties-get-all-properties", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "629a8648-cd96-4c87-a465-cf9dd93d2814", "name": "license-service-license-update-license-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4c91459b-1414-4dbd-b024-fa7b578a06a4", "name": "documenttemplate-service-admin-roles", "composite": true, "composites": {"realm": ["scheduling-library-scheduler-start-scheduler", "document-templates-service-templates-get-template-by-name-key", "document-templates-service-templates-refresh-template", "document-templates-service-templates-test-fill-template", "document-templates-service-templates-update-template", "scheduling-library-scheduler-refresh-scheduler", "document-templates-service-templates-get-template", "rabbit-library-task-direct-get-task", "document-templates-service-templates-get-all-templates", "rabbit-library-task-fanout-create-task", "scheduling-library-scheduler-get-all", "document-templates-service-templates-create-template", "document-templates-service-permissions-seed-roles-to-all-realms", "rabbit-library-task-fanout-get-task", "scheduling-library-scheduler-stop-scheduler", "rabbit-library-task-fanout-concurrent-create-task", "document-templates-service-permissions-seed-roles-by-realm", "document-templates-service-templates-get-templates-by-category", "scheduling-library-scheduler-reschedule-task", "document-templates-service-LocalOnly-refresh-all-templates", "document-templates-service-templates-download", "document-templates-service-templates-fill-template", "rabbit-library-task-direct-concurrent-create-task", "document-templates-service-templates-set-active-version", "rabbit-library-task-direct-create-task", "document-templates-service-templates-fill"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7c35048d-c380-4d5a-b0c2-c33f1ff40f85", "name": "license-service-event-add-address-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d0bf60ae-03f9-4a22-a5ed-2a4071d84c9d", "name": "license-service-config-license-status-create-license-status", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f1b6d65d-7a2f-4a36-a623-6f7259cc29fe", "name": "rabbit-library-task-direct-concurrent-create-task", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "50f26c31-a2dc-4104-852c-30697d4e37cb", "name": "report-service-test-generate-pdf-report", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d6b853a6-e018-4cda-b765-09d08d728141", "name": "document-templates-service-templates-test-fill-template", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "94eba63f-dc68-455b-bd1a-de65894c0e96", "name": "config-service-tenant-logged-in-user-get-active-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fd3079c7-ae65-4195-82ca-de83f1137566", "name": "report-service-report-create-with-report-type-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a8cfe533-6a0a-49ba-8f84-a269b3bd17ce", "name": "license-service-me-entity-fees-get-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a845a3cb-7d37-41c7-bf56-48e24973a3e0", "name": "license-service-multi-form-builder-update-multi-form-builder", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0654cd74-7d6d-4992-af50-01d1667f83b9", "name": "license-service-profile-clear-rejected-field-list", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "714a1468-404d-4148-8820-7fa10aaf288f", "name": "calculation-service-order-get-order", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1759072a-2027-4b34-a7c2-f82fb7788eb4", "name": "license-service-config-license-status-get-license-status-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "54d50666-449e-4a1d-bec8-af2dd5f586ef", "name": "document-templates-service-templates-set-active-version", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fde7ec93-498b-48f6-bd31-4241bbca5c5f", "name": "coordinator-service-report-create", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5a32a988-ca8e-4c18-8475-c087fd3b5a05", "name": "report-service-test-download-template", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f79535c3-be17-4069-b9e1-fb67163fd2f9", "name": "coordinator-service-license-create-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9bfb7bf8-be18-4932-9ac4-58543e5dc74b", "name": "license-service-code-lookup-batch-tag-create", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5a2bd034-a08b-4ba5-adab-d36be38d51b3", "name": "rabbit-library-task-direct-create-task", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "841c4d9b-49ab-4869-a092-47ec4ae7d6b1", "name": "auth-service-public-realm-get-realm-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7102996e-0495-48d9-8671-d9e35bd37d1f", "name": "coordinator-service-textextractor-process-file", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ec985356-2194-4948-bcde-27686406778a", "name": "license-service-event-add-document-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "30352002-9a19-4bd2-b268-aea70ba3a232", "name": "coordinator-service-me-health-get-service-health", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a063fda6-ba7e-4c7d-a465-228696412c20", "name": "ai-service-prompts-update-prompt", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "83d01dde-1bb0-45a6-b479-3ee372b061f6", "name": "config-service-tenant-logged-in-user-make-active-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "de132767-4956-4079-a1a4-8cb4deaa9db9", "name": "calculation-service-cart-get-cart-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ec6990ae-7729-46c9-bc6b-2e043c7bbc97", "name": "notification-service-app-properties-update-property", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ccc217bf-ce9d-459b-ac3b-a270d4c1f4fe", "name": "ai-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bb13ad0c-7a14-4bc2-9e5b-a2e98101ebae", "name": "document-service-me-get-file-preview", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9a7aa14f-80d6-4756-a54b-3f35af0aeea9", "name": "license-service-app-properties-get-property-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d4db11e8-e79b-46e4-bbcf-c9a52c86ea5e", "name": "license-service-config-license-type-create-license-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "017e49a3-48c4-4a37-8295-977af9045d6e", "name": "coordinator-service-me-checkout-checkout", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "*************-431a-9012-1eb52d6cd23c", "name": "license-service-merge-requests-reject-merge-request", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5b65a703-dcdf-46cb-8769-e77c1e7ad848", "name": "license-service-me-license-add-dog-to-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "13a26060-4648-4e5e-97ba-9380832896c8", "name": "report-service-report-generate-report-by-report-type-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c464d36d-31b4-420a-8f81-68b5b602c24c", "name": "license-service-me-profile-clear-rejected-field-list", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5f72096e-f23a-46b8-93f2-d59ad111d1bd", "name": "license-service-reminders-run-expire-licenses", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d921de28-c380-453b-92ae-2ab8489dcf36", "name": "ai-service-me-parse-ocr-text", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "109f717c-96c2-491c-a425-6ca3f47736be", "name": "license-service-tenant-get-tenant-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "dc4c0f02-784c-43d1-a53d-aaa5a70823f1", "name": "rabbit-library-task-fanout-get-task", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1831486f-fe22-4551-ac57-cc54a98f2581", "name": "report-service-report-create-with-report-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "77f24ac8-3855-40c7-a8cf-4b578670aeee", "name": "document-templates-service-templates-fill", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e44d8805-8e7b-418f-8abd-3570efa7b44c", "name": "calculation-service-order-roll-back-to-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "47751d53-b8f1-423a-84cd-58c9fe35304d", "name": "calculation-service-me-order-roll-back-to-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e22df17a-1b7f-4ada-9324-d73d4e4cc3e6", "name": "license-service-me-event-type-get-event-type-for-drop-down", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b9ca9fa2-b8db-4cf2-90ec-844f8bdfa365", "name": "ai-service-me-roles", "composite": true, "composites": {"realm": ["ai-service-me-parse-ocr-text", "ai-service-me-map-text-to-schema", "ai-service-me-map-image-to-schema"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c97c7573-c4ed-4117-93a0-1bb43fb0c1d9", "name": "config-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e2be74ed-d268-433b-989d-0dac32f92595", "name": "config-service-tenant-get-active-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c655d49a-ec37-44d9-aa03-b45679f2ed69", "name": "license-service-profile-remove-from-rejected-field-list", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2e5b7c8e-20b2-43b9-8ae0-d15e368cb42e", "name": "config-service-public-tenant-get-tenants", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "46d20c87-e321-4e02-9280-ccffad7cec87", "name": "rabbit-library-task-direct-get-task", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "074ce6ce-5a12-4687-b8b9-76503c9373e1", "name": "license-service-me-participant-get-participant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "368cf3cc-87fb-4486-9c1b-c92576f230e7", "name": "license-service-merge-requests-approve-merge-request", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2214cefc-fe97-4fb6-8a51-72d1b0873793", "name": "license-service-me-participant-update-dog", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4d8570c7-3d5c-47f9-813d-695f340b2dca", "name": "calculation-service-order-transfer-order", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b26ac7fe-f2c9-4df8-b67c-0d804f190ba3", "name": "license-service-license-calculate-license-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d96cfc97-ec5c-43da-b678-3251166e00ee", "name": "license-service-config-license-type-get-license-type-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9a84747e-8809-41c2-bcf3-b94addddb429", "name": "report-service-report-generate-json-report-by-report-type-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a6c04ab0-d4e9-4a10-bfcc-4f01fdc5e6a9", "name": "license-service-me-merge-by-code-merge-by-code", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ee0cf4c3-8438-42c0-b64f-7d06383f590f", "name": "calculation-service-me-order-cancel-order", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0ee67af7-e370-4f81-9d70-e314cf71436c", "name": "coordinator-service-license-get-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "67660129-823a-463b-a70e-7e95186744e8", "name": "calculation-service-cart-get-cart-invoice", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0f5f09e2-24fb-4d26-80a8-dc13aa541430", "name": "document-templates-service-templates-download", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d68dfa3a-9d75-422d-94ca-0954a2bbdd59", "name": "document-templates-service-LocalOnly-refresh-all-templates", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3536971d-6759-4d37-9f22-2cea8786eacb", "name": "ai-service-MapText-map-image-to-schema", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e2b9df28-775c-4ec6-bfc9-709a659e824a", "name": "license-service-me-event-add-address-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "58cea951-9e24-4f88-a43a-50c14fdaad48", "name": "license-service-event-type-get-event-types", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f371d0d4-9a78-4228-b597-c0eb44efa361", "name": "license-service-json-storage-delete-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "49879522-cec8-49a9-bb06-02ab63d42b51", "name": "payment-service-me-get-payment-token", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7508bf11-3627-4878-a097-d22b9d26d3c8", "name": "imageprocessing-service-admin-roles", "composite": true, "composites": {"realm": ["image-processing-service-ImageProcessing-extract-text-from-image", "image-processing-service-permissions-seed-roles-by-realm", "rabbit-library-task-fanout-create-task", "image-processing-service-textract-analyze-document", "rabbit-library-task-direct-concurrent-create-task", "rabbit-library-task-fanout-get-task", "rabbit-library-task-fanout-concurrent-create-task", "image-processing-service-textract-analyze-id", "rabbit-library-task-direct-create-task", "rabbit-library-task-direct-get-task", "image-processing-service-permissions-seed-roles-to-all-realms", "image-processing-service-mock-extract-text-from-image"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "94fd5b05-8a5a-4818-a5aa-6d3da640af6b", "name": "report-service-report-generate-json-report-by-report-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4f768449-3810-46bc-b18e-6dade76d7298", "name": "license-service-participant-mark-online-resident", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8e96be77-8e41-48c3-90da-7bc6cf0868a5", "name": "calculation-service-me-fees-get-all-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "cec9984b-a02b-46f4-88b8-590352a3a2c4", "name": "document-templates-service-templates-fill-template", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "080b9558-476a-4f3a-93ca-59ce7e7e3fe2", "name": "license-service-me-license-check-not-more-than-three-licenses", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "42fcda9b-6d78-42ea-be45-f93f08a02e8f", "name": "auth-service-public-realm-get-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fd0dced9-17c9-428d-b131-a261983d2ebe", "name": "license-service-event-type-create-event-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9a5a7dd8-8dd0-4a90-a595-c0f8c6af8946", "name": "calculation-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "be3feff3-5c97-42e2-a433-9352b347c8a8", "name": "license-service-me-participant-get-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "500f0ee7-6227-4dad-a153-140e148a4605", "name": "config-service-tenant-get-user-tenants", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d54e4fa6-1977-443e-8936-2c7e97cc8382", "name": "coordinator-service-cart-add-to-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "21a0b443-67c4-4e8a-b0d2-e8d2359933c2", "name": "image-processing-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "cbf9a905-7711-44ab-acb1-9385bfdcc757", "name": "license-service-config-license-type-update-license-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4921b803-898e-4de6-8437-b77832aeb2cb", "name": "license-service-event-type-get-event-type-for-drop-down", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c5038ca2-5bc1-4fb5-8d9b-7fdea535e91c", "name": "license-service-license-check-not-more-than-three-licenses", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4b6148f0-7408-4f87-9ed9-08fa040ad97a", "name": "license-service-me-participant-mark-online-resident", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4a8be52f-b94f-4742-8604-8e6db60cb52f", "name": "license-service-search-search-email-exists", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f9bbdda9-fc9c-4381-989a-fa76c2fb8527", "name": "license-service-me-participant-update-online-participant-contact", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fa0c95cc-de17-4e7d-aa17-810fce580c58", "name": "coordinator-service-license-get-licenses", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "77f9e2c5-7740-4e7c-aea0-47e340cc58a6", "name": "image-processing-service-ImageProcessing-extract-text-from-image", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f6c5a52f-fc0d-4bf2-8720-9fe0e02eb4fe", "name": "license-service-config-license-type-get-all-license-types", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f64e3c89-d1fe-4b26-8848-1aa31745bb13", "name": "document-service--handle-file-upload", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4dc020e1-395c-4aff-98f7-bf4661bd35b2", "name": "calculation-service-me-cart-remove-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "81f53abe-03b9-4b8d-b408-c1f6d76baccb", "name": "license-service-license-query", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a3542986-f566-4c40-ba24-e6b783875141", "name": "config-service-app-property-delete-property", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9c41c851-e193-41f0-b547-1b9ea2ea40b2", "name": "coordinator-service-fees-cartItem-add-fee-on-cart-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7f8680c7-0e48-4f4c-ac01-655443ac1191", "name": "license-service-document-get-document-types", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5998fc33-5633-4778-b96c-b49950f4b65e", "name": "ai-service-admin-roles", "composite": true, "composites": {"realm": ["rabbit-library-task-fanout-create-task", "scheduling-library-scheduler-get-all", "scheduling-library-scheduler-start-scheduler", "ai-service-prompts-get-prompt-by-id", "rabbit-library-task-fanout-get-task", "scheduling-library-scheduler-stop-scheduler", "rabbit-library-task-fanout-concurrent-create-task", "scheduling-library-scheduler-reschedule-task", "ai-service-permissions-seed-roles-to-all-realms", "ai-service-permissions-seed-roles-by-realm", "ai-service-prompts-get-all-prompts", "rabbit-library-task-direct-concurrent-create-task", "scheduling-library-scheduler-refresh-scheduler", "ai-service-prompts-update-prompt", "ai-service-map-text-parse-ocr-text", "ai-service-prompts-create-prompt", "ai-service-MapText-map-text-to-schema", "ai-service-prompts-get-prompt-by-key", "rabbit-library-task-direct-create-task", "rabbit-library-task-direct-get-task", "ai-service-prompts-delete-prompt", "ai-service-MapText-parse-ocr-text", "ai-service-MapText-map-image-to-schema"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4f79e728-57a9-4374-b2c1-04503963061e", "name": "license-service-code-lookup-create-or-lookup", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8ec00b7d-2e76-484f-822e-e9cc17042ac6", "name": "config-service-sql-storage-get-all", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8e08930a-34fb-414f-8151-2e8b80ef034a", "name": "config-service-me-json-storage-get-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ec5d2f08-ee73-4cb1-b4e5-37a7e9637862", "name": "document-service-me-roles", "composite": true, "composites": {"realm": ["document-service-me-get-metadata", "document-service-me-get-history", "document-service-me-get-file-preview", "document-service-me-get-file"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9fd97f56-f5e5-471b-bc4e-aeb374f9acc6", "name": "license-service-app-properties-update-property", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "dea4bcd1-9226-4de4-a9f7-544264660278", "name": "license-service-me-profile-get-individual-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "28a66b9c-3e0c-432d-9b83-4a3b371f4949", "name": "license-service-merge-requests-get-merge-requests-by-request-user", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "004323dd-ea5e-4a9d-a1d1-d6366920a394", "name": "config-service-json-storage-create-update-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "50547d98-1d04-4f08-987a-dbff8e1f402c", "name": "document-service--get-metadata", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0b6ec4eb-0202-4ac6-9898-b7c498bdd245", "name": "config-service-tenant-join-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "374a045a-5dff-4728-b881-9ed94e85242b", "name": "image-processing-service-textract-analyze-document", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f0f8fc6e-677d-4b3f-af6d-baf038e7f28b", "name": "license-service-entity-fee-add-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1f88b47c-8a01-40ef-8b23-e5d1e122bfa4", "name": "notification-service-app-properties-get-property-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "50af8fc6-52b5-4ac1-b156-f24fc51332b5", "name": "license-service-config-license-status-delete-license-status-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f5574365-dc65-44eb-a285-018af6b5dee1", "name": "auth-service-realm-add-role-to-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "40877c49-e646-4b91-9669-887058a1502c", "name": "payment-service-Payment-test-log", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e27e1cf7-59c4-4850-aed7-388c76b6814b", "name": "license-service-me-event-add-dog-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1239c14b-bd4c-4d2a-8ddc-4d0cba54da3e", "name": "calculation-service-me-cart-get-active-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f2a4d549-a9a7-4700-87f8-acdaaece7096", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fc19923a-6b45-4d89-a314-56128542dbfb", "name": "license-service-entity-fee-delete-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3d6c3474-6944-42b4-8d2c-7c17a27a9390", "name": "calculation-service-order-list-orders", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "75cd530a-874c-4df2-9825-ed5f53c71133", "name": "license-service-me-profile-get-dog-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a84bd5fd-8e4d-4775-b0bc-e7db0847feb0", "name": "payment-service-Payment-delete-payment", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "496e4adb-71d4-4183-b367-a6707c45e546", "name": "license-service-document-get-document", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "58a81fbc-ce47-4eae-b4d2-2bc3f065738a", "name": "config-service-me-tenant-get-active-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "81ef9984-6fc1-44eb-8910-0b7fbbbb2bfc", "name": "payment-service-Payment-get-payments-by-order-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bc7e6a0c-9fa9-476b-8415-f64933bee24e", "name": "calculation-service-fees-create-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bf2ac5bc-556c-4190-b6fa-5fe3fb793764", "name": "config-service-app-property-seed-properties", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "10393a12-e6ee-4af7-8aea-30922658e130", "name": "license-service-me-merge-requests-get-merge-requests", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "66126230-323a-4f1e-a28f-d40ec9244a3c", "name": "report-service-report-get-report-form-data-by-section-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b77ec86c-225e-4914-98c1-08fd90ee7eb7", "name": "coordinator-service-me-textextractor-merge", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "484448fb-6935-465d-b549-84dbe5e3f560", "name": "calculation-service-cart-clear-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0484a7cd-5063-4dd2-9073-537a05731d1e", "name": "coordinator-service-payment-submit-payment-by-items", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "199a67bc-c21c-4df1-9935-0a77ad687a11", "name": "calculation-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "14307532-842b-4899-a6e9-9b310e111468", "name": "calculation-service-cart-switch-active-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3a5e8a93-fae6-4f93-8f0a-75c7b76ba9db", "name": "license-service-profile-get-document-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "cf54e989-64c8-404a-9d4e-757943197fb6", "name": "license-service-reminders-run-expiration-reminders", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "31994e26-0a99-419c-a16a-9a3cae50984e", "name": "calculation-service-me-order-get-order", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7ad6c789-a06a-4008-a696-cf4ff21a16d4", "name": "ai-service-me-map-image-to-schema", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1c55615a-5740-4bb3-b456-1f0f562cec3f", "name": "license-service-qr-codes-generate-batch", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "20281aa2-9164-4d50-bb62-6d2e754720da", "name": "config-service-sql-storage-delete-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bc32f595-135a-467f-ab9b-9025b9a03b4e", "name": "calculation-service-fees-get-fees-by-keys", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b5c352a3-e2a7-445e-abc5-f816e9aab00b", "name": "config-service-json-storage-delete-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c9e8110c-a416-43f0-aba4-6d716fa8c888", "name": "coordinator-service-payment-submit-payment-and-delete-cart-items", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6201ab0b-865b-4104-87b4-8931451c117a", "name": "coordinator-service-textextractor-handle-file-upload", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bbbc2592-52cd-459a-aaba-cd7afc54eab6", "name": "license-service-json-storage-update-json-storage", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "00f2f0b1-5618-49c7-bfc6-9625dad6281d", "name": "payment-service-inbox-process-webhook-inbox-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5a6805e5-5e02-48dc-9c37-1f5ff7be4c31", "name": "calculation-service-me-cart-create-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b3616fe7-d0b6-451c-a74b-a0199e21ea2a", "name": "notification-service-status-create", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "08982e87-80f8-4671-a9e8-21258b620f3f", "name": "documenttemplate-service-me-roles", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a1489dfb-e190-47b7-b895-896594272249", "name": "license-service-app-properties-get-property-by-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "70a07e9a-18c8-4684-9350-f58c79b5df83", "name": "license-service-license-get-license-form", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "26575829-3a1a-48ba-b4c5-cf7d6305c938", "name": "coordinator-service-me-payment-generate-receipt", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d1fc2d59-2de4-44d8-833b-1aa95b00e627", "name": "document-service-me-get-metadata", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "201ef1a6-ad15-48ed-add8-4f6bc2bd931c", "name": "notification-service-status-update", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b5347dc9-0784-470f-9a02-49bdecd7f30d", "name": "coordinator-service-me-roles", "composite": true, "composites": {"realm": ["coordinator-service-me-cart-add-to-cart", "coordinator-service-me-health-get-service-health", "coordinator-service-me-cart-infer-payee", "coordinator-service-me-participant-delete-participant", "coordinator-service-me-payment-generate-receipt", "coordinator-service-me-textextractor-process-file", "coordinator-service-me-license-generate-form", "coordinator-service-me-contact-us-contact-us", "coordinator-service-me-textextractor-merge", "coordinator-service-me-history-get-transactions", "coordinator-service-me-checkout-checkout-no-payment-due", "coordinator-service-me-checkout-checkout", "coordinator-service-me-payment-get-receipts"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1efa0374-15e5-435c-8a1a-abad6c53f694", "name": "coordinator-service-fees-cartItem-remove-fee-on-cart-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "cf889246-0a95-4c37-bb23-9b315507a3bf", "name": "payment-service-Payment-get-all-payments", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3b2daa3c-0cad-4b2f-b381-5496d54a01ec", "name": "document-templates-service-templates-refresh-template", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "763388fb-9919-47f9-8577-8062f6c93e9a", "name": "license-service-config-license-status-update-license-status", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "876f5b3d-9861-4211-80f6-5c9e3fa53073", "name": "license-service-qr-codes-generate-by-code", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "356eca8f-6ab0-4391-b523-c299f6f6e99b", "name": "coordinator-service-participant-delete-participant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6b6728e7-418c-4162-a48c-e4a88c0f5b74", "name": "ai-service-prompts-create-prompt", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "701fc626-8ed2-4999-a543-60485ab141de", "name": "license-service-config-license-status-get-all-license-status", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3cb866d0-5e63-4b6b-825a-3112a3b3941f", "name": "coordinator-service-payment-generate-receipt", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f13a8834-ecce-46d8-ab00-2cc53a493f82", "name": "calculation-service-admin-roles", "composite": true, "composites": {"realm": ["calculation-service-cart-find-carts-by-filters", "calculation-service-cart-remove-cart-item-fee", "calculation-service-order-transfer-order", "scheduling-library-scheduler-start-scheduler", "calculation-service-cart-create-cart", "calculation-service-order-create-order-from-cart", "calculation-service-cart-remove-cart-item-by", "calculation-service-cart-add-fee-on-cart-item", "calculation-service-cart-get-cart-invoice", "calculation-service-cart-list-carts", "calculation-service-order-roll-back-to-cart", "calculation-service-order-get-order", "calculation-service-permissions-seed-roles-to-all-realms", "scheduling-library-scheduler-refresh-scheduler", "calculation-service-cart-add-item", "calculation-service-cart-get-cart-item", "calculation-service-order-create-order-from-items", "calculation-service-cart-remove-item", "calculation-service-Fee-create-fee", "rabbit-library-task-direct-get-task", "calculation-service-order-list-orders", "calculation-service-fees-create-fee", "calculation-service-fees-get-fees-by-keys", "rabbit-library-task-fanout-create-task", "calculation-service-cart-delete-cart", "scheduling-library-scheduler-get-all", "calculation-service-cart-clear-cart", "calculation-service-order-edit-order", "calculation-service-order-cancel-order", "calculation-service-fees-get-manual-fees", "rabbit-library-task-fanout-get-task", "scheduling-library-scheduler-stop-scheduler", "calculation-service-Fee-get-all-fees", "rabbit-library-task-fanout-concurrent-create-task", "scheduling-library-scheduler-reschedule-task", "calculation-service-cart-change-price-on-cart-item-fee", "calculation-service-cart-get-active-cart", "calculation-service-cart-switch-active-cart", "calculation-service-fees-get-all-fees", "calculation-service-cart-get-cart-summary", "rabbit-library-task-direct-concurrent-create-task", "calculation-service-order-find-orders-by-filters", "calculation-service-Fee-get-fees-by-keys", "calculation-service-fees-upsert-fees", "calculation-service-cart-get-preview-invoice", "calculation-service-order-get-order-id", "rabbit-library-task-direct-create-task", "calculation-service-permissions-seed-roles-by-realm"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "dff07edd-6858-4627-b6b3-4a1872734ed0", "name": "coordinator-service-me-contact-us-contact-us", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f5c33d76-c471-43b2-95c0-b0f1e18188b1", "name": "calculation-service-cart-delete-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e46dc9ee-79bc-4b34-8d52-a42a552aa108", "name": "report-service-test-generate-json-report", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7d9f9f88-0c4a-4207-90e7-aa45cd88d7aa", "name": "image-processing-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "449b07c5-296a-4097-8fac-03b1dd17067b", "name": "notification-service-status-get-all", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f009170a-4f1a-4e55-80e8-b3e8aa95d426", "name": "report-service-type-update-report-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d2487796-f6de-4bf5-848c-ee58366ca008", "name": "license-service-qr-codes-generate-with-text", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fefcedda-0b51-4d91-81f1-899e1ea87eab", "name": "manage-admin-settings", "description": "", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1c11b671-e221-48da-b58c-23f8a03cb6fd", "name": "calculation-service-cart-list-carts", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4d215537-44e0-454c-a017-c9ba72385950", "name": "coordinator-service-license-generate-form-async", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5c36b6a1-8fea-4482-b04b-8a022a965e92", "name": "auth-service-user-delete-user", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "83c3e041-3d9b-400c-84ea-3c5a1249850c", "name": "license-service-me-license-mark-license-as-pending-approval", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d9403650-0818-49bb-9189-7b06391fa6cb", "name": "calculation-service-Fee-get-all-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d6ad6f40-b1be-4fa1-893c-18f0a27a6402", "name": "report-service-form-section-add-section", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7cc6962e-d5d1-4949-b5a8-8d87c25f19f1", "name": "document-templates-service-templates-get-template-by-name-key", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7fc97ac8-485b-4da5-ae72-f708045d0b7b", "name": "report-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bbe46aab-decb-4340-923e-7a8427a1de2e", "name": "license-service-participant-delete-participant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a6a9888a-cd4e-44c0-b77d-c0e7cf6c5589", "name": "config-service-tenant-logged-in-user-join-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "67879f41-ad9b-4182-be6e-46e98429b569", "name": "config-service-sql-storage-delete-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2a22652a-fa47-4a08-b9db-4e66d6869e7b", "name": "license-service-event-type-get-event-types-by-profile-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5452afbf-7822-4a36-82cd-f0977e7eefef", "name": "license-service-tenant-get-all-tenants", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a52ade74-a53f-44c7-a614-91404ae08abd", "name": "license-service-qr-codes-generate-and-upload", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "917a5836-a59d-4f90-8de0-5f0de91d641a", "name": "image-processing-service-me-textract-analyze-document", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a0e98574-d14c-4ba5-a2ad-1cc3e8f3fdfb", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "893c93f4-efab-4970-9fa2-ad712904bc0c", "name": "license-service-me-event-add-license-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8745a547-d5f6-41f6-b235-589ea47bb8eb", "name": "license-service-profile-get-individual-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0d0f7d0b-665e-49c2-a2e2-e3a5bcec31a7", "name": "license-service-json-storage-get-json-storage-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5970bdec-a743-4e40-b4c5-1a6e02049e5f", "name": "payment-service-authorizedotnet-webhooks-put", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "883612c0-5336-4c18-8114-8c8aeedf8783", "name": "license-service-participant-mark-approved", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8eb05aab-e87c-442d-9f7e-84f5eb683a70", "name": "license-service-me-event-type-get-event-types", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "47c9ad98-a52f-41ba-a80e-65bad7bc6a90", "name": "license-service-profile-get-rejected-field-list", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d3ac0af6-ef8d-4c3a-b53f-770bea2499fc", "name": "resident", "description": "", "composite": true, "composites": {"realm": ["config-service-me-roles", "notification-service-me-roles", "license-service-me-roles", "document-service-me-roles", "documenttemplate-service-me-roles", "report-service-me-roles", "coordinator-service-me-roles", "payment-service-me-roles", "calculation-service-me-roles", "imageprocessing-service-me-roles", "auth-service-me-roles", "ai-service-me-roles"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "cb4b181b-7884-439e-8543-05343453e09c", "name": "license-service-participant-create-online-resident", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c892570c-f10a-45bc-aa5f-3cd92f14445b", "name": "calculation-service-cart-get-active-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "81452169-03fe-454d-ac0c-dacbba80c51d", "name": "coordinator-service-me-checkout-checkout-no-payment-due", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "27559262-208e-422d-b9b3-c251cf09ab99", "name": "config-service-sql-storage-delete-by-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6a5a3a12-56e8-4a30-b44e-63fe11124594", "name": "coordinator-service-payment-get-receipts", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f618b1ae-b03b-40b0-84ae-3053be7f7698", "name": "coordinator-service-me-cart-infer-payee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c3995631-86dd-4a7f-871a-1a032df1d712", "name": "license-service-profile-get-business-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9938c758-9f6b-4945-8920-72bf182c64ef", "name": "scheduling-library-scheduler-get-all", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a90da5e4-be15-451c-bb23-d437327ce7fc", "name": "config-service-sql-storage-test", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a0ddaf18-e45d-4d4f-bd90-57f9a97427fc", "name": "image-processing-service-textract-analyze-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f8272dfe-62ce-422d-8eeb-6d5032d66143", "name": "ai-service-prompts-get-all-prompts", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "510a7b78-8f1f-41d6-8674-fcec60b7ecdc", "name": "license-service-participant-mark-unapproved", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "66a7dae4-2af4-41da-aa08-cf73f9c3faa5", "name": "license-service-me-merge-requests-get-merge-request", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "75fb435a-1e79-4746-a80e-e7df49e676a2", "name": "license-service-participant-update-participant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "01bbd5fe-2d05-4eac-84d1-f4366673d2a7", "name": "license-service-public-code-lookup-get-by-code", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1b67ca2e-f417-40e3-acd6-c0537f4ef935", "name": "calculation-service-me-cart-remove-cart-item-by", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ffdefe1c-a80e-406b-bfe9-fb76731cdd2f", "name": "license-service-code-lookup-get-by-entity-type-and-entity-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b7eac160-f071-418c-806a-f74fe2674794", "name": "config-service-rule-evaluate", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "11adf751-ff52-4b09-9d3b-bfec50029161", "name": "license-service-me-profile-get-license-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5a0a7229-9227-40c1-b99f-12347379681b", "name": "coordinator-service-me-textextractor-process-file", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "997cb976-fbec-4d34-a4e1-171e8f61a129", "name": "license-service-me-event-type-get-event-types-by-profile-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ae2a24bf-a8c2-45ee-b365-517f80731d86", "name": "coordinator-service-payment-submit-payment-by-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c98ab9e2-f3d5-406f-8150-f8b6c9fdb2bf", "name": "auth-service-me-roles", "composite": true, "composites": {"realm": ["auth-service-me-user-delete-user", "auth-service-me-user-get-user-profile", "auth-service-me-user-get-user"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9931424a-43b3-4d38-8a15-7abf91f9277f", "name": "default-roles-schenectady", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fcf41175-539b-48cf-be92-3febe693d7bf", "name": "document-service-me-get-history", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "77d445f1-e2b8-4901-af80-4fa0913b01e5", "name": "license-service-me-profile-get-document-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "64157d95-18d5-4fb3-9c42-ac18e42e9012", "name": "notification-service-notification-cancel", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7dd4a18c-f320-4baf-a9a3-c36a5d07ee3f", "name": "license-service-document-get-document-history", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6880846c-54bc-4945-8ec0-5a8a08982706", "name": "calculation-service-order-get-order-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6b27a996-4dae-4df9-9e79-12f1e408b4b2", "name": "notification-service-status-get", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2211760a-29fd-447e-933d-38f572abe24f", "name": "coordinator-service-admin-roles", "composite": true, "composites": {"realm": ["coordinator-service-cart-add-to-cart", "coordinator-service-license-generate-form-async", "coordinator-service-permissions-seed-roles-to-all-realms-for-each-microservice", "coordinator-service-license-get-license", "coordinator-service-payment-submit-payment-by-cart", "coordinator-service-payment-submit-payment-by-items", "coordinator-service-permissions-seed-roles-by-realm", "coordinator-service-report-get", "coordinator-service-cart-infer-payee", "coordinator-service-payment-submit-payment-by-order", "coordinator-service-fees-cartItem-update-fee-on-cart-item", "coordinator-service-textextractor-handle-file-upload", "coordinator-service-payment-submit-payment-and-delete-cart-items", "rabbit-library-task-direct-get-task", "coordinator-service-participant-delete-participant", "rabbit-library-task-fanout-create-task", "coordinator-service-license-get-licenses", "rabbit-library-task-fanout-get-task", "coordinator-service-health-get-service-health", "rabbit-library-task-fanout-concurrent-create-task", "coordinator-service-license-generate-form", "coordinator-service-fees-cartItem-remove-fee-on-cart-item", "coordinator-service-checkout-checkout", "coordinator-service-textextractor-process-file", "coordinator-service-textextractor-merge", "coordinator-service-fees-cartItem-add-fee-on-cart-item", "rabbit-library-task-direct-concurrent-create-task", "coordinator-service-permissions-seed-roles-to-all-realms", "coordinator-service-payment-get-receipts", "coordinator-service-license-create-license", "coordinator-service-report-create", "coordinator-service-license-save-license-draft", "rabbit-library-task-direct-create-task", "coordinator-service-history-get-transactions", "coordinator-service-payment-generate-receipt"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "13f4b4fa-1207-43d1-9df7-056b99b1f65d", "name": "auth-service-realm-create-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "408fb5ae-ab30-4d56-afba-4f89c387ab61", "name": "calculation-service-cart-find-carts-by-filters", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5b33e419-19ea-43a3-96c4-cd63c09a68bd", "name": "config-service-me-tenant-join-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "35ef44a7-83f1-4990-adcd-a3ec9bea3a2a", "name": "license-service-me-participant-create-online-resident", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d25f522a-15e4-455a-bbc6-d581d16d53be", "name": "payment-service-inbox-get-webhook-inbox-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "72a32314-e564-4967-8023-a202aee773f6", "name": "license-service-me-profile-get-rejected-field-list", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "beb1cd4e-3325-4d65-b43a-5e51039e3afb", "name": "license-service-report-get-adhoc-report", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "cbf02bf4-438c-4b3e-a239-2ad2b1680c9a", "name": "report-service-report-generate-sync-pdf-report-by-report-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e316c267-2916-4004-8bb8-015fd5dc6c72", "name": "calculation-service-cart-add-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f00adfd0-1f3b-4310-85aa-61317bfb22df", "name": "scheduling-library-scheduler-start-scheduler", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b0b30eb5-e933-43a4-a743-1e9b4f552a1d", "name": "license-service-merge-requests-get-merge-requests", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9f3d8658-c70c-42e4-bac7-7600e67b1b10", "name": "config-service-tenant-logged-in-user-get-roles", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d9005775-bed5-499a-b1c5-68ce50c8b2af", "name": "payment-service-authorizedotnet-config-configure", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "dfeaf1b9-9bea-4da3-8e61-535ec5d0a297", "name": "payment-service-providers-update-payment-provider", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6d3c5c2b-6d8e-4a84-885b-7096c7e7abd8", "name": "license-service-me-license-create-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "25a4dac2-fd03-4926-8490-ec2490242701", "name": "notification-service-status-delete", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fb3b24ec-3a42-4527-95be-a5a083f6b8e5", "name": "license-service-event-add-dog-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c15678cb-4e73-4630-94b0-9ee9f66eb3d6", "name": "calculation-service-cart-remove-cart-item-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6334f62d-1583-409f-b77f-6941858003c3", "name": "license-service-me-profile-get-address-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8303f766-18bb-4bbe-ab0d-18e23f70f13a", "name": "license-service-entity-fee-update-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8ff43c3f-e112-4516-9340-d049207db300", "name": "calculation-service-order-find-orders-by-filters", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3c9ef217-6866-4fd7-b67b-e17bde2b1468", "name": "config-service-sql-storage-create-or-update", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0c179d64-ad19-4183-9858-c7236d03df6d", "name": "license-service-me-license-create-pending-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4784c251-4e97-41ec-8bea-26c1f78c4608", "name": "license-service-document-save-document-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8f1d5acc-b53d-45f2-a8cf-524e72e8703a", "name": "license-service-profile-get-contact", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a53b274b-0ff8-446a-81fe-298d4686436d", "name": "config-service-tenant-leave-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b10b0e1b-e0eb-4dff-aa53-6df4c099638f", "name": "coordinator-service-report-get", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d621b6b6-fdf7-4575-9d3a-27671d0e50e2", "name": "license-service-license-add-license-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "986c83f7-922b-4ffd-b4a1-7e58d02e752a", "name": "license-service-participant-add-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8ea216a4-375a-41b1-8761-c0bc090b1152", "name": "config-service-tenant-make-active-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0f036d84-a970-4f90-98d3-3ea55afbe9f3", "name": "payment-service-providers-create-payment-provider", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "245e7c2a-b6c0-41f0-81a4-167576a2bac9", "name": "coordinator-service-payment-submit-payment-by-order", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "311f956a-ce28-4033-bab3-029415c54308", "name": "license-service-me-license-create-final-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5d090ff5-0b2b-4aa6-9a9e-56f115b8f37b", "name": "notification-service-app-properties-get-cache", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c40e2ae2-6937-4a8a-a88d-98017366ade0", "name": "license-service-search-search-parcel", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "21be6457-31e6-49e9-961d-3ca1028003eb", "name": "report-service-report-generate-async-report-by-report-type-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "90e79e6c-7716-4c9d-bc39-02f1d8259c2a", "name": "license-service-profile-get-address-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e035872d-526b-49f9-8249-2ad4bd23a139", "name": "coordinator-service-permissions-seed-roles-to-all-realms-for-each-microservice", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d85ca055-d670-4cf4-ac7c-cd0ace0ad0b9", "name": "license-service-me-license-renew-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6c36a1d8-ebe2-4890-894a-347504d24b25", "name": "report-service-report-generate-report-by-report-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2fd1d92f-c676-457e-aee2-feb0c09980d2", "name": "document-service--get-file", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "144dca55-8b19-46a3-be4f-07d346e0c230", "name": "payment-service-me-submit-payment", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "889bb3e1-7316-4945-9f9d-07faa2027f1e", "name": "report-service-type-get-all-report-types", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "833053a8-e93a-47b7-9b65-12d721c032bf", "name": "license-service-entity-add-association", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1a98ccce-4005-4510-ab5e-4990a2113ceb", "name": "document-templates-service-templates-get-all-templates", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9a8dcb6b-3644-45fd-bd42-10f70590c87d", "name": "city-admin", "description": "", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "83931ea3-4379-40c0-b6ed-d0809682b9c3", "name": "license-service-profile-get-license-entity-and-all-associations", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b1b27eeb-f04b-4e95-979f-b1ce932fcfff", "name": "coordinator-service-license-generate-form", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "fc82bf77-3a44-4f09-b37b-8c603bc9c719", "name": "image-processing-service-me-extract-text-from-image", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a0cd6772-7134-4856-906d-6176b37e077a", "name": "config-service-public-tenant-get-identity-providers", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2b67dfaa-1336-4b89-93c9-524b8fdba95d", "name": "report-service-me-roles", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4534e54c-7bac-4b8a-9026-e707fbc74ec5", "name": "license-service-entity-create-entity", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b443897f-cb68-445d-8b1e-88e9533470ee", "name": "report-service-report-create-adhoc-report", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "76254df2-c02b-43ea-a612-c1f928fe8313", "name": "payment-service-authorizedotnet-accept-webhook", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a043f639-22bc-47f9-934f-f17eecc4924d", "name": "license-service-event-add-license-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c2cfc5e1-5ca2-43de-950a-52377226563c", "name": "auth-service-me-user-get-user-profile", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7c3193b9-b6f5-44f4-a59e-39f1e7e67d7b", "name": "config-service-tenant-create-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "048c483e-b9ca-463e-a28e-6636c5efc819", "name": "notification-service-notification-get", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a7eec4a2-8546-4b0d-9dc9-a6268a58a188", "name": "license-service-qr-codes-generate-qr-code-no-upload", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8b5ea13c-ec0e-48e9-825c-fb15322eff17", "name": "calculation-service-cart-get-cart-summary", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c81fb0bb-703e-459a-be7f-06b9f805491b", "name": "calculation-service-me-cart-list-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1a2ea2da-68b0-44a2-a534-2d76e4cb313a", "name": "license-service-tenant-create-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4f390c49-85c2-4f92-ad87-a498c0338787", "name": "report-service-admin-roles", "composite": true, "composites": {"realm": ["report-service-type-get-all-report-types", "scheduling-library-scheduler-start-scheduler", "report-service-type-add-report-type", "report-service-form-section-get-section-by-uuid", "report-service-report-generate-json-report-by-report-name", "report-service-test-generate-json-report", "report-service-report-generate-report-by-report-name", "report-service-report-generate-async-report-by-report-type-id", "report-service-type-update-report-type", "report-service-report-get-report", "report-service-report-create-adhoc-report", "scheduling-library-scheduler-refresh-scheduler", "report-service-permissions-seed-roles-to-all-realms", "report-service-type-get-report-type-by-name", "rabbit-library-task-direct-get-task", "report-service-report-generate-sync-report-by-report-type-id", "report-service-report-create-with-report-name", "rabbit-library-task-fanout-create-task", "scheduling-library-scheduler-get-all", "report-service-test-generate-pdf-report", "rabbit-library-task-fanout-get-task", "report-service-report-generate-json-report-by-report-type-id", "scheduling-library-scheduler-stop-scheduler", "report-service-type-get-report-type-by-uuid", "report-service-report-generate-sync-pdf-report-by-report-name", "report-service-form-section-get-all-sections", "rabbit-library-task-fanout-concurrent-create-task", "report-service-permissions-seed-roles-by-realm", "scheduling-library-scheduler-reschedule-task", "report-service-type-delete-report-type", "report-service-test-download-template", "report-service-report-generate-report-by-report-type-id", "report-service-form-section-add-section", "report-service-report-create-with-report-type-id", "report-service-report-generate-async-pdf-report-by-report-name", "report-service-test-get-templates", "rabbit-library-task-direct-concurrent-create-task", "report-service-report-get-report-form-data-by-section-name", "rabbit-library-task-direct-create-task", "report-service-test-generate-pdf-report-by-template-key", "report-service-form-section-delete-section"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a2e9a04a-0384-4d62-bca2-44c5f18f1df4", "name": "ai-service-me-map-text-to-schema", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9ea71cda-2585-43a7-b7d6-5a8686f6b52f", "name": "ai-service-prompts-get-prompt-by-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0519b530-836f-448a-982a-657fdb2a7eb5", "name": "auth-service-user-get-user", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "10194382-919f-4392-8c5c-c383fd820073", "name": "payment-service-Payment-submit-payment", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b22763c2-2060-46b2-a5c0-26033e2c12a0", "name": "coordinator-service-textextractor-merge", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e1f3bb4d-8304-49ba-8a06-0d0e0a16e2ce", "name": "license-service-license-get-license-actions", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "71fa5ad0-16ae-4b42-a812-982b33d91f78", "name": "license-service-tenant-delete-tenant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "751ec388-bfc6-4c95-badb-67d533bd6e55", "name": "license-service-license-remove-participant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bfa8ced0-2caa-4279-9649-76af7d669cfb", "name": "license-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "695c44bc-2b06-4f60-85be-09b191688627", "name": "rabbit-library-task-fanout-create-task", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "be8444a0-2b3b-4fbe-9542-6037d358e276", "name": "license-service-license-create-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9a8c1119-9b7a-43da-b732-1881dcf16c69", "name": "auth-service-me-user-get-user", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "70f00f20-1a57-499b-834a-a5df7898ba93", "name": "config-service-admin-roles", "composite": true, "composites": {"realm": ["config-service-sql-storage-get-all", "config-service-app-property-set-properties", "config-service-tenant-leave-tenant", "config-service-sql-storage-get-by-uuid", "config-service-tenant-logged-in-user-get-active-tenant", "config-service-tenant-create-tenant", "config-service-permissions-seed-roles-by-realm", "config-service-sql-storage-get", "config-service-sql-storage-delete-by-name", "config-service-app-property-get-property", "config-service-tenant-get-active-tenant", "config-service-app-property-delete-property", "config-service-rule-evaluate", "config-service-public-tenant-get-tenants", "config-service-tenant-get-user-tenants", "rabbit-library-task-direct-get-task", "config-service-tenant-logged-in-user-join-tenant", "config-service-sql-storage-create-or-update", "rabbit-library-task-fanout-create-task", "config-service-sql-storage-test", "config-service-sql-storage-delete-by-uuid", "config-service-tenant-get-user-roles", "rabbit-library-task-fanout-get-task", "config-service-tenant-make-active-tenant", "config-service-tenant-logged-in-user-get-tenants", "config-service-app-property-seed-properties", "rabbit-library-task-fanout-concurrent-create-task", "config-service-json-storage-get-json-storage-history", "config-service-json-storage-get-json-storage", "config-service-sql-storage-get-by-name", "config-service-tenant-logged-in-user-get-roles", "config-service-sql-storage-delete-by-id", "config-service-json-storage-create-update-json-storage", "config-service-app-property-get-properties", "config-service-permissions-seed-roles-to-all-realms", "rabbit-library-task-direct-concurrent-create-task", "config-service-sql-storage-get-names", "config-service-tenant-logged-in-user-make-active-tenant", "config-service-public-tenant-get-identity-providers", "config-service-tenant-join-tenant", "config-service-json-storage-delete-json-storage", "rabbit-library-task-direct-create-task", "config-service-tenant-logged-in-user-leave-tenant"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ca3f8dd0-c647-43ba-8819-d2656384628c", "name": "license-service-app-properties-get-cache", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2c53794f-84af-4ab2-9842-3a7d9a35ce47", "name": "license-service-public-profile-get-dog", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6f1e2619-af75-421c-9159-17b317d0797b", "name": "license-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f1e3bc8d-4545-467d-ae5b-8a79c38bdbe6", "name": "license-service-document-get-documents", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0dcfef8e-c3bb-45a5-acfb-b25ee8da2d73", "name": "license-service-license-renew-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e7918625-105c-469e-b4d6-50c292032878", "name": "coordinator-service-me-history-get-transactions", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8929a709-4fca-4523-8111-82750d03f676", "name": "calculation-service-cart-get-preview-invoice", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "13585b67-11ce-4c6b-a6f1-8097b940d5aa", "name": "license-service-search-search-dog", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5320c295-6e5a-4fef-aeae-9d4cf2210697", "name": "notification-service-app-properties-delete-property-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a6b12227-7315-4670-9ce5-5ca821f01765", "name": "calculation-service-order-cancel-order", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "61a6337b-af09-4253-8410-cf184cb5f025", "name": "coordinator-service-checkout-checkout", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "63c5b39c-4417-4b2f-aa6d-1f2aa49e77ae", "name": "coordinator-service-license-save-license-draft", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "550e67d1-2e1e-40ac-864f-77e57de02352", "name": "license-service-license-re-process-fee", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2fdb28e7-6469-4d7c-968b-5a571c29aa4f", "name": "report-service-form-section-get-all-sections", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "286c7ea3-f5a3-41b9-ae81-6a6d2e7282d1", "name": "ai-service-prompts-get-prompt-by-key", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ec31a062-731f-4a04-9db2-244c4aacc505", "name": "license-service-entity-update-entity", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "52e717bc-02a2-40aa-9afd-5211ca477124", "name": "report-service-report-generate-async-pdf-report-by-report-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "27be7dd9-8b32-476b-8038-b3399a568d1a", "name": "license-service-license-delete-license-draft", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c57e41b4-870e-4e88-8a52-8f07ec5076b1", "name": "license-service-merge-by-code-merge-by-code", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "56b61aff-d3a7-49dd-9375-0ccee5082353", "name": "calculation-service-me-fees-get-fees-by-keys", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "02e447d5-c336-4988-b70e-f3add444e4f3", "name": "calculation-service-order-create-order-from-items", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a880a4f6-0376-4277-882c-8a24bf5d6c84", "name": "license-service-license-get-license-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "60a3c226-e790-41f0-85d2-525fb7cd927f", "name": "config-service-sql-storage-get-names", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0d7e1e09-3291-4086-98e4-178701432e30", "name": "config-service-sql-storage-get", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9867cd81-6d08-4fb1-8710-bf0eb8511b80", "name": "calculation-service-order-edit-order", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b35a94a0-7914-49f8-9b43-1c7591e51b4b", "name": "ai-service-MapText-map-text-to-schema", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f70a4e1b-1a42-4fa8-a23a-a3a210806668", "name": "calculation-service-fees-get-all-fees", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3dd03675-5ec8-4587-8a02-7e1c4122ecbc", "name": "license-service-search-search-global", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "134cfd3b-249d-4e45-9517-d121c3ed7bcb", "name": "document-templates-service-templates-create-template", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b639f96c-1571-47d8-aab2-0addebc2bdc4", "name": "notification-service-me-roles", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c05de110-ea24-41b5-a97c-bcda24b7b4e2", "name": "notification-service-error-send-email", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "01f9fc9d-e6ac-4f11-8c89-8de6537836ea", "name": "license-service-license-save-license-draft", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "bb198eb3-cafd-4609-954f-b6a749eb2670", "name": "document-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c0006524-244d-43a5-93d7-6a9817050fc0", "name": "report-service-form-section-delete-section", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4f7e68ce-f922-4f27-bfbf-e3363db1b7f6", "name": "license-service-license-mark-license-as-pending-approval", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c2b8819d-aa77-4e72-9fa9-ab88bcb2e01a", "name": "license-service-me-merge-by-code-exists-by-registration-code", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "46699ae1-adb6-455b-9c87-1299210a0fed", "name": "license-service-public-dog-report-sighting", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1ac08ae3-60a7-4126-8bc7-034953c88912", "name": "payment-service-providers-get-payment-provider-by-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "371fb4b2-2e52-4148-8d7f-2e812917abe4", "name": "license-service-license-mark-license-as-approved", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7765aa9a-a61c-4569-9ee6-c9c291629eab", "name": "coordinator-service-me-cart-add-to-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "f4a9fa55-20a9-49cb-9212-e70c21542610", "name": "document-service--get-history", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "71fc4464-18cd-4a44-9d11-e46dbeb8acb0", "name": "license-service-search-search-individual", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "e3ec2e3f-f578-4cd8-bfd3-f77fe79a6803", "name": "coordinator-service-me-license-generate-form", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "8b31add7-9c57-42d2-aee4-ffc3d4db06bf", "name": "document-service-permissions-seed-roles-to-all-realms", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4eee9358-1dc3-4880-97ee-3a96f5678ada", "name": "document-templates-service-templates-update-template", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ccb44ec7-69fe-43d4-b22c-bc1cb7e3ac47", "name": "config-service-json-storage-get-json-storage-history", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "520177c3-0d77-43d6-b14a-e04780b0f5b7", "name": "license-service-participant-patch-participant-contact", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7700d164-4ced-4133-b2cd-9b1124080835", "name": "calculation-service-me-order-list-orders", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "18366fb4-48ef-4f94-a955-e806d46584c9", "name": "payment-service-Payment-get-payment-token", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9f201ff5-e0c1-4514-b734-8b548300f01f", "name": "license-service-license-add-dog-to-license", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "75d73618-7d4a-48be-962d-a25139eb9366", "name": "license-service-merge-requests-approve-merge-request-by-entity-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ce7a3e2a-56bc-43e1-90df-c1a6609cacf6", "name": "payment-service-authorizedotnet-webhooks-get-all", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ac7646e4-2b18-45d1-ba7f-e7b7e3aea82b", "name": "coordinator-service-health-get-service-health", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3661bd2d-d25d-4beb-bbc7-3ed57768ca25", "name": "license-service-event-type-get-event-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "0847860b-2449-42df-bf3a-034af00e95f6", "name": "notification-service-notification-get-all", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "9111dcc0-c3f4-49b7-b0f4-831934b2a4d3", "name": "license-service-license-change-license-status", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "15d7258a-fd40-4756-bfa2-11f4c644b847", "name": "imageprocessing-service-me-roles", "composite": true, "composites": {"realm": ["image-processing-service-me-textract-analyze-id", "image-processing-service-me-textract-analyze-document", "image-processing-service-me-extract-text-from-image"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1e0fac33-ba83-4f79-b243-f7ce2d8cd539", "name": "license-service-merge-by-code-exists-by-registration-code", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1916594d-8dde-4a00-9b37-dbf030688e9e", "name": "report-service-type-add-report-type", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "16dea4c0-40ab-4f20-a076-3a5680d444a4", "name": "license-service-document-delete", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b63875ff-2399-45eb-90ef-19d8e8b63908", "name": "license-service-search-search-organization", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b88ee036-2c19-4331-a706-13dfbd354ef0", "name": "document-templates-service-templates-get-template", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "6ad40ba4-cd7b-477c-9ef0-f6802977035f", "name": "calculation-service-me-cart-add-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "43f8d226-5580-4b96-8acc-ae38c5372215", "name": "license-service-multi-form-builder-get-multi-form-builder-by-entity-id", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "89dcc634-5fa9-4a39-b70c-289bc202efe7", "name": "license-service-participant-create-resident", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "ffb514d2-b8de-435c-8386-23932d6cb69b", "name": "auth-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a4fd7b9c-e8de-4569-b209-5cb0ae5f8cc3", "name": "ai-service-map-text-parse-ocr-text", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d2023d23-46f6-4c88-a9b8-7ebd5c5ac84f", "name": "config-service-me-rule-evaluate", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "c6e0a02a-0a62-4762-ab6f-733936000d30", "name": "document-templates-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "91a2b338-fced-487b-bd01-c28404ea0425", "name": "auth-service-user-get-all-users", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d688de08-8b04-490e-90d2-e4d00fb61aaf", "name": "payment-service-me-roles", "composite": true, "composites": {"realm": ["payment-service-me-submit-payment", "payment-service-me-get-payment-token", "payment-service-me-get-payments-by-order-id"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "b7e9bb83-c6ae-4a81-99ad-43c017f6f640", "name": "payment-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "611d575b-6197-4a82-84b4-d61ee003d654", "name": "license-service-license-get-house-hold-count-by-participant", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "dd250ada-fc88-4107-8cd7-ee8411d52e81", "name": "config-service-sql-storage-get-by-uuid", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "047053b4-2f5f-4af5-b6f5-8704e73aa900", "name": "payment-service-authorizedotnet-webhooks-get", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "26a20725-84d4-4e73-9de2-413dc1a82dbc", "name": "ai-service-prompts-delete-prompt", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "503dce15-6d6f-4de1-8d22-dbd646d2bd84", "name": "calculation-service-cart-add-fee-on-cart-item", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "efc8379b-a65e-4c16-956e-123d9745f183", "name": "calculation-service-me-order-create-order-from-cart", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "1eb28988-7b77-475c-94a5-13fa9a69fba7", "name": "report-service-permissions-seed-roles-by-realm", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "2642863c-07a8-4d69-8297-fbed57ff00bc", "name": "license-service-me-participant-mark-offline-resident", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4a427524-ba47-47b7-9c66-8f5cbf663d56", "name": "license-service-me-participant-update-individual", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "24fa4f0f-8c0e-4b14-b965-b16c870b125f", "name": "notification-service-processing-process", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "93045ec2-c724-4298-b289-7a775599913f", "name": "document-service-admin-roles", "composite": true, "composites": {"realm": ["rabbit-library-task-fanout-create-task", "scheduling-library-scheduler-get-all", "scheduling-library-scheduler-start-scheduler", "rabbit-library-task-fanout-get-task", "scheduling-library-scheduler-stop-scheduler", "document-service--get-file-preview", "document-service--handle-file-upload", "rabbit-library-task-fanout-concurrent-create-task", "document-service--get-history", "document-service--handle-file-update", "scheduling-library-scheduler-reschedule-task", "document-service-permissions-seed-roles-to-all-realms", "document-service--get-file", "document-service--delete", "rabbit-library-task-direct-concurrent-create-task", "scheduling-library-scheduler-refresh-scheduler", "document-service--get-metadata", "document-service-permissions-seed-roles-by-realm", "rabbit-library-task-direct-create-task", "rabbit-library-task-direct-get-task"]}, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "d5225f24-73db-4d32-8e7b-b17dc8a259d8", "name": "rabbit-library-task-fanout-concurrent-create-task", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "5748227a-09f3-465e-8b20-0e4802d90bb5", "name": "notification-service-app-properties-create-property", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "4647bdea-f670-487a-a7cc-001de5e448b2", "name": "license-service-me-license-delete-license-draft", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "a86f4da5-70c7-413c-a896-224cdc7961d0", "name": "license-service-me-event-add-individual-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "7f34334b-fc05-4eb5-8a07-775bb773868b", "name": "license-service-multi-form-builder-delete-multi-form-builder", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "faef644b-38f6-425a-9418-8c819b781617", "name": "report-service-type-get-report-type-by-name", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "70bab5b9-0acd-45a4-ba7e-b4031876130e", "name": "license-service-me-event-add-document-event", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "45918056-9087-4491-ab1c-88a4c6ef4ddf", "name": "license-service-participant-mark-offline-resident", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}, {"id": "3cf26774-3f39-4a5d-aa3f-7b6ea527f5be", "name": "scheduling-library-scheduler-stop-scheduler", "composite": false, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde", "attributes": {}}], "client": {"chazy-clerkXpress-backend": [], "realm-management": [{"id": "047ce10a-9502-45b7-a0b5-5b326d7aa64d", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "61715faf-8aae-40af-816e-2f96722f0682", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "49b8e7f6-e24a-41b4-9f9a-2988d30d1024", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "2ed3fe9d-96b1-401b-ad01-8c9a20796dd9", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "54cadb3e-01dd-48a5-bb7c-2840d1a2657d", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "45d768ec-0134-488c-8f0d-8967ddab1423", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "6c2cd8fa-7326-4513-9f41-68786b5054bb", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "634035f9-e47c-4afc-97ca-0f268c5bee96", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "7ea9481f-c0a9-4b8a-87f9-91f2ffe4c265", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "18397841-0464-4259-a91b-b76120980549", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "34a5785f-9445-4c9f-a128-ad23f24da99a", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "1c03b8ad-abba-4e1a-8c63-d40af3aefed5", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "aeb06348-7912-4be0-af6c-a953605a7c12", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "14cc42ee-abc8-4e82-8fa5-b84ae5a6449f", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "1cedebe4-26ec-4364-b89b-851235d490c5", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "aadf51d2-0ac8-43ff-b144-c02c6b16045d", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-events", "view-authorization", "view-clients", "query-users", "view-users", "query-clients", "manage-identity-providers", "manage-users", "create-client", "manage-authorization", "view-identity-providers", "manage-events", "query-realms", "view-realm", "manage-clients", "impersonation", "manage-realm", "query-groups"]}}, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "2b592916-65c8-407e-ae52-23a3a5d816b4", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "b8e25246-5e32-419f-9977-eb8ccad34b49", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}, {"id": "c04972e8-398f-44c5-a75a-5a5f5af7c75d", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "attributes": {}}], "clerkXpress-swagger": [], "clerkXpress-backend": [{"id": "47f2162b-85ce-4b27-a5ef-ab618bf7095e", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "9953d457-0939-46b9-b9d2-30905cd2aa29", "attributes": {}}], "tenant4-clerkXpress-backend": [], "sczzzzzz-clerkXpress-backend": [], "security-admin-console": [], "sch-clerkXpress-backend": [], "account-console": [], "clerkXpress-frontend": [{"id": "ef403bc4-675f-4b50-854f-3c6446c5a0fb", "name": "license.create", "description": "", "composite": true, "composites": {"realm": ["license-service-license-add-dog-to-license", "license-service-license-create-pending-license"]}, "clientRole": true, "containerId": "eeee5906-5908-4253-8806-8739d13fd925", "attributes": {}}, {"id": "4da6bc77-953e-4c23-8b82-eb60c3e5ab45", "name": "super-admin", "description": "", "composite": false, "clientRole": true, "containerId": "eeee5906-5908-4253-8806-8739d13fd925", "attributes": {}}, {"id": "11d04f47-2b56-445e-a3b0-f7918f7353f6", "name": "resident", "description": "", "composite": false, "clientRole": true, "containerId": "eeee5906-5908-4253-8806-8739d13fd925", "attributes": {}}], "tenant3-clerkXpress-backend": [], "broker": [{"id": "8598df5e-5c1b-4b3b-9c51-cbd10516e3df", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "f251db35-ce31-4b92-80a1-27cd27ee03f2", "attributes": {}}], "tenant2-clerkXpress-backend": [], "clerkXpress-postman": [], "manewtenant-clerkXpress-backend": [], "tenant1-clerkXpress-backend": [], "clerkXpress-frontend-local": [{"id": "4d104603-99cb-4ab3-9a23-e21bd270ea5d", "name": "resident", "description": "", "composite": false, "clientRole": true, "containerId": "1fa9741a-5e1e-4f36-b867-8e1a2924a69f", "attributes": {}}, {"id": "b40e4b75-19aa-4c26-a8c8-b62e934ec319", "name": "super-admin", "description": "", "composite": false, "clientRole": true, "containerId": "1fa9741a-5e1e-4f36-b867-8e1a2924a69f", "attributes": {}}, {"id": "498ff5f8-58ec-4000-a900-ef8922460762", "name": "license.create", "description": "", "composite": false, "clientRole": true, "containerId": "1fa9741a-5e1e-4f36-b867-8e1a2924a69f", "attributes": {}}], "admin-cli": [], "account": [{"id": "********-22aa-443c-8e6a-02b1ffd2b9f6", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}, {"id": "287e91a3-7178-43ab-bc51-8fb41aa27726", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}, {"id": "e75f2f29-5536-4e58-8934-2677d2591633", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}, {"id": "06afdf10-afb6-40cd-8b0e-4c639aa316a7", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}, {"id": "1f7f7272-b152-45db-9468-6b9f5488d07f", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}, {"id": "e50029ae-7f0e-43ec-a081-eea186b9a64e", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}, {"id": "2d1cbd5c-332e-43f4-a87d-1c1a10860b5a", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}, {"id": "9add3be6-e6fe-4ee5-bdbb-a15d810b42e4", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "attributes": {}}], "schenectady-clerkXpress-backend": []}}, "groups": [{"id": "c96819a2-4adc-4fc6-a8ca-e1e64386567e", "name": "chazy", "path": "/chazy", "subGroups": [{"id": "ba101976-e8e7-455e-b896-0b149320af64", "name": "public", "path": "/chazy/public", "parentId": "c96819a2-4adc-4fc6-a8ca-e1e64386567e", "subGroups": [], "attributes": {}, "realmRoles": ["resident"], "clientRoles": {"clerkXpress-frontend": ["resident"]}}, {"id": "cc5e171f-8852-44de-8b4d-8a7d2d4febe4", "name": "super-admin", "path": "/chazy/super-admin", "parentId": "c96819a2-4adc-4fc6-a8ca-e1e64386567e", "subGroups": [], "attributes": {}, "realmRoles": ["super-admin"], "clientRoles": {"clerkXpress-frontend": ["super-admin"]}}], "attributes": {"logo": ["https://clerkxpress-public.s3.us-east-1.amazonaws.com/clerkxpress-logo.svg"], "displayName": ["City of Chazy"], "enabled": ["true"], "group": ["New York"]}, "realmRoles": [], "clientRoles": {}}, {"id": "0b45262f-613d-4592-b938-5cba871ce079", "name": "schenectady", "path": "/schenectady", "subGroups": [{"id": "b550de6d-1f7e-4b46-a33b-9b5917dfdc38", "name": "animal-control", "path": "/schenectady/animal-control", "parentId": "0b45262f-613d-4592-b938-5cba871ce079", "subGroups": [], "attributes": {}, "realmRoles": ["manage-admin-settings"], "clientRoles": {}}, {"id": "996b5ea4-e32f-4d57-ac4e-2d511faf9b0f", "name": "public", "path": "/schenectady/public", "parentId": "0b45262f-613d-4592-b938-5cba871ce079", "subGroups": [], "attributes": {}, "realmRoles": ["resident"], "clientRoles": {"clerkXpress-frontend": ["resident"]}}, {"id": "8708d4fa-e3fb-4115-9654-f29af89a9cec", "name": "super-admin", "path": "/schenectady/super-admin", "parentId": "0b45262f-613d-4592-b938-5cba871ce079", "subGroups": [], "attributes": {}, "realmRoles": ["super-admin"], "clientRoles": {"clerkXpress-frontend": ["super-admin"]}}], "attributes": {"logo": ["https://clerkxpress-public.s3.us-east-1.amazonaws.com/schnectady-logo.png"], "displayName": ["City of Schenectady"], "enabled": ["true"], "group": ["New York"]}, "realmRoles": [], "clientRoles": {}}, {"id": "db742021-0e00-447a-b863-83697f30b114", "name": "template", "path": "/template", "subGroups": [{"id": "2680b433-df38-4033-9ca5-5002fa409753", "name": "public", "path": "/template/public", "parentId": "db742021-0e00-447a-b863-83697f30b114", "subGroups": [], "attributes": {}, "realmRoles": ["resident"], "clientRoles": {"clerkXpress-frontend": ["resident"]}}, {"id": "613a7135-24f7-4861-a41d-c88401495435", "name": "super-admin", "path": "/template/super-admin", "parentId": "db742021-0e00-447a-b863-83697f30b114", "subGroups": [], "attributes": {}, "realmRoles": ["super-admin"], "clientRoles": {"clerkXpress-frontend": ["super-admin"]}}], "attributes": {"displayName": ["New Tenant"], "enabled": ["true"]}, "realmRoles": [], "clientRoles": {}}], "defaultRole": {"id": "9931424a-43b3-4d38-8a15-7abf91f9277f", "name": "default-roles-schenectady", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "5f32666e-18cc-43df-845e-7d887dc7cdde"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "bed45dc7-2141-4994-bd10-42ed598bd223", "username": "service-account-chazy-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["chazy"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "chazy-clerkXpress-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": ["/chazy/super-admin"]}, {"id": "7f453602-c4df-4fe9-b671-6a99058bca22", "username": "service-account-clerkxpress-postman", "emailVerified": false, "attributes": {"active_tenant": ["schenectady"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "clerk<PERSON><PERSON>-postman", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "clientRoles": {"realm-management": ["view-users"]}, "notBefore": 0, "groups": ["/chazy/public", "/schenectady/public", "/chazy/super-admin", "/schenectady/super-admin"]}, {"id": "bcbe3a04-bbc8-4c8f-b014-c0ca1f47ddc7", "username": "service-account-manewtenant-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["manewtenant"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "manewtenant-clerkXpress-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": []}, {"id": "ca0e6b54-a4d9-4d88-b658-1cda7b558d4d", "username": "service-account-sch-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["sch"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "sch-clerkXpress-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": []}, {"id": "f6f5a976-6b52-46b9-af20-4a47c0fab24a", "username": "service-account-schenectady-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["schenectady"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "schenectady-clerkXpress-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": ["/schenectady/super-admin"]}, {"id": "dfbdd8c4-9389-4344-9c0c-556cb95b85ed", "username": "service-account-sczzzzzz-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["sczzzzzz"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "sczzzzzz-clerkXpress-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": []}, {"id": "b3f0d419-48d7-406b-8879-c513c1bc9fe8", "username": "service-account-tenant1-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["tenant1"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "tenant1-clerk<PERSON><PERSON>-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": []}, {"id": "69b3dadb-6b2f-4dc0-a2bb-2e627399ef3d", "username": "service-account-tenant2-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["tenant2"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "tenant2-clerk<PERSON>press-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": []}, {"id": "a7734f28-3897-4ff4-b715-0843301c16f5", "username": "service-account-tenant3-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["tenant3"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "tenant3-clerk<PERSON><PERSON>-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": []}, {"id": "b61190bc-0019-4461-a4ab-5f8f29b5ec2f", "username": "service-account-tenant4-clerkxpress-backend", "emailVerified": false, "attributes": {"active_tenant": ["tenant4"]}, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "tenant4-clerk<PERSON><PERSON>-backend", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-schenectady"], "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "active_tenant", "roles": ["auth-service-realm-create-realm", "auth-service-realm-create-composite-role", "ai-service-admin-roles"]}, {"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"realm-management": [{"clientScope": "active_tenant", "roles": ["view-identity-providers", "view-realm", "manage-identity-providers", "impersonation", "realm-admin", "create-client", "manage-users", "query-realms", "view-authorization", "query-clients", "query-users", "manage-events", "manage-realm", "view-events", "view-users", "view-clients", "manage-authorization", "manage-clients", "query-groups"]}], "clerkXpress-backend": [{"clientScope": "active_tenant", "roles": ["uma_protection"]}], "clerkXpress-frontend": [{"clientScope": "active_tenant", "roles": ["super-admin", "license.create", "resident"]}], "broker": [{"clientScope": "active_tenant", "roles": ["read-token"]}], "account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}, {"clientScope": "active_tenant", "roles": ["manage-account", "view-applications", "view-consent", "view-groups", "manage-account-links", "delete-account", "manage-consent", "view-profile"]}]}, "clients": [{"id": "da262d69-f03f-44cf-a1ec-e1b703e67bfb", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/clerkXpress/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/clerkXpress/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "4fdaf9c2-f003-4cf6-b450-729373e9dd14", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/clerkXpress/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/clerkXpress/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "8e4ac2c2-6dea-49b5-a138-e746ea97440b", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "04e6c7b8-fbf0-43a8-b07c-7b63d7478468", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "f251db35-ce31-4b92-80a1-27cd27ee03f2", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "26620eeb-6440-45d6-8454-bdf9f31acf38", "clientId": "chazy-clerkXpress-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "3d1719c6-c881-4288-bc66-682a8e321806", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "0028f4d1-02b3-4209-85a2-bb3a208a5a5d", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "1566f9ee-d2b9-4363-b043-0c6685fbca62", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9953d457-0939-46b9-b9d2-30905cd2aa29", "clientId": "clerkXpress-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [""], "webOrigins": [""], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "8163c865-3687-486a-af38-43f0c63c9b5f", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "59ddb142-9dd9-40f7-bc37-8a4b01d7147e", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "88c8a72e-220e-4f08-bd23-7504484cd7d5", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "active_tenant", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "eeee5906-5908-4253-8806-8739d13fd925", "clientId": "clerkXpress-frontend", "name": "ClerkXPress Frontend Application (prod)", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["http://localhost:3000/*", "*"], "webOrigins": ["http://localhost:3000/", "*", "http://localhost:3000"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "http://localhost:3000/*##*", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "pkce.code.challenge.method": "S256", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "redmine_api_key", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "1fa9741a-5e1e-4f36-b867-8e1a2924a69f", "clientId": "clerkXpress-frontend-local", "name": "ClerkXPress Frontend Application (local)", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["http://localhost:3000/*", "*"], "webOrigins": ["http://localhost:3000/", "*", "http://localhost:3000"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "http://localhost:3000/*##*", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "pkce.code.challenge.method": "S256", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "active_tenant", "basic", "redmine_api_key", "email", "picture", "group"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "1924b725-5be7-4489-8cb8-3e7140960eea", "clientId": "clerk<PERSON><PERSON>-postman", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "24a49928-890c-40bc-ae2d-1ddb5da1a23a", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "583ba99d-ce00-436c-96d8-6b40b7fdbfa3", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "3843393c-ecb0-434b-be61-aed3d36a2263", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "openid", "roles", "profile", "user_title", "active_tenant", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "715a83b1-28f5-4560-a26d-1fff69d72432", "clientId": "clerk<PERSON><PERSON>-swagger", "name": "ClerkXPress License Service (Swagger)", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "*", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "tls-client-certificate-bound-access-tokens": "false", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "acr.loa.map": "{}", "require.pushed.authorization.requests": "false", "tls.client.certificate.bound.access.tokens": "false", "display.on.consent.screen": "false", "pkce.code.challenge.method": "S256", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "e1a963df-225a-4857-afc7-61c86fa8b48c", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "224baec5-bda3-40ef-817e-f9b3bc76bb0b", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}, {"id": "44151d37-fc20-478f-8489-a95c010597e0", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "openid", "roles", "profile", "user_title", "basic", "active_tenant", "redmine_api_key", "picture", "email", "group"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0f3f3f31-c212-48e0-bf24-117af94d5545", "clientId": "manewtenant-clerkXpress-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "27f6fde6-6e52-4775-8235-430943f67179", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "2d5942f1-9b52-4906-94cd-a0232be50564", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "23750c5e-78ed-4604-9aab-67d8f3d1cca4", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "283f07e1-62f5-4b11-b60d-4575afc8c9e6", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "99ebf69d-33e4-47a5-bda8-e9b4c53155cc", "clientId": "sch-clerkXpress-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "40d84e9c-beb6-43ca-a880-40c548a891b2", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "6f6c02f2-cccd-46a7-8354-db4aaab96eaa", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "436a6adb-8c0b-41f6-a5df-38536f634d27", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "fab5eb11-822f-48a8-b925-b0807ed17c5b", "clientId": "schenectady-clerkXpress-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "9f176fab-4ad3-4207-9e5e-d9a8ebf39e1b", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "80e54448-7285-4e59-ac5a-a2eaee6166bc", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "a8b4195d-bd49-41e1-a693-409f4ec2d375", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "f237e7de-0cc6-48e0-9b44-1c13aaf3f733", "clientId": "sczzzzzz-clerkXpress-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "17ad4e92-122b-470a-8206-ee471c2ae8a3", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "e715aa6e-9ee0-407a-961f-2581438ba1d9", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "5705ecdc-59ab-41e3-8ca5-74df41a1218d", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0efbe396-7e9e-457f-bb70-bee510bf609c", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/clerkXpress/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/clerkXpress/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "d71a4760-2147-425c-beb6-e8357c8c8624", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "39eacb97-0e41-4ceb-99b4-a12d5504e624", "clientId": "tenant1-clerk<PERSON><PERSON>-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "1b27fb09-eb57-4d95-ab98-20e0a9cd9ad5", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "aa6f2edf-249f-4a23-8c8b-1ad39ba64e07", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "3482b243-81cf-4616-8ba3-650e254fdacc", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "3119bf96-9816-4da5-a3e9-ffaf4aad233e", "clientId": "tenant2-clerk<PERSON>press-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "0d8d1b09-003d-4b6f-b4f1-be1b173fc255", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "5dbae887-747e-49d5-9fcf-57ae5624583a", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "1554f95b-d9e8-4131-b18d-c5c82b630672", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0bba06a0-8b3f-49c3-926c-76c544f1cd2c", "clientId": "tenant3-clerk<PERSON><PERSON>-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "c91dab2a-5142-461c-8cfd-9274590b4b5c", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "0e4414e5-5931-4145-b0b8-4a142fea3d5c", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "ec7b50a7-9dec-4860-a956-53135a5b5ac6", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "8a7d9dd7-7e01-4c04-b538-aee0cd03c22c", "clientId": "tenant4-clerk<PERSON><PERSON>-backend", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.secret.creation.time": "**********", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "b2ec9392-3e82-4021-8f93-5e12257565b0", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "9c2532f7-8e7e-46a2-8217-62e5389f22e4", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "1dc0c563-fa72-48f1-a204-4f6ce64c0140", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "user_title", "basic", "active_tenant", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "e50e870b-eccd-4a20-adb9-d9aa72cfdef2", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "3f467489-cead-47aa-817a-0c166380c806", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "1ca0e57c-8276-4efa-8a55-2c5f3c125110", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "e37782c3-d0d8-4d18-8649-30840ec4eb82", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "f3dd9da8-06c7-45f0-8d99-89bb5c04f04e", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "3383a061-8095-42be-84c2-84db84c8a9f7", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}, {"id": "3ca50d52-3610-42f6-b10f-e90b48220dbf", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}]}, {"id": "b6f2f432-ab03-4def-a902-37c4a924133c", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "73b8cb33-4f3a-4874-806c-20345c7bb26d", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "d6c28ff4-15ae-4b1f-891c-08e43bdf5248", "name": "active_tenant", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "642d821f-e7f6-48cd-a953-e44d5415690c", "name": "Active tenant mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "false", "introspection.token.claim": "true", "multivalued": "false", "userinfo.token.claim": "true", "user.attribute": "active_tenant", "id.token.claim": "true", "lightweight.claim": "false", "access.token.claim": "true", "claim.name": "active_tenant", "jsonType.label": "String"}}]}, {"id": "b71521b3-2c98-481b-a973-bc888a7e8746", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "66f34680-279b-447c-9104-cd76afaade5e", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "04d2ad6b-efed-4cbb-b36c-c1c686c65938", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "50c82b47-6657-496a-a1a2-93d602c897df", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "a2c432d9-6be1-476d-bef1-7913e34187c3", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "fe55e40b-5bb9-439f-8d64-212384ae51d8", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean", "userinfo.token.claim": "true"}}]}, {"id": "0da073a0-0ea9-4f62-b653-d3c18421d3e8", "name": "picture", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "88cfd6cd-e9e4-4073-9dcb-45d706072c1c", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String", "userinfo.token.claim": "true"}}]}, {"id": "29e520cc-9617-4725-8fab-6204216aedb5", "name": "redmine_api_key", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "b9be6b55-76d2-4b20-b2f4-18d37e79300c", "name": "redmine_api_key_mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "redmine_api_key", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "redmine_api_key", "jsonType.label": "String", "userinfo.token.claim": "true"}}]}, {"id": "638cc0c6-c146-4c5a-8851-cea991dda81e", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "5a0c2a87-5ac5-4af7-ac00-af0cd900b1ab", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "9830abf5-f0fa-4a0c-a2d4-036454473d1f", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "63de8988-e7db-4ede-9c49-1812b2cfb342", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "realm_access.roles", "jsonType.label": "String"}}, {"id": "d54cf7f9-1ad4-4888-b1a1-0c969929f462", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "3f8b8cf8-1e95-475c-b788-24600abdad5e", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "false", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String"}}]}, {"id": "2f97d764-89fa-46a6-b2ba-39acd78b3c82", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "ec98cf70-9dcb-48a2-92bc-14a2a4cec661", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "afb00867-a627-4458-9afa-18594439981a", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "dfefd060-dfa4-47ef-bbbf-04f6aaf953f2", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long", "userinfo.token.claim": "true"}}, {"id": "e2414e9e-70ee-4c39-a5a9-09fe0d721d6b", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "c9e93a6b-147f-4eda-8ec8-e8eaf380589e", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "8b6c1286-c5e6-4bb7-977a-0d288ebe2db8", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "9d987ece-0905-4406-bd9b-072ca667bb70", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "3ae910c6-16d2-4aa9-a565-fd581b1ff131", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "a75a49d3-3726-411e-9aa3-2eea75fc8bb8", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "3c17a4b5-9423-4482-92bb-1cfa2ed36486", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "a2f01fbc-2fad-4797-a42e-c3cf0b3cf746", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "de441e47-4af8-4ea8-b2cd-f22ce0bf365f", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "9dc8e90d-828f-4a48-81f6-555d8fb0ec3d", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "cca16a69-4787-4d8f-a465-c76e22720a5c", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "0ddfb1ff-6aac-4292-9026-4b31e544fc22", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String", "userinfo.token.claim": "true"}}]}, {"id": "b72f1114-792c-41fa-b585-ee97fd386654", "name": "user_title", "description": "The title for the user", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "eeb02ccc-41da-4b36-a45f-5804fb7f0fe6", "name": "user_title_mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "title", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "title", "jsonType.label": "String", "userinfo.token.claim": "true"}}]}, {"id": "dd6f869f-de73-4962-871f-c116e65c8321", "name": "openid", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"id": "8356efe8-ebc6-4ff5-9afd-1259c325e30e", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "1727ab16-a793-4813-bb72-79665e4c75f3", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean", "userinfo.token.claim": "true"}}, {"id": "918135be-45d3-427b-aba8-c941c2fa4c4f", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String", "userinfo.token.claim": "true"}}]}, {"id": "e42e61e6-3041-40d1-a532-4241e1c7f76d", "name": "group", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "7375c848-979d-4c96-b3be-6becb749bd9b", "name": "group", "protocol": "openid-connect", "protocolMapper": "oidc-group-membership-mapper", "consentRequired": false, "config": {"full.path": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "group", "userinfo.token.claim": "true", "multivalued": "true"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr", "user_title", "openid", "active_tenant", "basic"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src *; frame-ancestors *; object-src *;", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {"password": "**********", "replyToDisplayName": "", "starttls": "true", "auth": "true", "port": "587", "host": "smtp.office365.com", "replyTo": "", "from": "<EMAIL>", "fromDisplayName": "ClerkXpress", "envelopeFrom": "", "ssl": "false", "user": "<EMAIL>"}, "eventsEnabled": true, "eventsExpiration": 1800, "eventsListeners": ["jboss-logging", "email"], "enabledEventTypes": ["UPDATE_CONSENT_ERROR", "SEND_RESET_PASSWORD", "GRANT_CONSENT", "VERIFY_PROFILE_ERROR", "UPDATE_TOTP", "REMOVE_TOTP", "REVOKE_GRANT", "LOGIN_ERROR", "CLIENT_LOGIN", "RESET_PASSWORD_ERROR", "IMPERSONATE_ERROR", "CODE_TO_TOKEN_ERROR", "CUSTOM_REQUIRED_ACTION", "OAUTH2_DEVICE_CODE_TO_TOKEN_ERROR", "RESTART_AUTHENTICATION", "UPDATE_PROFILE_ERROR", "IMPERSONATE", "LOGIN", "UPDATE_PASSWORD_ERROR", "OAUTH2_DEVICE_VERIFY_USER_CODE", "CLIENT_INITIATED_ACCOUNT_LINKING", "TOKEN_EXCHANGE", "REGISTER", "LOGOUT", "AUTHREQID_TO_TOKEN", "DELETE_ACCOUNT_ERROR", "CLIENT_REGISTER", "IDENTITY_PROVIDER_LINK_ACCOUNT", "UPDATE_PASSWORD", "DELETE_ACCOUNT", "FEDERATED_IDENTITY_LINK_ERROR", "CLIENT_DELETE", "IDENTITY_PROVIDER_FIRST_LOGIN", "VERIFY_EMAIL", "CLIENT_DELETE_ERROR", "CLIENT_LOGIN_ERROR", "RESTART_AUTHENTICATION_ERROR", "REMOVE_FEDERATED_IDENTITY_ERROR", "EXECUTE_ACTIONS", "TOKEN_EXCHANGE_ERROR", "PERMISSION_TOKEN", "SEND_IDENTITY_PROVIDER_LINK_ERROR", "EXECUTE_ACTION_TOKEN_ERROR", "SEND_VERIFY_EMAIL", "OAUTH2_DEVICE_AUTH", "EXECUTE_ACTIONS_ERROR", "REMOVE_FEDERATED_IDENTITY", "OAUTH2_DEVICE_CODE_TO_TOKEN", "IDENTITY_PROVIDER_POST_LOGIN", "IDENTITY_PROVIDER_LINK_ACCOUNT_ERROR", "UPDATE_EMAIL", "OAUTH2_DEVICE_VERIFY_USER_CODE_ERROR", "REGISTER_ERROR", "REVOKE_GRANT_ERROR", "LOGOUT_ERROR", "UPDATE_EMAIL_ERROR", "EXECUTE_ACTION_TOKEN", "CLIENT_UPDATE_ERROR", "UPDATE_PROFILE", "AUTHREQID_TO_TOKEN_ERROR", "FEDERATED_IDENTITY_LINK", "CLIENT_REGISTER_ERROR", "SEND_VERIFY_EMAIL_ERROR", "SEND_IDENTITY_PROVIDER_LINK", "RESET_PASSWORD", "CLIENT_INITIATED_ACCOUNT_LINKING_ERROR", "OAUTH2_DEVICE_AUTH_ERROR", "UPDATE_CONSENT", "REMOVE_TOTP_ERROR", "VERIFY_EMAIL_ERROR", "SEND_RESET_PASSWORD_ERROR", "CLIENT_UPDATE", "IDENTITY_PROVIDER_POST_LOGIN_ERROR", "CUSTOM_REQUIRED_ACTION_ERROR", "UPDATE_TOTP_ERROR", "CODE_TO_TOKEN", "VERIFY_PROFILE", "GRANT_CONSENT_ERROR", "IDENTITY_PROVIDER_FIRST_LOGIN_ERROR"], "adminEventsEnabled": true, "adminEventsDetailsEnabled": false, "identityProviders": [{"alias": "microsoft", "internalId": "82122ec6-d293-4c90-822a-aba4a9e772b4", "providerId": "microsoft", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": true, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "config": {"hideOnLoginPage": "false", "clientId": "e57c1883-7dfc-4730-8cf2-4e887403207b", "acceptsPromptNoneForwardFromClient": "false", "disableUserInfo": "false", "syncMode": "LEGACY", "filteredByClaim": "false", "clientSecret": "**********", "caseSensitiveOriginalUsername": "false", "defaultScope": "openid profile User.ReadBasic.All"}}], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "1a0621de-1292-4e46-9bb7-58aab5316f3f", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "2ae57c4e-29d2-4083-b788-5a10506c4a22", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "afea3902-fb06-4bf7-a819-651d4ca09261", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "293354b4-175e-45d9-ba38-f5bd7bf2780a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "saml-user-attribute-mapper"]}}, {"id": "3c6df741-962a-43f7-98b9-777b86a36f58", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "72793047-b5c2-4fb3-956a-8bf3052a0275", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "46e04451-1d00-43a3-bbec-96073494cabd", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "0d4bc741-ed06-4404-97d7-f3ef18625c55", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "bca7edbc-4842-4d94-ac15-9059773b1c3d", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false}],\"groups\":[{\"name\":\"user-metadata\",\"displayHeader\":\"User metadata\",\"displayDescription\":\"Attributes, which refer to user metadata\"}],\"unmanagedAttributePolicy\":\"ENABLED\"}"]}}], "org.keycloak.keys.KeyProvider": [{"id": "c5a2a04e-469e-480b-88c9-188ac918c1cc", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS512"]}}, {"id": "4879f905-2f31-4247-a68a-e44c4fb1b0cd", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "c9e2dd4e-8457-4308-8f9a-4febdddb364b", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "256f4b79-97eb-40db-8fa4-d0522a88f769", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}, {"id": "5f0d0577-c598-41e4-83d8-ca39c37fee85", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "60561cc3-f236-486a-bafb-8ab698c599b1", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "096b78c1-b797-4250-8104-b05e2894c0da", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "80bdf06d-918d-42fd-b325-441448f0e26d", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e5cf7990-fa74-487e-9bb4-7cc93b2f6230", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "24e7048b-3870-4f1d-8b18-31d420e6a46b", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "b0126dbd-aaab-490c-ab09-5f3097c5ba13", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "5b0bbf8c-40d5-4a97-b864-ad8efd645d66", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "60732e34-c98c-41e1-8db5-22b93fb29c72", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "22fcb6d3-bc32-41da-bac3-aa3cad109ba5", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "3910af66-f8cc-42eb-8e28-b53517acf2ee", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "26770b82-bd72-4071-a2d0-e1d80b7b7f27", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "7641f6c6-60b0-4bfc-9ce4-9b1ddfc710d5", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "9f3b70be-7336-45c3-aa41-a99f08c4c6b7", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "72ea089e-7d4f-418e-8ac7-62c8bcc90b57", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "53911315-7b86-4c8f-81af-fc431a43979d", "alias": "idme", "description": "", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 0, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "957594b1-8340-4509-a51c-77deec61e585", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "2c89772b-e0bf-4a4f-b334-553caf47fdf4", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "fe3103b2-0b5e-48d3-bc8c-8aeb6262ca08", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "c5fc4972-b010-4f94-86af-c5f2b84e23f3", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "b5fa5080-71c6-4446-a09d-78998e4b22ce", "alias": "Password update if user is unique", "config": {"require.password.update.after.registration": "true"}}, {"id": "0a9755f0-d758-45ca-8d4c-049e48ae64a9", "alias": "Password update if user is unique", "config": {"require.password.update.after.registration": "true"}}, {"id": "8ab49540-ec49-4505-95b7-fb8d547a9182", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "724f63a8-ae67-4088-879a-1e5998f12353", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": true, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}, {"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": false, "defaultAction": false, "priority": 1001, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "actionTokenGeneratedByUserLifespan-execute-actions": "", "actionTokenGeneratedByUserLifespan-verify-email": "", "actionTokenGeneratedByUserLifespan.verify-email": "", "actionTokenGeneratedByUserLifespan.idp-verify-account-via-email": "", "clientOfflineSessionIdleTimeout": "0", "actionTokenGeneratedByUserLifespan.execute-actions": "", "cibaExpiresIn": "120", "actionTokenGeneratedByUserLifespan-idp-verify-account-via-email": "", "frontendUrl": "", "organizationsEnabled": "false", "shortVerificationUri": "", "actionTokenGeneratedByUserLifespan.reset-credentials": "", "cibaAuthRequestedUserHint": "login_hint", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "actionTokenGeneratedByUserLifespan-reset-credentials": "", "cibaInterval": "5", "realmReusableOtpCode": "false", "oauth2DeviceCodeLifespan": "600", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "acr.loa.map": "{}", "adminEventsExpiration": "900"}, "keycloakVersion": "25.0.1", "userManagedAccessAllowed": false, "organizationsEnabled": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}