# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
# https://github.com/takari/maven-wrapper#usage-without-binary-jar
.mvn/wrapper/maven-wrapper.jar

# Eclipse
.project
.classpath
.c9/
*.launch
.settings/
.metadata
.recommenders

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Spring Boot
spring-boot-starter-parent/
application-dev.properties
application-local.properties

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
application.log

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Generated sources
*/target/generated-sources/
*/target/generated-test-sources/

# H2 Database files
*.h2.db
*.trace.db

# Application specific
/uploads/
/downloads/
/temp/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production