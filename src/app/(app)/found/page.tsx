"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import Image from "next/image";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import {
  PublicCodeLookUpResponse,
  useGetPublicCodeDetails,
} from "@/hooks/api/usePublicCode";
import { requests } from "@/utils/agent";
import { LuLoader } from "react-icons/lu";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";

export default function FoundPage() {
  const { push } = useRouter();
  const [code, setCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [_, setToast] = useAtom(toastAtom);
  const [error, setError] = useState<string | null>(null);

  const findCode = async () => {
    if (code.length === 6) {
      const response = await requests.get<PublicCodeLookUpResponse>(
        `/license/public/code-lookup/${code}`,
      );
      return response;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (code.length === 6) {
      setLoading(true);
      try {
        const found = await findCode();
        console.log("Code details:", found);
        if(found?.entityId) {
          push(`/found/dog/${found.entityId}?realm=${found?.realm}`)
        } else {
          setError("Dog tag is not registered.");
        }
      } catch (error:any) {
        console.log(error);
        if (error.response?.status === 404) {
          setError("Code not found. Please try again.");
        } else if (error.response?.status === 401) {
          setError("Unauthorized access.");
        } else {
          setError("Error fetching code details. Please try again.");
        }
      }
      setLoading(false);
    } else {
      console.log("Code is not valid. It must be 6 characters.");
    }
  };

  return (
    <div className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white bg-opacity-10"
            style={{
              width: `${Math.random() * 10 + 5}px`,
              height: `${Math.random() * 10 + 5}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animation: `float ${Math.random() * 10 + 5}s linear infinite`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 w-full max-w-md rounded-2xl border border-white border-opacity-30 bg-white bg-opacity-90 p-8 shadow-2xl backdrop-blur-lg">
        <div className="relative mb-10 flex justify-center">
          <div className="bg-white-500 absolute inset-0 rounded-full opacity-30 blur-md filter"></div>
          <Image
            src="/logos/ClerkXpress.svg"
            alt="Company Logo"
            width={200}
            height={80}
            className="relative z-10 rounded-md"
          />
        </div>
        <h1 className="text-center text-3xl font-bold text-neutral-700">
          Enter Your Code:
        </h1>
        <form
          onSubmit={handleSubmit}
          className="flex flex-col items-center space-y-6"
        >
          <InputOTP
            maxLength={6}
            pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
            className="border-neutral-500"
            value={code}
            onChange={(value) => {
              setCode(value.toUpperCase());
              setError(null);
            }}
          >
            <InputOTPGroup className="border-neutral-500">
              {/* 6 slot loop */}
              {[...Array(6)].map((_, i) => (
                <InputOTPSlot
                  key={i}
                  index={i}
                  className="h-14 w-14 border-neutral-500 text-3xl"
                />
              ))}
            </InputOTPGroup>
          </InputOTP>

          {error && (
            <div className="mt-4 rounded-lg font-semibold text-lg p-4 text-red-500">
              {error}
            </div>
          )}

          <Button
            type="submit"
            disabled={code.length !== 6 || loading}
            className="flex w-full transform items-center justify-center space-x-2 rounded-lg bg-blue-600 px-4 py-3 font-bold text-white transition duration-200 ease-in-out hover:scale-105 hover:bg-blue-700 hover:shadow-lg"
          >
            <span className="flex items-center gap-2">
              {loading && <LuLoader className="size-5 animate-spin" />}
              {loading ? "Searching..." : "Search"}
            </span>
          </Button>
        </form>

        <p className="mt-6 text-center text-sm text-neutral-900">
          Enter the unique code to unlock what you&apos;re searching for.
        </p>
      </div>
    </div>
  );
}
