import { Individual } from "@/types/IndividualType";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  EditDialogFooter,
  createFormData,
} from "../../../../../../(resident)/(registered)/resident/[entityType]/[entityId]/components/modals/EditDialog";
import {
  useUpdateEntityProfile2,
  useUpdateResidentProfile,
} from "@/hooks/api/useProfiles";
import { useForm } from "react-hook-form";
import { FormValues } from "@/types/DogType";
import { use, useState } from "react";
import { useAtom } from "jotai";
import { toastAtom } from "@/components/ui/toast/toast";
import { useQueryClient } from "@tanstack/react-query";
import {
  Comment,
  IndividualDateOfBirth,
  IndividualFirstName,
  IndividualLastName,
  IndividualMiddleName,
} from "@/components/dialog/EditFields";
import { useParams } from "next/navigation";

const EditProfile = ({
  individual,
  admin = true,
}: {
  individual: Individual;
  admin?: boolean;
}) => {
  console.log(individual);

  const initialValues = {
    firstName: individual?.firstName || "",
    middleName: individual?.middleName || "",
    lastName: individual?.lastName || "",
    dateOfBirth: individual?.dateOfBirth || "",
  };

  const updateResidentProfile = useUpdateResidentProfile();
  const updateClerkProfile = useUpdateEntityProfile2();

  const updateProfile: any = admin ? updateClerkProfile : updateResidentProfile;

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<FormValues>({
    defaultValues: initialValues,
    mode: "onChange",
  });

  const [isOpen, setIsOpen] = useState(false);
  const [, setToast] = useAtom(toastAtom);
  const queryClient = useQueryClient();

  const handleCancel = () => {
    reset(initialValues);
    setIsOpen(false);
  };

  const handleSave = (data: any) => {
    const formData = createFormData(data);

    updateProfile.mutate(
      {
        entityId: individual.entityId,
        body: formData,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries();
          reset(data);
          setToast({
            status: "success",
            label: "Individual Updated",
            message: "Successfully Updated Individual Information",
          });
          setIsOpen(false);
        },
        onError: (error: any) => {
          console.log(error);
          setToast({
            status: "error",
            label: "Error Updating Profile",
            message: error?.response?.data?.message,
          });
        },
      },
    );
  };

  return (
    <EditDialog title="Edit Profile" isOpen={isOpen} setIsOpen={setIsOpen}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handleSave)}>
        <IndividualFirstName register={register} errors={errors} />
        <IndividualMiddleName register={register} errors={errors} />
        <IndividualLastName register={register} errors={errors} />
        <IndividualDateOfBirth register={register} errors={errors} />
        <Comment register={register} />
        <EditDialogFooter
          handleCancel={handleCancel}
          disabled={!isDirty}
          loading={updateProfile.isLoading}
        />
      </form>
    </EditDialog>
  );
};

export default EditProfile;
