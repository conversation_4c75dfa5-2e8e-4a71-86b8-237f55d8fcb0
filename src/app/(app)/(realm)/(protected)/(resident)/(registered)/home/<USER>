import ProtectedRoute from '@/components/ProtectedRoute/ProtectedRoute'
import { useMyPermissions } from '@/hooks/useMyPermissions'
import { useRouter } from 'next/navigation'
import React from 'react'

export default function ProtectedLayout({
  children
}:{
  children:  React.ReactNode
}) {
  // // ------------------------------------------
  // // Only on this page check for admin permissions for now.  Need a better solution when possible. -- Sean B - 8/1/2024
  // const { push } = useRouter()
  // const { hasPermissions } = useMyPermissions()
  // if(hasPermissions(["super-admin"])){
  //   push("/dashboard")
  // }
  // // ------------------------------------------

  const requiredPermissions = ["resident"]

  return (
    <ProtectedRoute
      permissions={requiredPermissions}
    >
      {children}
    </ProtectedRoute>
  )
}