import { AnimatePresence, motion } from "framer-motion";
import { useParams, usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import ProfileActions from "@/components/profile/ProfileActions";
import { FiChevronDown } from "react-icons/fi";
import { LuActivitySquare } from "react-icons/lu";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Title } from "@/components/profile/ProfileComponents";
import { Section } from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/profile/Dialogs";
import { License } from "@/types/LicenseType";
import { formatDate } from "@/components/license/licenseHelper";

const LicenseTypeMap: { [key: string]: string } = {
  dogLicense: "Dog License",
  purebredDogLicense: "Purebred Dog License",
};

export const LicenseContent = ({ license }: { license: License }) => {
  
  const pathname = usePathname();
  const router = useRouter();

  const containerVariants = {
    hidden: { opacity: 0, y: -50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 100 },
    },
    exit: { opacity: 0, y: 50, transition: { duration: 0.2 } },
  };

  console.log(license);

  const [isEventsOpen, setIsEventsOpen] = useState<boolean>(false);

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key="license"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className="bg-white shadow rounded p-6 w-full"
      >
        <div className="flex flex-col gap-4">
          <div className="flex gap-12">
            <div className="flex gap-4 grow">
              <Image
                src={"/images/icons/license.png"}
                alt="license"
                width={100}
                height={100}
                className="rounded h-fit"
              />
              <div className="flex flex-col">
                <h3 className="text-4xl font-medium text-neutral-800 -mb-1">
                  {license?.licenseNumber}
                </h3>
                <p className="text-neutral-700 mb-auto">
                  {LicenseTypeMap[license?.licenseType?.code ?? ""]}
                </p>
                {/* <ProfileActions
                  entityType="license"
                  entityId={license?.entityId}
                  className="mt-1 flex items-center justify-center w-fit gap-2 text-sm px-4 py-1 bg-clerk-background rounded hover:bg-clerk-background/80 text-white"
                >
                  Actions <FiChevronDown />
                </ProfileActions> */}
              </div>
            </div>
            <div className="grow flex gap-6 justify-end">
              <Sheet>
                <SheetTrigger
                  onClick={() => {
                    setIsEventsOpen(true);
                  }}
                >
                  <Button
                    variant="ghost"
                    className="flex flex-col items-center h-fit"
                    onClick={() => setIsEventsOpen(true)}
                  >
                    <LuActivitySquare className="text-3xl" />
                    <div className="text-neutral-600">History</div>
                  </Button>
                </SheetTrigger>
                {/* <DogHistory /> */}
              </Sheet>
            </div>
          </div>

          {/* Profile */}
          <div className="mt-10">
            <div className="flex flex-col xl:flex-row gap-10">
              <div className="flex flex-col gap-20 w-full">
                {/* License Dates */}
                <div>
                  <Title>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span>License Dates</span>
                      </div>
                    </div>
                  </Title>

                  <div className="flex flex-col gap-4 ">
                    <Section label="Issue Date:">
                      <div className="flex flex-col gap-2">
                        <div>{formatDate(license?.issuedDate)}</div>
                      </div>
                    </Section>
                    <Section label="Valid Till:">
                      <div className="flex flex-col gap-2">
                        <div>{formatDate(license?.validToDate)}</div>
                      </div>
                    </Section>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
