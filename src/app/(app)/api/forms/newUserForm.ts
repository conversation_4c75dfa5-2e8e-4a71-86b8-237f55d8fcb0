import type { NextApiRequest, NextApiResponse } from "next";

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const steps = [
    {
      type: "instructions",
      title: "Instructions",
      pageInstructions:
        "Please complete the following steps to register as a new user.",
      footnotes:
        'Attention: A valid ID must be uploaded to complete registration. If you wish to register without uploading a valid ID, please contact the office at 555-555-5555 or visit "http://www.example.com" for instructions on how to complete registration in person or by mail.',
      paragraphs: null,
      sections: null,
      optional: false,
      step: 1,
    },
    {
      type: "identification",
      title: "Documents",
      pageInstructions: "",
      paragraphs: null,
      footnotes: "Valid ID must be uploaded to complete registration.",
      sections: [
        {
          label: "Identification Upload",
          subheading: "",
          conditionals: [
            {
              label: "Drivers License",
              value: "driversLicense",
            },
            {
              label: "State ID",
              value: "stateID",
            },
            {
              label: "Passport",
              value: "passport",
            },
          ],
          inputs: [
            {
              label: "Front of ID",
              description: "Please upload the front of your ID.",
              fieldID: 1,
              fieldName: "idFront",
              inputType: "file",
              defaultValue: "",
              required: {
                value: true,
                message: "no more than 1 char",
              },
              min: {
                value: 1,
                message: "no more than 1 char",
              },
              max: {
                value: 1,
                message: "no more than 1 char",
              },
              pattern: null,
              info: null,
              conditionalDisplay: {
                fieldName: "idType",
                values: ["driversLicense", "stateID"],
              },
            },
            {
              label: "Back of ID",
              fieldName: "idBack",
              inputType: "file",
              defaultValue: "",
              size: "md",
              required: true,
              min: null,
              max: null,
              minLength: null,
              maxLength: null,
              pattern: null,
              validate: null,
              info: null,
              conditionalDisplay: {
                fieldName: "idType",
                values: ["driversLicense", "stateID"],
              },
            },
            {
              label: "Passport",
              fieldName: "passport",
              inputType: "file",
              defaultValue: "",
              required: true,
              min: null,
              max: null,
              minLength: null,
              maxLength: null,
              pattern: null,
              validate: null,
              info: null,
              conditionalDisplay: {
                fieldName: "idType",
                values: ["passport"],
              },
            },
          ],
        },
        {
          label: "Additional Supporting Documents",
          subheading: null,
          conditionals: null,
          inputs: [
            {
              label: "Utility Bill",
              fieldName: "utilityBill",
              inputType: "file",
              defaultValue: "",
              required: true,
              min: null,
              max: null,
              minLength: null,
              maxLength: null,
              pattern: null,
              validate: null,
              info: null,
            },
          ],
        },
      ],
      showIdentificationCard: true,
      optional: true,
      step: 2,
    },
    {
      type: "formWithDocView",
      title: "User Information",
      pageInstructions: null,
      footnotes: null,
      paragraphs: null,
      sections: [
        {
          label: "Personal Information",
          subheading: "",
          inputs: [
            {
              label: "First Name",
              fieldName: "firstName",
              inputType: "text",
              defaultValue: "",
              size: "md",
              required: true,
              min: null,
              max: null,
              pattern: null,
              info: null,
            },
            {
              label: "Last Name",
              fieldName: "lastName",
              inputType: "text",
              defaultValue: "",
              size: "md",
              required: true,
              min: null,
              max: null,
              pattern: null,
              info: null,
            },
            {
              label: "Organization/Business Name",
              fieldName: "businessName",
              inputType: "text",
              defaultValue: "",
              size: "md",
              required: false,
              min: null,
              max: null,
              pattern: null,
              info: null,
            },
            {
              label: "Email",
              fieldName: "email",
              inputType: "email",
              defaultValue: "",
              size: "md",
              required: true,
              min: null,
              max: null,
              pattern: null,
              info: null,
            },
            {
              label: "Phone",
              fieldName: "phone",
              inputType: "tel",
              defaultValue: "",
              size: "md",
              required: true,
              min: null,
              max: null,
              pattern: null,
              info: null,
            },
            {
              label: "Date of Birth",
              fieldName: "dateOfBirth",
              inputType: "date",
              defaultValue: "",
              size: "sm",
              required: true,
              min: null,
              max: null,
              pattern: null,
              info: null,
            },
          ],
        },
        {
          label: "Primary Residency",
          inputs: [
            {
              label: "Address",
              fieldName: "address",
              inputType: "text",
              defaultValue: "",
              size: "lg",
              google: true,
            },
            {
              label: "Address 2",
              fieldName: "address2",
              inputType: "text",
              defaultValue: "",
              size: "sm",
              google: true,
            },
            {
              label: "City",
              fieldName: "city",
              inputType: "text",
              defaultValue: "",
              size: "sm",
              google: true,
            },
            {
              label: "State",
              fieldName: "state",
              inputType: "text",
              defaultValue: "",
              size: "sm",
              google: true,
            },
            {
              label: "Zip Code",
              fieldName: "zip",
              inputType: "text",
              defaultValue: "",
              size: "sm",
              google: true,
            },
          ],
        },

        // Mailing Address Section
        // {
        //   label: "Mailing Residency",
        //   subheading: "If different from primary residency",
        //   conditionals: [
        //     {
        //       label: "Address",
        //       value: "address",
        //     },
        //     {
        //       label: "Address 2",
        //       value: "address2",
        //     },
        //     {
        //       label: "City",
        //       value: "city",
        //     },
        //     {
        //       label: "State",
        //       value: "state",
        //     },
        //     {
        //       label: "Zip Code",
        //       value: "zip",
        //     },
        //   ],
        //   inputs: [
        //     // Checkbox to indicate if mailing address is different from primary residency
        //     {
        //       label: "Mailing Residency is same as Primary Residency?",
        //       fieldName: "mailingResidencyIsSameAsPrimaryResidency",
        //       inputType: "checkbox",
        //       defaultValue: true,
        //       size: "lg",
        //       google: false,
        //     },
        //     {
        //       label: "Address",
        //       fieldName: "mailingAddress",
        //       inputType: "text",
        //       defaultValue: "",
        //       size: "lg",
        //       google: true,
        //       conditionalDisplay: {
        //         fieldName: "mailingResidencyIsSameAsPrimaryResidency",
        //         values: [false],
        //       },
        //     },
        //     {
        //       label: "Address 2",
        //       fieldName: "mailingAddress2",
        //       inputType: "text",
        //       defaultValue: "",
        //       size: "sm",
        //       google: true,
        //     },
        //     {
        //       label: "City",
        //       fieldName: "mailingCity",
        //       inputType: "text",
        //       defaultValue: "",
        //       size: "sm",
        //       google: true,
        //     },
        //     {
        //       label: "State",
        //       fieldName: "mailingState",
        //       inputType: "text",
        //       defaultValue: "",
        //       size: "sm",
        //       google: true,
        //     },
        //     {
        //       label: "Zip Code",
        //       fieldName: "mailingZip",
        //       inputType: "text",
        //       defaultValue: "",
        //       size: "sm",
        //       google: true,
        //     },
        //   ],
        // },
      ],
      optional: false,
      step: 3,
    },
    {
      type: "confirmation",
      title: "Confirm Information",
      pageInstructions: null,
      footnotes: null,
      paragraphs: null,
      sections: null,
      optional: true,
      step: 4,
    },
  ];

  res.status(200).json(steps);
}
