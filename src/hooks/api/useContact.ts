"use client";
import { useMutation } from "@tanstack/react-query";
import { requests } from "@/utils/agent";

// Get order information
export const useContactStaff = () => {
  return useMutation(
    ({
      issue,
      description
    }: {
      issue: string;
      description: string;
    }) => {
      return requests.post<any>(
        `/coordinator/me/contact-us`,
        {
          issue,
          description
        },
      );
    },
  );
};