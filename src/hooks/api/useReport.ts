import { requests } from "@/utils/agent";
import { useMutation, useQuery } from "@tanstack/react-query";

export type GenerateReport = {
  reportTypeId: string;
  selectedFormat: string;
  body: {
    [key: string]: string;
  };
};

type StandardOptions = {
  onSuccess?: (data: any, variables: any, context: any) => void;
  onError?: (error: any, variables: any, context: any) => void;
};

// Get all reports by section
export const useGetAllReportBySection = (sectionName: string) => {
  return useQuery({
    queryKey: ["getAllReportBySection", sectionName],
    queryFn: () => requests.get<any>(`/report/report/section/${sectionName}`),
  });
};

// Get a report
export const useGenerateReport = ({
  onSuccess,
  onError,
}: StandardOptions = {}) => {
  return useMutation(
    ({ reportTypeId, selectedFormat, body }: GenerateReport) =>
      requests.post<any>(`/report/report/generate/${reportTypeId}/${selectedFormat}`, body),
    {
      onSuccess,
      onError,
    }
  );
};

// Get a report - JSON
export const useGenerateReportJSON = ({
  onSuccess,
  onError,
}: StandardOptions = {}) => {
  return useMutation(
    ({ reportTypeId, body }: GenerateReport) =>
      requests.post<any>(`/report/report/generate/${reportTypeId}`, {
        ...body,
        outputType: "json",
      }),
    {
      onSuccess,
      onError,
    }
  );
};

// Get report by id
export const useGetReportById = (
  reportId: string | null,
  refetchInterval?: number
) => {
  return useQuery({
    queryKey: ["getReportById", reportId],
    queryFn: () => requests.get<any>(`/report/report/${reportId}`),
    enabled: !!reportId,
    refetchInterval: refetchInterval,
  });
};
