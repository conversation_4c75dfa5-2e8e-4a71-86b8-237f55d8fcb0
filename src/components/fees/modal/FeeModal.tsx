"use client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON>nt,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Dog } from "@/types/DogType";
import { Individual } from "@/types/IndividualType";
import { License } from "@/types/LicenseType";
import FeeList from "./FeeList";
import { FeeModalProvider, useFeeModalContext } from "./FeeModalContext";
import { X } from "lucide-react";
import GroupSelect from "./GroupSelect";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  entity: Dog | Individual | License;
  entityType: string;
  title: string;
  defaultGroup?: string;
  groupLock?: boolean;
}

export function FeeModal({
  isOpen,
  onClose,
  entity,
  entityType,
  defaultGroup,
  title = "Fees",
  groupLock = false,
}: ModalProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <FeeModalProvider
          entity={entity}
          entityType={entityType}
          defaultGroup={defaultGroup}
          isOpen={isOpen}
          onClose={onClose}
        >
          <FeeModalContent
            title={title}
            groupLock={groupLock}
          />
        </FeeModalProvider>
      </AlertDialogContent>
    </AlertDialog>
  );
}

function FeeModalContent({
  title,
  groupLock,
}: {
  title: string;
  groupLock: boolean;
}) {
  const { onClose, group } = useFeeModalContext();

  return (
    <>
      <AlertDialogHeader>
        <AlertDialogTitle className="flex items-center justify-between">
          {title}
          <button
            onClick={onClose}
            className="rounded-full p-1 hover:text-blue-700"
          >
            <X />
          </button>
        </AlertDialogTitle>
      </AlertDialogHeader>
      {!group ? (
        <GroupSelect />
      ) : (
        <FeeList groupLock={groupLock} />
      )}
    </>
  );
}
