import { IconType } from "react-icons";
import { FaHatWizard } from "react-icons/fa";
import { FiMail, FiFileText, FiSearch, FiArchive, FiSettings, FiGrid, FiHome, FiActivity, FiHelpCircle, FiSidebar, FiCircle } from "react-icons/fi";
import { LuShapes } from "react-icons/lu";
import { TbLicense, TbDog, TbReportAnalytics, TbUser } from "react-icons/tb";
import { BiCheckSquare, BiSupport, BiStore } from "react-icons/bi";
import { HiOutlineOfficeBuilding } from "react-icons/hi";
import { MdCallMerge } from "react-icons/md";
import { PiCertificateBold } from "react-icons/pi";
import { BsFiles } from "react-icons/bs";

export const sidebarIcons: { [key: string]: IconType } = {
  "FaHatWizard": FaHatWizard,
  "FiMail": FiMail,
  "FiFileText": FiFileText,
  "FiSearch": FiSearch,
  "FiArchive": FiArchive,
  "FiSettings": FiSettings,
  "FiGrid": FiGrid,
  "FiHome": FiHome,
  "FiActivity": FiActivity,
  "FiHelpCircle": FiHelpCircle,
  "LuShapes": LuShapes,
  "TbLicense": TbLicense,
  "TbDog": TbDog,
  "TbReportAnalytics": TbReportAnalytics,
  "BiCheckSquare": BiCheckSquare,
  "BiSupport": BiSupport,
  "BiStore": BiStore,
  "HiOutlineOfficeBuilding": HiOutlineOfficeBuilding,
  "MdCallMerge": MdCallMerge,
  "PiCertificateBold": PiCertificateBold,
  "BsFiles": BsFiles,
  "default": FiCircle,
  "FiSidebar": FiSidebar,
  "TbUser": TbUser
};
