{"id": "dogLicense-renewal", "pages": {"error": {"title": "Error Loading <PERSON>", "settings": ["error", "hideSidebar"], "paragraph": "An error occurred while processing your renewal. Please try again later.", "navigation": [{"type": "button", "label": "Try Again", "action": {"goToPage": "instructions"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "errorDocumentScanRabies": {"title": "Document <PERSON><PERSON> - Rabies Vaccine", "settings": ["error", "hideSidebar"], "paragraph": "An error occurred while scanning your rabies vaccination document. Please check the document and try again.", "navigation": [{"type": "button", "label": "Back to Upload", "action": {"goToPage": "renewalUploadDocuments"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "errorDocumentScanExemption": {"title": "Document <PERSON><PERSON> - Rabies Exemption", "settings": ["error", "hideSidebar"], "paragraph": "An error occurred while scanning your rabies exemption document. Please check the document and try again.", "navigation": [{"type": "button", "label": "Back to Upload", "action": {"goToPage": "renewalUploadDocuments"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "errorUpdateRabies": {"title": "Update Error - Rabies Information", "settings": ["error", "hideSidebar"], "paragraph": "An error occurred while updating your dog's rabies information. Please try again or contact support if the problem persists.", "navigation": [{"type": "button", "label": "Back to Upload", "action": {"goToPage": "renewalUploadDocuments"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "errorUpdateRabiesExemption": {"title": "Update Error - Rabies Exemption", "settings": ["error", "hideSidebar"], "paragraph": "An error occurred while updating your dog's rabies exemption information. Please try again.", "navigation": [{"type": "button", "label": "Back to Upload", "action": {"goToPage": "renewalUploadDocuments"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "errorStartRenewal": {"title": "Start Renewal Error", "settings": ["error", "hideSidebar"], "paragraph": "An error occurred while starting your renewal process. This might be due to license data or eligibility check issues.", "navigation": [{"type": "button", "label": "Try Again", "action": {"goToPage": "instructions"}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "instructions": {"title": "License Renewal", "content": "<p style=\"margin: 0px 0px 20px 0px;\">Welcome to the license renewal process. We'll guide you through renewing your dog license, including updating any required documentation.</p><br><p style=\"font-weight: bold;\">This application is for {{context:dog.dogName}}</p>", "disclaimer": "<p class=\"text-sm italic text-left mt-4\">*Registered dogs are required to have a rabies vaccination certificate. If your dog's rabies vaccination is expired, you will need to upload a new certificate.</p>", "settings": ["page", "hideSideBar"], "paragraph": "Please review the information and continue with your renewal.", "navigation": [{"type": "button", "label": "Start Renewal Process", "action": {"guard": {"field": "dog.licenseExempt", "value": true, "matchType": "boolean"}, "onSuccess": {"guard": {"field": "dog.dogSpayedOrNeutered", "value": "yes", "matchType": "exact"}, "onSuccess": {"guard": {"field": "licenseFeesCalculation.reasoncodes", "value": "vaccineExpired", "matchType": "contains"}, "onSuccess": {"goToPage": "renewalUploadDocuments"}, "onError": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "goToPage": "licenseDurationInformation"}}}, "onError": {"goToPage": "spayNeuterUpdate"}}, "onError": {"goToPage": "serviceAnimalCheck"}}, "variant": "primary"}, {"type": "link", "label": "Cancel", "variant": "ghost", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "renewalNotEligible": {"title": "Renewal Not Available", "paragraph": "This license is not eligible for renewal at this time. This could be due to pending payments, approvals, or other requirements.", "navigation": [{"type": "link", "label": "Back to License", "variant": "primary", "navigate": {"url": "/a/license/{{context:entityId}}"}}]}, "renewalUploadDocuments": {"title": "Update Rabies Vaccination Information", "paragraph": "Your dog's rabies vaccination will expire soon. You must update your rabies information to complete the renewal.", "fields": [{"id": "dog.vaccineDatesExempt", "type": "checkbox", "label": "My dog is exempt from rabies vaccination", "required": false}, {"id": "dog.dogRabiesVaccinationDocument", "type": "file", "label": "Rabies Vaccination Certificate", "required": true, "acceptedFileType": ["pdf", "jpg", "jpeg", "png"], "displayConditions": {"conditions": [{"field": "dog.vaccineDatesExempt", "value": false}]}}, {"id": "dog.dogRabiesVaccinationExemptionDocument", "type": "file", "label": "Rabies Exemption Certificate", "required": false, "acceptedFileType": ["pdf", "jpg", "jpeg", "png"], "displayConditions": {"conditions": [{"field": "dog.vaccineDatesExempt", "value": true}]}}], "navigation": [{"type": "button", "label": "Continue", "variant": "primary", "action": {"guard": {"field": "dog.vaccineDatesExempt", "value": true, "matchType": "boolean"}, "onSuccess": {"guard": {"field": "dog.dogRabiesVaccinationExemptionDocument", "value": true, "matchType": "notNull"}, "onSuccess": {"callApi": "documentScanExemption", "onError": {"callApi": "updateDogRabiesExemption", "onError": {"goToPage": "errorUpdateRabiesExemption"}, "onSuccess": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "goToPage": "licenseDurationInformation"}}}, "onLoading": {"modal": true, "title": "Scanning Document", "message": "Extracting exemption information..."}, "onSuccess": {"assignEvent": {"dog.vaccineName": "Rabies", "dog.veterinaryName": "{{event:<PERSON><PERSON>ame}}", "dog.vaccineDatesExempt": "{{event:vaccineDatesExempt}}"}, "callApi": "updateDogRabiesExemption", "onError": {"goToPage": "errorUpdateRabiesExemption"}, "onSuccess": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "goToPage": "licenseDurationInformation"}}}}, "onError": {"goToPage": "error"}}, "onError": {"guard": {"field": "dog.dogRabiesVaccinationDocument", "value": true, "matchType": "notNull"}, "onSuccess": {"callApi": "documentScanRabies", "onError": {"goToPage": "reviewRabiesInformation"}, "onLoading": {"modal": true, "title": "Scanning Document", "message": "Extracting updated vaccine information..."}, "onSuccess": {"assignEvent": {"dog.veterinaryName": "{{event:<PERSON><PERSON>ame}}", "dog.vaccineName": "{{event:vaccineName}}", "dog.vaccineDueDate": "{{event:vaccineDueDate}}"}, "goToPage": "reviewRabiesInformation"}}, "onError": {"goToPage": "error"}}}}, {"type": "button", "label": "Back", "variant": "ghost", "action": {"guard": {"field": "dog.licenseExempt", "value": true, "matchType": "boolean"}, "onSuccess": {"guard": {"field": "dog.dogSpayedOrNeutered", "value": "yes", "matchType": "exact"}, "onSuccess": {"goToPage": "instructions"}, "onError": {"goToPage": "spayNeuterUpdate"}}, "onError": {"guard": {"field": "dog.dogSpayedOrNeutered", "value": "yes", "matchType": "exact"}, "onSuccess": {"goToPage": "serviceAnimalCheck"}, "onError": {"goToPage": "spayNeuterUpdate"}}}, "operation": "back"}]}, "reviewRabiesInformation": {"title": "Review Rabies Vaccination Information", "paragraph": "Please review the information extracted from your rabies vaccination document. You can make any necessary corrections before saving.", "fields": [{"id": "dog.veterinaryName", "type": "text", "label": "Veterinary Name", "required": true, "placeholder": "Enter veterinary name"}, {"id": "dog.vaccineName", "type": "select", "label": "Vaccine Name", "required": true, "placeholder": "Select vaccine name", "options": [{"label": "Rabies", "value": "Rabies"}], "validate": [{"type": "oneOf", "values": ["Rabies"], "message": "Please select a valid vaccine name from the dropdown."}]}, {"id": "dog.vaccineDueDate", "type": "date", "label": "Vaccine Expiration Date", "required": true, "validate": [{"type": "future<PERSON>in", "unit": "months", "value": 1, "message": "Vaccine expiration date must have at least 1 month of validity."}]}], "navigation": [{"type": "button", "label": "Save & Continue", "variant": "primary", "action": {"callApi": "updateDogRabies", "onError": {"goToPage": "errorUpdateRabies"}, "onSuccess": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "goToPage": "licenseDurationInformation"}}}}, {"type": "button", "label": "Back to Upload", "variant": "ghost", "action": {"goToPage": "renewalUploadDocuments"}, "operation": "back"}]}, "spayNeuterUpdate": {"title": "Spay/Neuter Status Update", "paragraph": "Has your dog been spayed or neutered since the last renewal?", "fields": [{"id": "dog.recentlySpayed<PERSON>r<PERSON><PERSON><PERSON>", "type": "select", "label": "Recently Spayed or Neutered?", "options": [{"label": "Yes - Recently spayed/neutered", "value": "yes"}, {"label": "No - Still not spayed/neutered", "value": "no"}], "required": true, "placeholder": "Select status"}, {"id": "dog.spayNeuterDocument", "type": "file", "label": "Spay/Neuter Certificate", "required": true, "acceptedFileType": ["pdf", "jpg", "jpeg", "png"], "displayConditions": {"conditions": [{"field": "dog.recentlySpayed<PERSON>r<PERSON><PERSON><PERSON>", "value": "yes"}]}}], "navigation": [{"type": "button", "label": "Continue", "variant": "primary", "action": {"guard": {"field": "dog.recentlySpayed<PERSON>r<PERSON><PERSON><PERSON>", "value": "yes", "matchType": "exact"}, "onSuccess": {"callApi": "updateDogSpayNeuter", "onError": {"goToPage": "error"}, "onSuccess": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "guard": {"field": "licenseFeesCalculation.reasoncodes", "value": "vaccineExpired", "matchType": "contains"}, "onSuccess": {"goToPage": "renewalUploadDocuments"}, "onError": {"goToPage": "licenseDurationInformation"}}}}, "onError": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "guard": {"field": "licenseFeesCalculation.reasoncodes", "value": "vaccineExpired", "matchType": "contains"}, "onSuccess": {"goToPage": "renewalUploadDocuments"}, "onError": {"goToPage": "licenseDurationInformation"}}}}}, {"type": "button", "label": "Back", "variant": "ghost", "action": {"guard": {"field": "dog.licenseExempt", "value": true, "matchType": "boolean"}, "onSuccess": {"goToPage": "instructions"}, "onError": {"goToPage": "serviceAnimalCheck"}}, "operation": "back"}]}, "serviceAnimalCheck": {"title": "Service Animal Status", "paragraph": "Is your dog a service animal that is exempt from licensing fees?", "fields": [{"id": "dog.licenseExempt", "type": "checkbox", "label": "My dog is a service animal exempt from licensing fees", "required": false}, {"id": "dog.serviceAnimalDocument", "type": "file", "label": "Service Animal Certification", "required": true, "acceptedFileType": ["pdf", "jpg", "jpeg", "png"], "displayConditions": {"conditions": [{"field": "dog.licenseExempt", "value": true}]}}], "navigation": [{"type": "button", "label": "Continue", "variant": "primary", "action": {"guard": {"field": "dog.licenseExempt", "value": true, "matchType": "boolean"}, "onSuccess": {"callApi": "updateDogServiceAnimal", "onError": {"goToPage": "error"}, "onSuccess": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "guard": {"field": "dog.dogSpayedOrNeutered", "value": "yes", "matchType": "exact"}, "onSuccess": {"guard": {"field": "licenseFeesCalculation.reasoncodes", "value": "vaccineExpired", "matchType": "contains"}, "onSuccess": {"goToPage": "renewalUploadDocuments"}, "onError": {"goToPage": "licenseDurationInformation"}}, "onError": {"goToPage": "spayNeuterUpdate"}}}}, "onError": {"callApi": "getLicenseFeesCalculation", "onError": {"goToPage": "error"}, "onSuccess": {"assignEvent": {"licenseFeesCalculation.items": "{{event:items}}", "licenseFeesCalculation.duration": "{{event:duration}}", "licenseFeesCalculation.isrenewable": "{{event:isrenewable}}", "licenseFeesCalculation.reasoncodes": "{{event:reasoncodes}}", "licenseFeesCalculation.message": "{{event:message}}"}, "guard": {"field": "dog.dogSpayedOrNeutered", "value": "yes", "matchType": "exact"}, "onSuccess": {"guard": {"field": "licenseFeesCalculation.reasoncodes", "value": "vaccineExpired", "matchType": "contains"}, "onSuccess": {"goToPage": "renewalUploadDocuments"}, "onError": {"goToPage": "licenseDurationInformation"}}, "onError": {"goToPage": "spayNeuterUpdate"}}}}}, {"type": "button", "label": "Back", "variant": "ghost", "action": {"goToPage": "instructions"}, "operation": "back"}]}, "licenseDurationInformation": {"title": "Select Renewal Term", "paragraph": "Choose your renewal term:", "fields": [{"id": "licenseFeesCalculation.items", "data": "{{context:licenseFeesCalculation.items}}", "type": "table", "label": "License Pricing", "sortBy": {"order": "asc", "column": "total"}, "columns": [{"id": "label", "type": "text", "label": "License Duration"}, {"id": "total", "type": "currency", "label": "Price", "currencySymbol": "$"}], "required": false, "paragraph": "Below are the prices for each license duration:", "allowSearch": false, "allowSorting": false, "allowPagination": false}, {"id": "license.endYear", "type": "select", "label": "License Duration", "sortBy": {"order": "asc", "column": "total"}, "options": "{{context:licenseFeesCalculation.items}}", "required": true, "placeholder": "Select a Duration", "optionValueMap": {"label": "label", "value": "endYear"}}], "navigation": [{"type": "button", "label": "Next", "variant": "primary", "action": {"guard": {"field": "license.endYear", "value": true, "matchType": "notNull"}, "onSuccess": {"goToPage": "confirmationLicenseDuration"}}}, {"type": "button", "label": "Back", "variant": "ghost", "action": {"guard": {"field": "licenseFeesCalculation.reasoncodes", "value": "vaccineExpired", "matchType": "contains"}, "onSuccess": {"goToPage": "renewalUploadDocuments"}, "onError": {"guard": {"field": "dog.dogSpayedOrNeutered", "value": "yes", "matchType": "exact"}, "onSuccess": {"guard": {"field": "dog.licenseExempt", "value": true, "matchType": "boolean"}, "onSuccess": {"goToPage": "instructions"}, "onError": {"goToPage": "serviceAnimalCheck"}}, "onError": {"goToPage": "spayNeuterUpdate"}}}, "operation": "back"}]}, "confirmationLicenseDuration": {"title": "Confirm <PERSON>wal", "paragraph": "Please confirm your renewal selections:", "fields": [{"id": "renewalSummary", "type": "confirmationGroup", "label": "<PERSON><PERSON>", "value": ""}, {"id": "license.endYear", "type": "select", "label": "Years", "value": "{license.endYear}", "options": "{{context:licenseFeesCalculation.items}}", "disabled": true, "required": true, "optionValueMap": {"label": "label", "value": "endYear"}}], "navigation": [{"type": "button", "label": "Confirm & Renew", "variant": "primary", "action": {"callApi": "renewLicense", "onError": {"goToPage": "error"}, "onSuccess": {"callApi": "addToCart", "onError": {"goToPage": "success"}, "onSuccess": {"goToPage": "success"}}}}, {"type": "button", "label": "Back", "variant": "ghost", "action": {"goToPage": "licenseDurationInformation"}, "operation": "back"}]}, "success": {"title": "Renewal Successful", "settings": ["hideSidebar"], "paragraph": "The renewal has been added to your cart. Please proceed to checkout.", "navigation": [{"type": "link", "label": "Go to Cart", "variant": "primary", "navigate": {"url": "/cart"}}]}}, "context": {"license": {"endYear": null}, "error": null}, "sidebar": [{"label": "Instructions", "order": 1, "pages": ["instructions"]}, {"label": "Service Animal", "order": 2, "pages": ["serviceAnimalCheck"]}, {"label": "Spay/Neuter Update", "order": 3, "pages": ["spayNeuterUpdate"]}, {"label": "Rabies Update", "order": 4, "pages": ["renewalUploadDocuments", "reviewRabiesInformation"]}, {"label": "Duration", "order": 5, "pages": ["licenseDurationInformation"]}, {"label": "Confirmation", "order": 6, "pages": ["confirmationLicenseDuration"]}], "formName": "Dog License Renewal Form", "functions": {"documentScanRabies": {"url": "/coordinator/me/textextractor/merge", "type": "rest", "method": "POST", "format": "formData", "description": "Scan and extract updated vaccine details", "body": {"schema": {"vaccineName": "string", "vaccineDueDate": "date", "veterinaryName": "string"}, "dogRabiesVaccinationDocument": "{{context:dog.dogRabiesVaccinationDocument}}"}}, "documentScanExemption": {"url": "/coordinator/me/textextractor/merge", "type": "rest", "method": "POST", "format": "formData", "description": "Scan and extract updated exemption details", "body": {"schema": {"veterinaryName": "string", "vaccineDatesExempt": "boolean"}, "dogRabiesVaccinationExemptionDocument": "{{context:dog.dogRabiesVaccinationExemptionDocument}}"}}, "updateDogRabies": {"url": "/license/me/participant/{{context:dog.entityId}}", "type": "rest", "method": "PATCH", "format": "formData", "description": "Update dog rabies information", "body": {"vaccineName": "Rabies", "vaccineDueDate": "{{context:dog.vaccineDueDate}}", "veterinaryName": "{{context:dog.veterinaryName}}", "dogRabiesVaccinationDocument": "{{context:dog.dogRabiesVaccinationDocument}}"}}, "updateDogRabiesExemption": {"url": "/license/me/participant/{{context:dog.entityId}}/rabies-exemption", "type": "rest", "method": "PATCH", "format": "formData", "description": "Update dog rabies exemption information", "body": {"veterinaryName": "{{context:dog.veterinaryName}}", "vaccineDatesExempt": "yes", "dogRabiesVaccinationExemptionDocument": "{{context:dog.dogRabiesVaccinationExemptionDocument}}"}}, "renewLicense": {"url": "/license/me/license/{{context:entityId}}/renew?endYear={{context:license.endYear}}", "type": "rest", "method": "POST", "format": "json", "body": {}}, "addToCart": {"type": "function", "function": "addToCart", "parameters": {"entityId": "{{context:entityId}}", "entityType": "license"}, "description": "Add renewed license to cart"}, "updateDogSpayNeuter": {"url": "/license/me/participant/{{context:dog.entityId}}", "type": "rest", "method": "PATCH", "format": "formData", "description": "Update dog spay/neuter status", "body": {"dogSpayedOrNeutered": "yes", "dogSpayedOrNeuteredDocument": "{{context:dog.spayNeuterDocument}}"}}, "updateDogServiceAnimal": {"url": "/license/me/participant/{{context:dog.entityId}}", "type": "rest", "method": "PATCH", "format": "formData", "description": "Update dog service animal status", "body": {"licenseExempt": "yes", "dogServiceAnimalExemptionDocument": "{{context:dog.serviceAnimalDocument}}"}}, "getLicenseFeesCalculation": {"url": "/license/me/license/{{context:entityId}}/fees/calculate", "type": "rest", "method": "GET", "format": "json", "description": "Get license fees calculation"}}, "initialPage": "instructions", "permissions": ["resident"]}