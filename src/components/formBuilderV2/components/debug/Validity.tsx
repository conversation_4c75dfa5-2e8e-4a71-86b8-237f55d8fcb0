import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useFormContext } from "../../providers/FormProvider";

const Validity = () => {
  const { validity } = useFormContext();

  return (
    <AccordionItem value="validity">
      <AccordionTrigger>
        <h3
          className={`text-left ${Object.keys(validity).length > 0 ? "text-red-500" : ""}`}
        >
          Validity: {Object.keys(validity).length}
          {Object.keys(validity).length ? " Invalid" : " Valid"}
        </h3>
      </AccordionTrigger>
      <AccordionContent>
        <div>
          {Object.keys(validity).map((key: string) => (
            <div key={key}>
              <div>{`${key}: ${
                typeof validity[key] === "string"
                  ? validity[key]
                  : JSON.stringify(validity[key])
              }`}</div>
            </div>
          ))}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default Validity;