import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useMachineContext } from "../../providers/MachineProvider";
import InputLabel from "./InputLabel";

type ColumnType = "text" | "currency" | "date";

interface TableColumn {
  id: string;
  type: ColumnType;
  label: string;
  currencySymbol?: string;
}

interface SortBy {
  column: string;
  order: "asc" | "desc";
}

interface TableInput {
  id: string;
  data: string;
  type: "table";
  label: string;
  columns: TableColumn[];
  required: boolean;
  paragraph: string;
  allowSearch: boolean;
  allowSorting: boolean;
  allowPagination: boolean;
  sortBy?: SortBy;
}

export default function TableComponent({
  input,
}: {
  input: TableInput;
}) {
  const { getContextValue } = useMachineContext();
  let tableData = getContextValue(input.id) || [];

  // Apply initial sorting if `sortBy` is defined
  if (input.sortBy) {
    const { column, order } = input.sortBy;
    tableData = [...tableData].sort((a: any, b: any) => {
      if (a[column] < b[column]) return order === "asc" ? -1 : 1;
      if (a[column] > b[column]) return order === "asc" ? 1 : -1;
      return 0;
    });
  }

  return (
    <div className="w-full">
      <div className="flex flex-col gap-1">
        <InputLabel label={input.label} id={input.id} />
        <Table className="w-full border border-slate-200">
          <TableHeader>
            <TableRow>
              {input.columns.map((column: any) => (
                <TableHead key={column.id} className="px-4 py-2 bg-gray-100">
                  {column.label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {tableData.length > 0 ? (
              tableData.map((row: any, rowIndex: number) => (
                <TableRow key={rowIndex}>
                  {input.columns.map((column: any) => (
                    <TableCell key={column.id} className="px-4 py-2">
                      {renderTableCell(row[column.id], column.type)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={input.columns.length} className="text-center p-4">
                  No data available.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

function renderTableCell(value: any, type: string) {
  switch (type) {
    case "currency":
      return `$${Number(value).toFixed(2)}`;
    case "date":
      return formatDate(value);
    default:
      return value;
  }
}

function formatDate(dateString: string) {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "Invalid Date";
  return date.toLocaleDateString("en-US", { month: "long", day: "numeric", year: "numeric" });
}
