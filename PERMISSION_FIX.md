# Permission Fix for 403 Forbidden Error

## Problem
You were getting a **403 Forbidden** error when calling `POST /api/records` because the `RecordController` didn't have `@RolesAllowed` annotations. Spring Security was denying access by default.

## What Was Fixed

### 1. Added New Permissions (Permissions.java)
Created a new `Permissions.Record` class with the following permissions:
```java
public static class Record {
    public static final String CREATE_RECORD = "RECORD:CREATE";
    public static final String GET_RECORD_BY_UUID = "RECORD:GET_BY_UUID";
    public static final String UPDATE_RECORD = "RECORD:UPDATE";
    public static final String DELETE_RECORD = "RECORD:DELETE";
    public static final String GET_ALL_RECORDS = "RECORD:GET_ALL";
    public static final String GET_RECORD_BY_TYPE = "RECORD:GET_BY_TYPE";
    public static final String GET_RECORD_BY_STATUS = "RECORD:GET_BY_STATUS";
    public static final String SEARCH_RECORDS = "RECORD:SEARCH";
    public static final String MANAGE_FEES = "RECORD:MANAGE_FEES";
}
```

### 2. Added @RolesAllowed Annotations
Updated three controllers:
- **RecordController** - All methods now have @RolesAllowed annotations
- **SearchController** - Global and advanced search endpoints
- **ProfileController** - Profile retrieval endpoints

### 3. Files Modified
- `record-application/src/main/java/com/scube/record/permission/Permissions.java`
- `record-application/src/main/java/com/scube/record/features/record/controller/RecordController.java`
- `record-application/src/main/java/com/scube/record/features/search/controller/SearchController.java`
- `record-application/src/main/java/com/scube/record/features/profile/controller/ProfileController.java`

## Next Steps to Make It Work

### Option 1: Add Roles to Keycloak (Recommended for Production)

1. **Log into Keycloak Admin Console:**
   ```
   http://localhost:8080
   ```

2. **Navigate to your realm** (e.g., `clerkXpress`)

3. **Create Client Roles** or **Realm Roles:**
   - Go to **Roles** → **Create Role**
   - Add the following roles:
     ```
     RECORD:CREATE
     RECORD:GET_BY_UUID
     RECORD:UPDATE
     RECORD:DELETE
     RECORD:GET_ALL
     RECORD:GET_BY_TYPE
     RECORD:GET_BY_STATUS
     RECORD:SEARCH
     RECORD:MANAGE_FEES
     ```

4. **Assign Roles to Users:**
   - Go to **Users** → Select your user → **Role Mappings**
   - Assign the necessary roles to your user

5. **Restart the Record Service:**
   ```bash
   # Find the process
   jps -l | grep record-application

   # Kill it
   kill <PID>

   # Restart (from Service_Record directory)
   java -jar record-application/target/record-application-0.0.1-SNAPSHOT.jar \
     --spring.profiles.active=local
   ```

6. **Get a new token** with the updated roles

7. **Test the endpoint again**

### Option 2: Temporarily Permit All (For Testing Only)

**WARNING: This bypasses security - use only for local testing!**

Edit `record-application/src/main/resources/application-local.yaml`:

```yaml
com.c4-soft.springaddons.oidc:
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/public/**"
      - "/actuator/**"
      - "/api/records/**"        # ADD THIS LINE
      - "/api/v1/search/**"      # ADD THIS LINE
      - "/api/v1/profiles/**"    # ADD THIS LINE
```

Then restart the service.

### Option 3: Use Swagger UI with Authentication

1. **Open Swagger UI:**
   ```
   http://localhost:9013/api/record/swagger-ui/index.html
   ```

2. **Click "Authorize"** button

3. **Configure OAuth2:**
   - Client ID: Your Keycloak client ID
   - Client Secret: Your Keycloak client secret (if required)
   - Scopes: Select appropriate scopes

4. **Authenticate** and get a token automatically

5. **Test endpoints** directly from Swagger UI

## Build Status
✅ **Compiled successfully** - Ready to restart service

## Required Permissions by Endpoint

### Record Management
- `POST /api/records` → Requires: `RECORD:CREATE`
- `GET /api/records` → Requires: `RECORD:GET_ALL`
- `GET /api/records/{uuid}` → Requires: `RECORD:GET_BY_UUID`
- `PUT /api/records/{uuid}` → Requires: `RECORD:UPDATE`
- `DELETE /api/records/{uuid}` → Requires: `RECORD:DELETE`

### Search
- `GET /api/v1/search/global` → Requires: `RECORD:SEARCH`
- `GET /api/v1/search/advanced` → Requires: `RECORD:SEARCH`

### Profiles
- `GET /api/v1/profiles/{uuid}` → Requires: `RECORD:GET_BY_UUID`
- `GET /api/v1/profiles/detail/{uuid}` → Requires: `RECORD:GET_BY_UUID`

### Fee Management
- All `/api/records/fees/*` endpoints → Require: `RECORD:MANAGE_FEES`

## How to Restart the Service

```bash
# 1. Navigate to Service_Record directory
cd /Users/<USER>/Documents/clerkxpress/Service_Record

# 2. Find current running process
jps -l | grep record

# 3. Kill the process
kill <process-id>

# 4. Start the service with local profile
java -jar record-application/target/record-application-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=local

# Or rebuild and run
mvn clean install -DskipTests && \
java -jar record-application/target/record-application-0.0.1-SNAPSHOT.jar \
  --spring.profiles.active=local
```

## Testing After Restart

```bash
# Get your auth token first
TOKEN="your-jwt-token-here"

# Test create record
curl -X POST "http://localhost:9013/api/record/api/records" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "recordTypeCode": "INDIVIDUAL",
    "recordName": "Test User",
    "status": "ACTIVE",
    "properties": {
      "firstName": "Test",
      "lastName": "User"
    },
    "createdBy": "<EMAIL>",
    "associations": []
  }'
```

## Summary

**Problem:** 403 Forbidden (no permissions defined)
**Solution:** Added @RolesAllowed annotations with new permission constants
**Action Required:**
1. Add roles to Keycloak OR temporarily permit-all for testing
2. Restart the service
3. Test with proper authentication

The service is now **compiled and ready to restart** with proper permission checks in place!
