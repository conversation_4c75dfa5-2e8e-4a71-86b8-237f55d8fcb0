update config.sql_storage
set sql_query= $$
	SELECT 
		o.order_id,
		TO_CHAR(p.transaction_date, 'MM/DD/YYYY') as "paidDate",
		p.payment_type as "paymentType",
		p.payment_number as "paymentNumber",
		COALESCE(p.payment_reference, '') as misc,
		CONCAT(
			payee.first_name, 
			' ', 
			payee.last_name
		) AS "paidBy", 
		oi.name,
		COALESCE(oi.description, '') as description,
		COALESCE(l.license_number, '') AS "licenseNumber",  
		replace( cast( jsonb_path_query(par.properties, '$.tagNumber') as text ), '"', '' ) as tag,
		cast( replace( cast( jsonb_path_query(par.properties, '$.licenseExempt') as text ), '"', '' ) as boolean ) as is_service,
		p.created_by AS "processedBy",
		1 AS quantity,
		fees.is_exempt AS "exempt",
		fees.has_senior_discount AS "seniorDiscount",
		fees.local_fees::NUMERIC(20,2)::TEXT AS "localFees",
		fees.state_fees::NUMERIC(20,2)::TEXT AS "stateFees",
		fees.total_fees::NUMERIC(20,2)::TEXT AS "totalFees",
		COALESCE(
			CASE WHEN oi.name LIKE 'Purebred%' THEN
				CASE WHEN activities.activity_count > 0 THEN
					CASE
						WHEN activities.activity_type = 'NEW' THEN 'Purebred '
						ELSE 'Purebred '
					END 
				ELSE
					'Purebred '
				END
			WHEN oi.name LIKE 'Dog License%' THEN
				CASE WHEN activities.activity_count > 0 THEN
					CASE
						WHEN activities.activity_type = 'NEW' THEN 'Original '
						ELSE 'Renewal '
					END 
				ELSE
					'Original '
				END
			ELSE
				COALESCE(oi.name, '') || ' ' || COALESCE(oi.description, '')
			END
			|| CASE WHEN (oi.name LIKE 'Purebred%' OR oi.name LIKE 'Dog License%') THEN
					CASE WHEN fees.is_altered 
					THEN 'Altered ' 
					ELSE 'Unaltered ' 
					END
				ELSE ''
				END
		,'')
		AS "feeName",
		CASE WHEN activities.activity_count > 0 THEN
			activities.activity_count || ' years'
		ELSE '' END AS "years"
	FROM calculation.order_ o
	INNER JOIN calculation.order_item oi 
		ON o.order_id = oi.order_id
		AND o.status = 'ORDER_PAID'
		AND DATE(timezone( 'America/New_York', o.order_paid_date + interval '3' hour)) >= DATE(:startDate)
		AND DATE(timezone( 'America/New_York', o.order_paid_date + interval '3' hour)) <= DATE(:endDate)
	LEFT JOIN LATERAL calculation.get_dog_license_fees_by_order_item_id(oi.order_item_id) AS fees
		ON true
	INNER JOIN payment.payment p 
		ON p.order_id = o.order_id
		AND (p.is_online_transaction = true)
	INNER JOIN payment.payee payee
		ON p.payee_id = payee.payee_id
	LEFT JOIN license.license l 
		ON l.license_uuid = oi.unique_item_id
	LEFT JOIN license.association a on l.license_id = a.child_id and a.child_association_type = 'LICENSE'
	LEFT JOIN license.participant par on a.parent_id = par.participant_id and a.parent_association_type = 'PARTICIPANT' and par.participant_type_group_id = 7
	LEFT JOIN LATERAL (
		SELECT
			COUNT(*) AS activity_count,
			MIN(CASE 
			WHEN la.activity_type = 'NEW' THEN 'NEW'
			ELSE 'RENEWAL' END) AS activity_type,
			la.license_id
		FROM license.license_activity la
		inner join license.license_activity_fee laf
			on laf.order_id = o.order_id
			and laf.license_activity_id = la.license_activity_id
		group by la.license_id
	) AS activities ON l.license_id = activities.license_id
	ORDER BY p.transaction_date;
$$
where name = 'DailyCashReportOnlinePayments';