update config.sql_storage
set sql_query = $$
WITH possible_titles AS (
    SELECT 'New, Female Spayed' AS title, 0 as sort_order, 8 as cost
    UNION ALL SELECT 'New, Male Neutered', 1, 8
    UNION ALL SELECT 'New, Female Unaltered', 2, 19
    UNION ALL SELECT 'New, Male Unaltered', 3, 19
	UNION ALL SELECT 'New, Service Dog Female', 4, 0
	UNION ALL SELECT 'New, Service Dog Male', 5, 0
	UNION ALL SELECT 'Renewal, Female Spayed', 6, 8
	UNION ALL SELECT 'Renewal, Male Neutered', 7, 8
    UNION ALL SELECT 'Renewal, Female Unaltered', 8, 19
    UNION ALL SELECT 'Renewal, Male Unaltered', 9, 19
	UNION ALL SELECT 'Renewal, Service Dog Female', 10, 0
	UNION ALL SELECT 'Renewal, Service Dog Male', 11, 0
    UNION ALL SELECT 'Replacement Tags', 12, 0.5
), gender_info as (
	select l.license_id, replace( cast( jsonb_path_query(p.properties, '$.dogSex') as text ), '"', '' ) as dog_gender
	from license.participant p
	inner join license.association a on p.participant_id = a.parent_id and a.parent_association_type = 'PARTICIPANT'
	inner join license.license l on a.child_association_type = 'LICENSE' and a.child_id = l.license_id
	where p.participant_type_group_id = 7
), fee_info as (
	SELECT laf.license_activity_id, laf.fee_code, 0 as amount, laf.license_type_name, laf.activity_type, laf.is_altered, laf.is_unaltered, true as is_exempt
	FROM calculation.order_ o
	INNER JOIN license.view_license_activity_fee_detail laf ON laf.order_id = o.order_id
	WHERE o.status ILIKE 'ORDER_PAID'
	AND laf.license_type_name ILIKE 'Dog License%'
	AND DATE(timezone( 'America/New_York', o.order_paid_date + interval '3' hour )) BETWEEN :startDate::date AND :endDate::date
	AND laf.fee_code ~~* '%-EXEMPT%'
	UNION ALL
	SELECT laf.license_activity_id, laf.fee_code, laf.amount, laf.license_type_name, laf.activity_type, laf.is_altered, laf.is_unaltered, false as is_exempt
	FROM calculation.order_ o
	INNER JOIN license.view_license_activity_fee_detail laf ON laf.order_id = o.order_id
	WHERE o.status ILIKE 'ORDER_PAID'
	AND laf.license_type_name ILIKE 'Dog License%'
	AND DATE(timezone( 'America/New_York', o.order_paid_date + interval '3' hour )) BETWEEN :startDate::date AND :endDate::date
	AND NOT laf.fee_code ~~* '%-EXEMPT%'
	AND NOT exists (
		select 1 
		from license.view_license_activity_fee_detail laf2 
		where laf2.order_id = laf.order_id 
		and laf2.license_activity_id = laf.license_activity_id 
		AND laf2.fee_code ~~* '%-EXEMPT%'
		and laf2.amount = laf.amount * -1
	)
),
license_activity_fees AS (
	SELECT
        laf.license_activity_id AS entity_id,
		CASE 
            WHEN laf.activity_type = 'NEW' AND ( laf.is_altered OR laf.fee_code ~~* '%-ALT%' ) AND g.dog_gender = 'Female' AND NOT laf.is_exempt THEN 'New, Female Spayed'
			WHEN laf.activity_type = 'NEW' AND ( laf.is_altered OR laf.fee_code ~~* '%-ALT%' ) AND g.dog_gender = 'Male' AND NOT laf.is_exempt THEN 'New, Male Neutered'
			WHEN laf.activity_type = 'NEW' AND ( laf.is_unaltered OR laf.fee_code ~~* '%-UNALT%' ) AND g.dog_gender = 'Female' AND NOT laf.is_exempt THEN 'New, Female Unaltered'
			WHEN laf.activity_type = 'NEW' AND ( laf.is_unaltered OR laf.fee_code ~~* '%-UNALT%' ) AND g.dog_gender = 'Male' AND NOT laf.is_exempt THEN 'New, Male Unaltered'
			WHEN laf.activity_type = 'NEW' AND g.dog_gender = 'Female' AND laf.is_exempt THEN 'New, Service Dog Female'
			WHEN laf.activity_type = 'NEW' AND g.dog_gender = 'Male' AND laf.is_exempt THEN 'New, Service Dog Male'
            WHEN laf.activity_type = 'RENEWAL' AND ( laf.is_altered OR laf.fee_code ~~* '%-ALT%' ) AND g.dog_gender = 'Female' AND NOT laf.is_exempt THEN 'Renewal, Female Spayed'
			WHEN laf.activity_type = 'RENEWAL' AND ( laf.is_altered OR laf.fee_code ~~* '%-ALT%' ) AND g.dog_gender = 'Male' AND NOT laf.is_exempt THEN 'Renewal, Male Neutered'
			WHEN laf.activity_type = 'RENEWAL' AND ( laf.is_unaltered OR laf.fee_code ~~* '%-UNALT%' ) AND g.dog_gender = 'Female' AND NOT laf.is_exempt THEN 'Renewal, Female Unaltered'
			WHEN laf.activity_type = 'RENEWAL' AND ( laf.is_unaltered OR laf.fee_code ~~* '%-UNALT%' ) AND g.dog_gender = 'Male' AND NOT laf.is_exempt THEN 'Renewal, Male Unaltered'
			WHEN laf.activity_type = 'RENEWAL' AND g.dog_gender = 'Female' AND laf.is_exempt THEN 'Renewal, Service Dog Female'
			WHEN laf.activity_type = 'RENEWAL' AND g.dog_gender = 'Male' AND laf.is_exempt THEN 'Renewal, Service Dog Male'
			WHEN laf.fee_code = 'CUSTOM-M-FEE' then 'Late Fees'
            ELSE 'Unknown'
        END AS title,
        sum(laf.amount) as total
    FROM fee_info laf
	INNER JOIN license.license_activity a 
		ON laf.license_activity_id = a.license_activity_id
	INNER JOIN gender_info g on a.license_id = g.license_id
	GROUP BY laf.license_activity_id, title
),
dog_tags AS (
    SELECT
        oi.order_item_id AS entity_id,
        'Replacement Tags' AS title,
        f.amount AS total
    FROM calculation.order_ o
    INNER JOIN calculation.order_item oi 
        ON o.order_id = oi.order_id
    INNER JOIN calculation.order_item_fee laf
        ON laf.order_item_id = oi.order_item_id
    INNER JOIN calculation.fee f
        ON f.fee_id = laf.fee_id
    WHERE o.status ILIKE 'ORDER_PAID'
        AND oi.name ILIKE 'Dog Tag%'
        AND oi.item_type_id ILIKE 'tag'
        AND DATE(timezone( 'America/New_York', o.order_paid_date + interval '3' hour )) BETWEEN :startDate::date AND :endDate::date
),
all_fees AS (
    SELECT title, total 
    FROM license_activity_fees
    UNION ALL
    SELECT title, total 
    FROM dog_tags
),
fees_by_amount AS (
    SELECT
        title,
        total AS amount,
        COUNT(*) AS quantity,
        total * COUNT(*) AS total_amount
    FROM all_fees
    GROUP BY title, total
)
SELECT 
    fba.title,
	COALESCE( pt.sort_order, 999 ) as sort_order,
    COALESCE(TO_CHAR(fba.amount, '$FM999,999,990.00'), '$0.00') AS amount,
    COALESCE(fba.quantity, 0) AS quantity, 
    COALESCE(TO_CHAR(fba.total_amount, '$FM999,999,990.00'), '$0.00') AS total,
    false AS is_total
FROM fees_by_amount fba
LEFT JOIN possible_titles pt ON fba.title = pt.title
WHERE fba.quantity IS NOT NULL

UNION ALL

SELECT 
    pt.title,
	pt.sort_order,
    TO_CHAR(pt.cost, '$FM999,999,990.00') AS amount,
    0 AS quantity, 
    '$0.00' AS total,
    false AS is_total
FROM possible_titles pt
WHERE NOT EXISTS (
    SELECT 1 FROM fees_by_amount fba WHERE pt.title = fba.title
)

UNION ALL

SELECT 
    'Total' AS title,
	0 as sort_order,
    '' AS amount,
    COALESCE(SUM(fba.quantity), 0) AS quantity,
    TO_CHAR(COALESCE(SUM(fba.total_amount), 0.00), '$FM999,999,990.00') AS total,
    true AS is_total
FROM fees_by_amount fba

ORDER BY is_total, sort_order, title, amount;

$$
where name = 'ClerkSummaryReportTableItems';


