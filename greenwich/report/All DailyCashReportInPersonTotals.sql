update config.sql_storage
set sql_query = $$
SELECT
    COALESCE(SUM(CASE WHEN p.payment_type = 'cash' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS cash,
    COALESCE(SUM(CASE WHEN p.payment_type in ( 'link', 'CREDITCARD', 'card' ) THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS card,
    COALESCE(SUM(CASE WHEN p.payment_type = 'ach' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS ach,
	COALESCE(SUM(CASE WHEN p.payment_type = 'personalCheck' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS "personalCheck",
    COALESCE(SUM(CASE WHEN p.payment_type = 'certifiedCheck' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS "certifiedCheck",
    COALESCE(SUM(CASE WHEN p.payment_type = 'moneyOrder' THEN fees.total_fees ELSE 0 END), 0.00)::NUMERIC(20,2)::TEXT AS "moneyOrder",
	COALESCE(SUM(fees.total_fees), 0.00)::NUMERIC(20,2)::TEXT AS total
FROM calculation.order_ o
INNER JOIN calculation.order_item oi 
    ON o.order_id = oi.order_id
    AND o.status = 'ORDER_PAID'
    AND DATE(timezone( 'America/New_York', o.order_paid_date + interval '3' hour )) >= DATE(:startDate)
    AND DATE(timezone( 'America/New_York', o.order_paid_date + interval '3' hour )) <= DATE(:endDate)
    
INNER JOIN payment.payment p 
    ON p.order_id = o.order_id
    AND (p.is_online_transaction IS NULL OR p.is_online_transaction = false)
LEFT JOIN LATERAL calculation.get_dog_license_fees_by_order_item_id(oi.order_item_id) AS fees
    ON true;
$$
where name = 'DailyCashReportInPersonTotals';