select o.* from order_ o
where order_number = 'O-154';

select o.* from order_ o
inner join order_item oi
on oi.order_id = o.order_id
where order_number = 'O-154';

select laf.* from license l
inner join license_activity la
on la.license_id = l.license_id
inner join license_activity_fee laf
on laf.license_activity_id = la.license_activity_id
where license_uuid in ('bcb776f1-f582-45a9-941c-332a771a09bb','72f4b57c-b6b8-4b0b-9fa0-a935ce3ddffd');


update license_activity_fee
set payment_status = 'UNPAID', paid_date = null, order_id = null
where license_activity_id in (
	138,
	139
);

update license
set license_status_id = 2, 
	properties = '{"endYear": 2026, "startYear": 2025, "licenseForm": null, "licenseLabel": "July 1, 2025 - June 30, 2026", "rejectedFields": [], "licenseDuration": 1}'::jsonb,
	valid_from_date = '2024-07-01 00:00:00+00', valid_to_date = '2025-06-30 23:59:59+00'
where license_uuid in ('bcb776f1-f582-45a9-941c-332a771a09bb');

update license
set license_status_id = 2, 
	properties = '{"endYear": 2026, "startYear": 2025, "licenseForm": null, "licenseLabel": "July 1, 2025 - June 30, 2026", "rejectedFields": [], "licenseDuration": 1}'::jsonb,
	valid_from_date = '2024-07-01 00:00:00+00', valid_to_date = '2025-06-30 23:59:59+00'
where license_uuid in ('72f4b57c-b6b8-4b0b-9fa0-a935ce3ddffd');

select * from audit_log_license
where license_uuid in ('72f4b57c-b6b8-4b0b-9fa0-a935ce3ddffd')
order by revision_id desc

