INSERT INTO config.json_storage (
  properties,
  created_by,
  created_date,
  last_modified_by,
  json_data,
  last_modified_date
)
VALUES (
  '{"payment": "paymentForm"}'::jsonb,
  'service-account-clerkxpress-seed', 
  CURRENT_TIMESTAMP, 
  'service-account-clerkxpress-seed',
  '[
    {
        "key": "cardOnline",
        "icon": "credit-card",
        "surcharge": [],
        "isDisabled": false,
        "displayName": "Card Online",
        "paymentMode": "online"
    },
    {
        "key": "cash",
        "icon": "cash",
        "surcharge": [],
        "isDisabled": false,
        "displayName": "Cash",
        "paymentMode": "offline"
    },
    {
        "key": "personalCheck",
        "icon": "personal-check",
        "surcharge": [],
        "isDisabled": false,
        "displayName": "Personal Check",
        "paymentMode": "offline"
    },
    {
        "key": "certifiedCheck",
        "icon": "certified-check",
        "surcharge": [],
        "isDisabled": false,
        "displayName": "Certified Check",
        "paymentMode": "offline"
    },
    {
        "key": "moneyOrder",
        "icon": "money-order",
        "surcharge": [],
        "isDisabled": false,
        "displayName": "Money Order",
        "paymentMode": "offline"
    }
]'::jsonb,  
  CURRENT_TIMESTAMP 
)
ON CONFLICT (properties)
DO UPDATE SET
  json_data = EXCLUDED.json_data,
  last_modified_by = EXCLUDED.last_modified_by,
  last_modified_date = CURRENT_TIMESTAMP;
