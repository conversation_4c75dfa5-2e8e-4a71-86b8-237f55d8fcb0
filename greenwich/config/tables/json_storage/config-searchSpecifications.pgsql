INSERT INTO config.json_storage (
  properties,
  created_by,
  created_date,
  last_modified_by,
  json_data,
  last_modified_date
)
VALUES (
  '{"config": "search_specifications"}'::jsonb,
  'service-account-clerkxpress-seed',
  CURRENT_TIMESTAMP,
  'service-account-clerkxpress-seed',
  '[
     {
       "joins": [
         {
           "type": "many_to_many",
           "join_type": "LEFT JOIN",
           "target_key": "address_id",
           "target_alias": "addr_indiv",
           "target_table": "license.address",
           "junction_alias": "aso_indiv",
           "junction_table": "license.association",
           "source_table_key": "participant_id",
           "junction_source_key": "parent_id",
           "junction_target_key": "child_id"
         },
         {
           "on": "participant.participant_type_group_id = license_ptg_indiv.participant_type_group_id",
           "type": "simple",
           "alias": "license_ptg_indiv",
           "table": "license.participant_type_group",
           "join_type": "LEFT JOIN"
         },
         {
           "on": "license_ptg_indiv.participant_group_id = license_pg_indiv.participant_group_id",
           "type": "simple",
           "alias": "license_pg_indiv",
           "table": "license.participant_group",
           "join_type": "LEFT JOIN"
         }
       ],
       "table": "license.participant",
       "id_field": "participant_id",
       "output_fields": {
         "status": {
           "id": 6,
           "column": "",
           "is_property": true
         },
         "entity_id": {
           "id": 4,
           "column": "participant_uuid",
           "is_property": false
         },
         "entity_type": {
           "id": 5,
           "column": "license_pg_indiv.name",
           "is_property": false
         },
         "third_display": [
           {
             "id": 3,
             "type": "email",
             "column": "email",
             "is_property": true
           },
           {
             "id": 6,
             "type": "phone",
             "column": "phone",
             "is_property": true
           }
         ],
         "primary_display": {
           "id": 1,
           "column": "firstName",
           "is_property": true
         },
         "secondary_display": {
           "id": 2,
           "column": "addr_indiv.full_address",
           "is_property": false
         }
       },
       "search_fields": [
         {
           "field": "firstName",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "lastName",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "email",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "phone",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "address",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "address2",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "city",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "state",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "zip",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         }
       ],
       "participant_type": "Individual"
     },
     {
       "joins": [
         {
           "type": "many_to_many",
           "join_type": "LEFT JOIN",
           "target_key": "address_id",
           "target_alias": "addr_dog",
           "target_table": "license.address",
           "junction_alias": "aso_dog",
           "junction_table": "license.association",
           "source_table_key": "participant_id",
           "junction_source_key": "parent_id",
           "junction_target_key": "child_id"
         },
         {
           "on": "participant.participant_type_group_id = license_ptg_dog.participant_type_group_id",
           "type": "simple",
           "alias": "license_ptg_dog",
           "table": "license.participant_type_group",
           "join_type": "LEFT JOIN"
         },
         {
           "on": "license_ptg_dog.participant_group_id = license_pg_dog.participant_group_id",
           "type": "simple",
           "alias": "license_pg_dog",
           "table": "license.participant_group",
           "join_type": "LEFT JOIN"
         }
       ],
       "table": "license.participant",
       "id_field": "participant_id",
       "output_fields": {
         "status": {
           "id": 6,
           "column": "dogSex",
           "is_property": true
         },
         "entity_id": {
           "id": 4,
           "column": "participant_uuid",
           "is_property": false
         },
         "entity_type": {
           "id": 5,
           "column": "license_pg_dog.name",
           "is_property": false
         },
         "third_display": [
           {
             "id": 3,
             "type": "Breed",
             "column": "dogBreed",
             "is_property": true
           },
           {
             "id": 6,
             "type": "Tag Number",
             "column": "tagNumber",
             "is_property": true
           }
         ],
         "primary_display": {
           "id": 1,
           "column": "dogName",
           "is_property": true
         },
         "secondary_display": {
           "id": 2,
           "column": "",
           "is_property": true
         }
       },
       "search_fields": [
         {
           "field": "dogName",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "dogBreed",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "tagNumber",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "dogPrimaryColor",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         },
         {
           "field": "name",
           "data_type": "text",
           "match_type": "contains",
           "is_property": false
         },
         {
           "field": "vaccineAdministeredDate",
           "data_type": "text",
           "match_type": "contains",
           "is_property": true
         }
       ],
       "participant_type": "Dog"
     },
     {
       "joins": [
         {
           "on": "license.license_status_id = license_status_tbl.license_status_id",
           "type": "simple",
           "alias": "license_status_tbl",
           "table": "license.license_status",
           "join_type": "LEFT JOIN"
         },
         {
           "on": "license.license_type_id = license_type_tbl.license_type_id",
           "type": "simple",
           "alias": "license_type_tbl",
           "table": "license.license_type",
           "join_type": "LEFT JOIN"
         }
       ],
       "table": "license.license",
       "id_field": "license_id",
       "output_fields": {
         "status": {
           "id": 6,
           "column": "license_status_tbl.name",
           "is_property": false
         },
         "entity_id": {
           "id": 4,
           "column": "license_uuid",
           "is_property": false
         },
         "entity_type": {
           "id": 5,
           "value": "License"
         },
         "third_display": [
           {
             "id": 3,
             "type": "",
             "column": "license_type_tbl.name",
             "is_property": false
           },
           {
             "id": 3,
             "type": "Valid upto",
             "column": "valid_to_date",
             "is_property": false
           }
         ],
         "primary_display": {
           "id": 1,
           "column": "license_number",
           "is_property": false
         },
         "secondary_display": {
           "id": 2,
           "column": "",
           "is_property": true
         }
       },
       "search_fields": [
         {
           "field": "license_number",
           "data_type": "text",
           "match_type": "contains",
           "is_property": false
         },
         {
           "field": "license_status_tbl.name",
           "data_type": "text",
           "match_type": "contains",
           "is_property": false
         },
         {
           "field": "license_type_tbl.name",
           "data_type": "text",
           "match_type": "contains",
           "is_property": false
         },
         {
           "field": "valid_from_date",
           "data_type": "date",
           "match_type": "range",
           "is_property": false
         },
         {
           "field": "valid_to_date",
           "data_type": "date",
           "match_type": "range",
           "is_property": false
         }
       ]
     }
   ]'::jsonb,
  CURRENT_TIMESTAMP
)
ON CONFLICT (properties)
DO UPDATE SET
  json_data = EXCLUDED.json_data,
  last_modified_by = EXCLUDED.last_modified_by,
  last_modified_date = CURRENT_TIMESTAMP;
