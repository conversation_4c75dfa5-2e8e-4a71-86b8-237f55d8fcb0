DROP FUNCTION IF EXISTS license.fn_determineDogLicenseDuration(i_license_entity_id uuid);
CREATE OR REPLACE FUNCTION license.fn_determineDogLicenseDuration(
    in i_license_entity_id uuid
)
RETURNS TABLE(
    duration INT,
    message TEXT,
    isRenewable BOOLEAN,
    reasonCodes TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_license record;
    v_license_status record;
    v_latest_license_activity record;
    v_is_renewal boolean := false;
    v_valid_to_date TIMESTAMP;
	v_valid_from_date TIMESTAMP;
    v_dog_properties jsonb;

    v_vaccine_expiration_date date;
    v_license_expiration_date date;
    v_is_vaccine_exempt boolean;
    v_max_renewal_years int := 1;
    v_current_date date := CURRENT_DATE;
    v_future_current_date date;
    v_max_vaccine_expiration_date date;
    v_whichever_is_less date;
    v_years_until_exp int := 0;
    v_duration int;
    v_message text;
    v_required_vaccine_valid_until date := (EXTRACT(YEAR FROM v_current_date) || '-06-01')::date;

    -- open registration for next year is june 1st to june 30th of the current year
    v_open_registration_start_date date := (EXTRACT(YEAR FROM v_current_date) || '-06-01')::date;
    v_open_registration_end_date date := (EXTRACT(YEAR FROM v_current_date) || '-07-30')::date;
    v_is_open_registration boolean := v_current_date BETWEEN v_open_registration_start_date AND v_open_registration_end_date;
    v_is_before_open_registration boolean := v_current_date < v_open_registration_start_date;
    v_is_after_open_registration boolean := v_current_date > v_open_registration_end_date;

    v_is_renewable boolean := true;
    v_reasons text[] := ARRAY[]::text[];
    v_start_year int := EXTRACT(YEAR FROM v_current_date);
    v_end_year int := EXTRACT(YEAR FROM v_current_date) + 1;
BEGIN
    -- Fetch license information
    SELECT l.*
    INTO v_license
    FROM license.view_license l
    WHERE l.license_type_code = 'dogLicense'
    AND l.license_uuid = i_license_entity_id;

    IF v_license IS NULL THEN
        RAISE EXCEPTION 'License with UUID % not found', i_license_entity_id;
    END IF;

    select ls.*
    into v_license_status
    from license.license_status ls
    where ls.license_status_id = v_license.license_status_id;

    select 
		la.*
	into v_latest_license_activity
	from license.license_activity la
	where la.license_id = v_license.license_id
	order by la.license_activity_id desc
	limit 1;

	v_is_renewal := (v_latest_license_activity.activity_type is not null);
    v_valid_to_date := COALESCE(v_latest_license_activity.valid_to_date, v_license.valid_to_date);
    v_valid_from_date := COALESCE(v_latest_license_activity.valid_from_date, v_license.valid_from_date);

    -- if the today's date is between the open registration start date and end date, set the max renewal years to 2
    IF v_is_open_registration THEN
        v_max_renewal_years := 1;
    END IF;
    RAISE NOTICE 'v_max_renewal_years: %', v_max_renewal_years;

    IF v_is_before_open_registration OR v_is_open_registration THEN
        v_start_year := v_start_year - 1;
        v_end_year := v_end_year - 1;
    END IF;

    -- Fetch dog properties
    SELECT pdog.properties
    INTO v_dog_properties
    FROM license.association aDog
    LEFT JOIN license.view_participant pdog
        ON pdog.participant_id = aDog.child_id
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
    AND pdog.group_name ILIKE 'Dog'
    order by pdog.properties->>'vaccineDueDate' ASC
    LIMIT 1;


    IF v_dog_properties IS NULL THEN
        RAISE EXCEPTION 'Dog not found for license with UUID %', i_license_entity_id;
    END IF;

    -- Fetch vaccine information
    v_vaccine_expiration_date := (v_dog_properties->>'vaccineDueDate')::date;
    v_is_vaccine_exempt := COALESCE(v_dog_properties->>'vaccineDatesExempt','false') = 'true';
    v_license_expiration_date := COALESCE(v_license.valid_to_date, v_current_date);

    -- Calculate future current date (3 years from the current month end)
    v_future_current_date := make_date(
        EXTRACT(YEAR FROM license.addYears(license.end_of_month(v_current_date), v_max_renewal_years))::int,
        6, -- June
        30
    );


    RAISE NOTICE 'v_max_renewal_years: %', v_max_renewal_years;
    RAISE NOTICE 'v_future_current_date: %', v_future_current_date;
    RAISE NOTICE 'v_license_expiration_date: %', v_license_expiration_date;
    RAISE NOTICE 'v_vaccine_expiration_date: %', v_vaccine_expiration_date;
	RAISE NOTICE 'v_is_renewal: %', v_is_renewal;
    RAISE NOTICE 'v_is_before_open_registration: %', v_is_before_open_registration;
    RAISE NOTICE 'v_is_open_registration: %', v_is_open_registration;
    RAISE NOTICE 'v_is_after_open_registration: %', v_is_after_open_registration;
    RAISE NOTICE 'v_start_year: %', v_start_year;
    RAISE NOTICE 'v_end_year: %', v_end_year;
    RAISE NOTICE 'v_valid_to_date: %', v_valid_to_date;
    RAISE NOTICE 'v_valid_from_date: %', v_valid_from_date;
    RAISE NOTICE 'v_current_date: %', v_current_date;
    RAISE NOTICE 'v_open_registration_start_date: %', v_open_registration_start_date;
    RAISE NOTICE 'v_open_registration_end_date: %', v_open_registration_end_date;

    IF v_is_vaccine_exempt THEN
        v_duration := 1;
        v_message := 'Exempt from vaccine, eligible for renewal';
        v_is_renewable := true;
        v_reasons := array_append(v_reasons, 'vaccineExempt');
    
    ELSIF v_is_open_registration THEN
        v_duration := v_max_renewal_years;
        v_message := 'Open registration period: eligible for up to ' || v_max_renewal_years || '-year renewal';
        v_is_renewable := true;
        v_reasons := array_append(v_reasons, 'openRegistration');

    ELSE
        v_duration := 1;
        v_message := 'Vaccine valid, eligible for renewal';
        v_is_renewable := true;
        v_reasons := array_append(v_reasons, 'vaccineValid');

    END IF;

    -- check if the v_license_expiration_date is greater than v_open_registration_end_date
    -- if the license expiration date is after the open registration end date, then they have already renewed
    IF v_license_expiration_date > v_open_registration_end_date THEN
        v_duration := 0;
        v_message := 'License already renewed, not eligible for renewal';
        v_is_renewable := false;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF EXTRACT(YEAR FROM v_license_expiration_date) = EXTRACT(YEAR FROM v_current_date) AND v_current_date < v_open_registration_start_date THEN
        v_duration := 0;
        v_message := 'License already renewed for this year, not eligible for renewal';
        v_is_renewable := false;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF COALESCE(v_valid_to_date, v_current_date) > (v_end_year + 1 || '-06-30')::date THEN
        v_duration := 0;
        v_message := 'License already renewed for this period, not eligible for renewal';
        v_is_renewable := false;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF v_vaccine_expiration_date < v_current_date AND NOT v_is_vaccine_exempt THEN
        v_duration := 0;
        v_message := 'Vaccine expired, not eligible for renewal';
        v_is_renewable := true;
        v_reasons := array_append(v_reasons, 'vaccineExpired');

    ELSIF v_vaccine_expiration_date < v_required_vaccine_valid_until AND NOT v_is_vaccine_exempt THEN
        v_duration := 0;
        v_message := 'Vaccine must be valid through ' || to_char(v_required_vaccine_valid_until, 'FMMonth FMDD') || ' to be eligible for renewal';
        v_is_renewable := true;
        v_reasons := array_append(v_reasons, 'vaccineExpired');
    ELSIF v_vaccine_expiration_date IS NULL AND NOT v_is_vaccine_exempt THEN
        v_duration := 0;
        v_message := 'No vaccine info and not exempt';
        v_is_renewable := true;
        v_reasons := array_append(v_reasons, 'vaccineExpired');
    END IF;

    IF v_license_status.code IN ('PENDING_PAYMENT') THEN
        v_is_renewable := false;
        v_message := 'License is pending payment. Please complete payment to renew or undo the previous renewal.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF v_license_status.code IN ('PENDING_APPROVAL') THEN
        v_is_renewable := false;
        v_message := 'License is pending approval. Please wait for approval before renewing.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF v_license_status.code IN ('REJECTED', 'APPROVED', 'SUBMITTED') THEN
        v_is_renewable := false;
        v_message := 'License is already approved or rejected. Cannot renew.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    ELSIF v_license_status.code IN ('CANCELED','CLOSED') THEN
        v_is_renewable := false;
        v_message := 'License is canceled or closed. Cannot renew.';
        v_duration := 0;
        v_reasons := array_append(v_reasons, 'alreadyRenewed');
    END IF;

    RETURN QUERY SELECT v_duration, v_message, v_is_renewable, to_json(v_reasons)::text;

END;
$$;

--select * from license.fn_determineDogLicenseDuration('b1b47047-9ec6-4545-8099-0a6e65a65521');

update license.license_type lt 
set duration_function = 'fn_determineDogLicenseDuration'
where lt.code = 'dogLicense';