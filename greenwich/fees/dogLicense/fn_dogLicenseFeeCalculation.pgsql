create or replace function license.fn_dogLicenseFeeCalculation(
	in i_license_entity_id uuid
)
returns table (
	fee_type text,
	fee_code text,
	fee_amount NUMERIC(20, 10)
)
LANGUAGE plpgsql
AS $$
BEGIN
	RETURN QUERY
	SELECT
	*
	FROM license.fn_dogLicenseFeeCalculation(i_license_entity_id, false, true) f;
END;
$$;


--drop function if exists fn_dogLicenseFeeCalculation(uuid);
create or replace function license.fn_dogLicenseFeeCalculation(
	in i_license_entity_id uuid,
	in i_is_renewal boolean,
	in i_is_late boolean
)
returns table (
	fee_type text,
	fee_code text,
	fee_amount NUMERIC(20, 10)
)
LANGUAGE plpgsql
AS $$
declare
	v_license record;
	v_latest_license_activity record;
	v_dog_properties jsonb;
	v_owner_properties jsonb;
	v_duration_months int := (18 - EXTRACT(MONTH FROM now()));
	v_late_fee_rate numeric := 1.00;
	v_late_capped_amount numeric := 25.00;

	v_is_spayed_or_neutered boolean := false;
	v_is_senior boolean := false;
	v_is_exempt boolean := false;

	v_is_renewal boolean := false;
	v_late_month_count int := 0;
	v_late_days_count int := 0;

	v_current_date date := CURRENT_DATE;
	v_open_registration_start_date date := (EXTRACT(YEAR FROM v_current_date) || '-06-01')::date;
    v_open_registration_end_date date := (EXTRACT(YEAR FROM v_current_date) || '-07-30')::date;
    v_is_open_registration boolean := v_current_date BETWEEN v_open_registration_start_date AND v_open_registration_end_date;
    v_is_before_open_registration boolean := v_current_date < v_open_registration_start_date;
    v_is_after_open_registration boolean := v_current_date > v_open_registration_end_date;
BEGIN
    select 
    	l.*
	into v_license
	from license.view_license l
	where l.license_type_code = 'dogLicense'
	and l.license_uuid = i_license_entity_id;

	if v_license is null then
        raise exception 'License with UUID % not found', i_license_entity_id;
    end if;

	select 
		la.*
	into v_latest_license_activity
	from license.license_activity la
	where la.license_id = v_license.license_id
	order by la.license_activity_id desc
	limit 1;

	if i_is_renewal then
		v_is_renewal := TRUE;
	else
		-- if there is an activity then it is a renewal
		-- if no activity yet then it'll be new license
		v_is_renewal := v_latest_license_activity.activity_type is not null;
	end if;

	SELECT
		pdog.properties
	INTO v_dog_properties	
	FROM license.association aDog
	LEFT JOIN license.view_participant pdog
		on pdog.participant_id = aDog.child_id
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
	and pdog.group_name ILIKE 'Dog';

	if v_dog_properties is null then
		raise exception 'Dog not found for license with UUID %', i_license_entity_id;
	end if;

	select 
		powner.properties
	into v_owner_properties
	from license.association aOwner
	LEFT JOIN license.view_participant powner
		on powner.participant_id = aOwner.child_id
	where aOwner.parent_association_type = 'LICENSE'
	and aOwner.child_association_type = 'PARTICIPANT'
	and aOwner.parent_id = v_license.license_id
	and powner.group_name ILIKE 'Individual';

	if v_owner_properties is null then
		raise exception 'Owner not found for license with UUID %', i_license_entity_id;
	end if;

	if COALESCE(v_dog_properties->>'dogSpayedOrNeutered','no') ILIKE 'yes' then
		v_is_spayed_or_neutered := true;
	end if;

	if (v_owner_properties->>'dateOfBirth')::date <= (now() - interval '65 years')::date then
		v_is_senior := true;
	end if;

	if COALESCE(v_dog_properties->>'licenseExempt','false') ILIKE 'true' then
		v_is_exempt := true;
	end if;

		-- late fees is only applicable for renewals
	-- compare today's date with the license_activity valid_to_date and compare how many months late the renewal is
	if v_is_renewal and i_is_late and v_is_after_open_registration and COALESCE(license.start_of_day(v_latest_license_activity.valid_to_date), v_current_date) <= v_current_date then
		v_late_month_count := license.countBetweenDates('months', v_current_date::TIMESTAMP, license.start_of_day(v_latest_license_activity.valid_to_date));
		-- if the renewal is atlease more than one day late, then add the late fee
		v_late_days_count := EXTRACT(DAY FROM AGE(v_current_date::TIMESTAMP, license.start_of_day(v_latest_license_activity.valid_to_date)));
		if v_late_days_count > 0 then
			v_late_month_count := v_late_month_count + 1;
		end if;
		if v_late_month_count > 0 then
			RETURN QUERY
			select 
				'manual' as fee_type,
				'DL-M-LATE' as fee_code,
				v_late_month_count * v_late_fee_rate as fee_amount;
		end if;
	end if;

	-- if the dog is spayed or neutered, add the DL-S-ALT fee
	-- if the dog is not spayed or neutered, add the DL-M-UNALT and DL-S-UNALT fees
	if v_is_spayed_or_neutered then
		RETURN QUERY
	    select 
	    	'calculated' as fee_type,
	    	'DL-S-ALT' as fee_code,
			0.0 as fee_amount
		union all
	    select 
	    	'calculated' as fee_type,
	    	'DL-M-ALT' as fee_code,
			0.0 as fee_amount;
	else
		RETURN QUERY
	    select 
	    	'calculated' as fee_type,
	    	'DL-M-UNALT' as fee_code,
			0.0 as fee_amount
		union all
	    select
	    	'calculated' as fee_type,
	    	'DL-S-UNALT' as fee_code,
			0.0 as fee_amount;
	end if;

	-- if license is exempt and not spayed or neutered, add the DL-M-EXEMPTUNALT and DL-M-EXEMPTSTATEUNALT fee
	if v_is_exempt and not v_is_spayed_or_neutered then
		RETURN QUERY
	    select 
	    	'calculated' as fee_type,
	    	'DL-M-EXEMPTUNALT' as fee_code,
			0.0 as fee_amount
		union all
	    select 
	    	'calculated' as fee_type,
	    	'DL-S-EXEMPTUNALT' as fee_code,
			0.0 as fee_amount;
	end if;

	-- if license is exempt and spayed or neutered, add the DL-M-EXEMPTSTATEALT fee
	if v_is_exempt and v_is_spayed_or_neutered then
		RETURN QUERY
	    select 
	    	'calculated' as fee_type,
	    	'DL-S-EXEMPTALT' as fee_code,
			0.0 as fee_amount
		union all
	    select 
	    	'calculated' as fee_type,
	    	'DL-M-EXEMPTALT' as fee_code,
			0.0 as fee_amount;
	end if;
END;
$$;

--select * from fn_dogLicenseFeeCalculation('f4dd9369-656d-4ea5-893b-7967b0013ae9');

update license_type lt 
set fee_config_function = 'fn_dogLicenseFeeCalculation'
where lt.code = 'dogLicense';