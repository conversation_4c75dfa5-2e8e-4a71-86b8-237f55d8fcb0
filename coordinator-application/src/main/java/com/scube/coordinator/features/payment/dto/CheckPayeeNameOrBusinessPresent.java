package com.scube.coordinator.features.payment.dto;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.util.ObjectUtils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CheckPayeeNameOrBusinessPresent.CheckPayeeNameOrBusinessPresentValidator.class)
public @interface CheckPayeeNameOrBusinessPresent {
    String message() default "One of First/Last name or business name is required";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class CheckPayeeNameOrBusinessPresentValidator implements ConstraintValidator<CheckPayeeNameOrBusinessPresent, CoordinatorPayeeDto> {

        @Override
        public void initialize(CheckPayeeNameOrBusinessPresent constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(CoordinatorPayeeDto elementDto, ConstraintValidatorContext constraintValidatorContext) {
            constraintValidatorContext.disableDefaultConstraintViolation();
            if (ObjectUtils.isEmpty(elementDto)) return false;
            if (ObjectUtils.isEmpty(elementDto.getBusinessName())) {
                var isFirstNameEmpty = ObjectUtils.isEmpty(elementDto.getFirstName());
                var isLastNameEmpty = ObjectUtils.isEmpty(elementDto.getLastName());

                if (!isFirstNameEmpty && !isLastNameEmpty) return true;

                if (isFirstNameEmpty) {
                    constraintValidatorContext.buildConstraintViolationWithTemplate("First name is required")
                            .addPropertyNode("firstName")
                            .addConstraintViolation();
                }

                if (isLastNameEmpty) {
                    constraintValidatorContext.buildConstraintViolationWithTemplate("Last name is required")
                            .addPropertyNode("lastName")
                            .addConstraintViolation();
                }
                return false;
            }
            return true;
        }
    }
}