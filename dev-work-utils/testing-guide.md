## 🐚 Shellfish Permit Setup (Full API Workflow)

This document provides a complete step-by-step workflow for configuring and linking **Individuals**, **Shellfish**, and **Shellfish Permits** using REST API calls.  
Each step includes endpoints, request bodies, and sample responses.

---

## ⚙️ Step 1: Create Record Type - INDIVIDUAL (if not exists)

**Endpoint:**  
`POST /api/config/record-types`

**Request Body:**
```json
{
  "typeCode": "INDIVIDUAL",
  "typeName": "Individual",
  "parentTypeCode": null,
  "description": "Individual person record type",
  "properties": {},
  "configJson": {},
  "createdBy": "admin"
}
````

**Response:**

```json
{
  "recordTypeId": 1,
  "recordTypeUuid": "111e1111-e11b-11d1-a111-111111111111",
  "typeCode": "INDIVIDUAL",
  "typeName": "Individual",
  "parentUuid": null,
  "parentTypeCode": null,
  "parentTypeName": null,
  "description": "Individual person record type",
  "properties": {},
  "configJson": {},
  "createdAt": "2025-10-06T10:00:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:00:00Z",
  "lastModifiedBy": "admin"
}
```

---

## ⚙️ Step 2: Create Record Type - SHELLFISH

**Endpoint:**
`POST /api/config/record-types`

**Request Body:**

```json
{
  "typeCode": "SHELLFISH",
  "typeName": "Shellfish",
  "parentTypeCode": null,
  "description": "Shellfish record type",
  "properties": {},
  "configJson": {},
  "createdBy": "admin"
}
```

**Response:**

```json
{
  "recordTypeId": 2,
  "recordTypeUuid": "222e2222-e22b-22d2-a222-************",
  "typeCode": "SHELLFISH",
  "typeName": "Shellfish",
  "parentUuid": null,
  "parentTypeCode": null,
  "parentTypeName": null,
  "description": "Shellfish record type",
  "properties": {},
  "configJson": {},
  "createdAt": "2025-10-06T10:01:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:01:00Z",
  "lastModifiedBy": "admin"
}
```

---

## ⚙️ Step 3: Create Record Type - SHELLFISH_PERMIT

**Endpoint:**
`POST /api/config/record-types`

**Request Body:**

```json
{
  "typeCode": "SHELLFISH_PERMIT",
  "typeName": "Shellfish Permit",
  "parentTypeCode": null,
  "description": "Permit for shellfish harvesting",
  "properties": {},
  "configJson": {},
  "createdBy": "admin"
}
```

**Response:**

```json
{
  "recordTypeId": 3,
  "recordTypeUuid": "333e3333-e33b-33d3-a333-************",
  "typeCode": "SHELLFISH_PERMIT",
  "typeName": "Shellfish Permit",
  "parentUuid": null,
  "parentTypeCode": null,
  "parentTypeName": null,
  "description": "Permit for shellfish harvesting",
  "properties": {},
  "configJson": {},
  "createdAt": "2025-10-06T10:02:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:02:00Z",
  "lastModifiedBy": "admin"
}
```

---

## 🔗 Step 4: Create Association Type - INDIVIDUAL_SHELLFISH

**Endpoint:**
`POST /api/config/association-types`

**Request Body:**

```json
{
  "typeCode": "INDIVIDUAL_SHELLFISH",
  "associationName": "Individual owns Shellfish",
  "description": "Association between individual and their shellfish",
  "parentRecordTypes": ["INDIVIDUAL"],
  "childRecordTypes": ["SHELLFISH"],
  "additionalProperties": {},
  "createdBy": "admin"
}
```

**Response:**

```json
{
  "associationTypeId": 1,
  "associationTypeUuid": "aaa1aaa1-a11a-11a1-a111-aaaaaaaaa111",
  "associationName": "Individual owns Shellfish",
  "typeCode": "INDIVIDUAL_SHELLFISH",
  "description": "Association between individual and their shellfish",
  "parentRecordTypes": ["INDIVIDUAL"],
  "childRecordTypes": ["SHELLFISH"],
  "properties": {
    "parent_record_types": ["INDIVIDUAL"],
    "child_record_types": ["SHELLFISH"]
  },
  "createdAt": "2025-10-06T10:03:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:03:00Z",
  "lastModifiedBy": "admin"
}
```

---

## 🔗 Step 5: Create Association Type - SHELLFISH_SHELLFISH_PERMIT

**Endpoint:**
`POST /api/config/association-types`

**Request Body:**

```json
{
  "typeCode": "SHELLFISH_SHELLFISH_PERMIT",
  "associationName": "Shellfish has Permit",
  "description": "Association between shellfish and its permit",
  "parentRecordTypes": ["SHELLFISH"],
  "childRecordTypes": ["SHELLFISH_PERMIT"],
  "additionalProperties": {},
  "createdBy": "admin"
}
```

**Response:**

```json
{
  "associationTypeId": 2,
  "associationTypeUuid": "aaa2aaa2-a22a-22a2-a222-aaaaaaaaa222",
  "associationName": "Shellfish has Permit",
  "typeCode": "SHELLFISH_SHELLFISH_PERMIT",
  "description": "Association between shellfish and its permit",
  "parentRecordTypes": ["SHELLFISH"],
  "childRecordTypes": ["SHELLFISH_PERMIT"],
  "properties": {
    "parent_record_types": ["SHELLFISH"],
    "child_record_types": ["SHELLFISH_PERMIT"]
  },
  "createdAt": "2025-10-06T10:04:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:04:00Z",
  "lastModifiedBy": "admin"
}
```

---

## 🔗 Step 6: Create Association Type - INDIVIDUAL_SHELLFISH_PERMIT

**Endpoint:**
`POST /api/config/association-types`

**Request Body:**

```json
{
  "typeCode": "INDIVIDUAL_SHELLFISH_PERMIT",
  "associationName": "Individual has Shellfish Permit",
  "description": "Association between individual and their shellfish permit",
  "parentRecordTypes": ["INDIVIDUAL"],
  "childRecordTypes": ["SHELLFISH_PERMIT"],
  "additionalProperties": {},
  "createdBy": "admin"
}
```

**Response:**

```json
{
  "associationTypeId": 3,
  "associationTypeUuid": "aaa3aaa3-a33a-33a3-a333-aaaaaaaaa333",
  "associationName": "Individual has Shellfish Permit",
  "typeCode": "INDIVIDUAL_SHELLFISH_PERMIT",
  "description": "Association between individual and their shellfish permit",
  "parentRecordTypes": ["INDIVIDUAL"],
  "childRecordTypes": ["SHELLFISH_PERMIT"],
  "properties": {
    "parent_record_types": ["INDIVIDUAL"],
    "child_record_types": ["SHELLFISH_PERMIT"]
  },
  "createdAt": "2025-10-06T10:05:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:05:00Z",
  "lastModifiedBy": "admin"
}
```

---

## 👤 Step 7: Create an Individual Record

**Endpoint:**
`POST /api/records`

**Request Body:**

```json
{
  "recordTypeCode": "INDIVIDUAL",
  "recordName": "John Doe",
  "status": "ACTIVE",
  "properties": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "555-0123"
  },
  "createdBy": "admin",
  "associations": []
}
```

**Response:**

```json
{
  "recordId": 1,
  "recordUuid": "123e4567-e89b-12d3-a456-************",
  "recordName": "John Doe",
  "status": "ACTIVE",
  "properties": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "555-0123"
  },
  "recordTypeCode": "INDIVIDUAL",
  "recordTypeName": "Individual",
  "createdAt": "2025-10-06T10:06:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:06:00Z",
  "lastModifiedBy": "admin"
}
```

💾 **Save this recordUuid:**
`123e4567-e89b-12d3-a456-************`

---

## 🐚 Step 8: Create a Shellfish Record for the Individual

**Endpoint:**
`POST /api/records`

**Request Body:**

```json
{
  "recordTypeCode": "SHELLFISH",
  "recordName": "Oyster Bed #42",
  "status": "ACTIVE",
  "properties": {
    "species": "Oyster",
    "location": "East Bay Area",
    "quantity": 1000,
    "harvestDate": "2025-01-15"
  },
  "createdBy": "admin",
  "associations": [
    {
      "associationTypeCode": "INDIVIDUAL_SHELLFISH",
      "associatedRecordUuid": "123e4567-e89b-12d3-a456-************",
      "role": "CHILD"
    }
  ]
}
```

**Response:**

```json
{
  "recordId": 2,
  "recordUuid": "234e5678-e89b-12d3-a456-************",
  "recordName": "Oyster Bed #42",
  "status": "ACTIVE",
  "properties": {
    "species": "Oyster",
    "location": "East Bay Area",
    "quantity": 1000,
    "harvestDate": "2025-01-15"
  },
  "recordTypeCode": "SHELLFISH",
  "recordTypeName": "Shellfish",
  "createdAt": "2025-10-06T10:07:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:07:00Z",
  "lastModifiedBy": "admin"
}
```

💾 **Save this recordUuid:**
`234e5678-e89b-12d3-a456-************`

---

## 🪪 Step 9: Create a Shellfish Permit (linking Individual + Shellfish)

**Endpoint:**
`POST /api/records`

**Request Body:**

```json
{
  "recordTypeCode": "SHELLFISH_PERMIT",
  "recordName": "Shellfish Harvest Permit 2025-001",
  "status": "ACTIVE",
  "properties": {
    "permitNumber": "SH-2025-001",
    "issueDate": "2025-01-01",
    "expiryDate": "2025-12-31",
    "harvestLimit": 1000,
    "fee": 150.00
  },
  "createdBy": "admin",
  "associations": [
    {
      "associationTypeCode": "SHELLFISH_SHELLFISH_PERMIT",
      "associatedRecordUuid": "234e5678-e89b-12d3-a456-************",
      "role": "CHILD"
    },
    {
      "associationTypeCode": "INDIVIDUAL_SHELLFISH_PERMIT",
      "associatedRecordUuid": "123e4567-e89b-12d3-a456-************",
      "role": "CHILD"
    }
  ]
}
```

**Response:**

```json
{
  "recordId": 3,
  "recordUuid": "345f6789-e89b-12d3-a456-************",
  "recordName": "Shellfish Harvest Permit 2025-001",
  "status": "ACTIVE",
  "properties": {
    "permitNumber": "SH-2025-001",
    "issueDate": "2025-01-01",
    "expiryDate": "2025-12-31",
    "harvestLimit": 1000,
    "fee": 150.00
  },
  "recordTypeCode": "SHELLFISH_PERMIT",
  "recordTypeName": "Shellfish Permit",
  "createdAt": "2025-10-06T10:08:00Z",
  "createdBy": "admin",
  "lastModifiedAt": "2025-10-06T10:08:00Z",
  "lastModifiedBy": "admin"
}
```

---

## ✅ Verification Endpoints

### 1️⃣ Verify Individual Record

**Endpoint:**
`GET /api/records/123e4567-e89b-12d3-a456-************`
**Expected:** Same as Step 7 response

### 2️⃣ Verify Shellfish Record

**Endpoint:**
`GET /api/records/234e5678-e89b-12d3-a456-************`
**Expected:** Same as Step 8 response

### 3️⃣ Verify Permit Record

**Endpoint:**
`GET /api/records/345f6789-e89b-12d3-a456-************`
**Expected:** Same as Step 9 response

### 4️⃣ Get All Records by Type

**Endpoint:**
`GET /api/records/type/SHELLFISH`

**Expected Response:**

```json
[
  {
    "recordId": 2,
    "recordUuid": "234e5678-e89b-12d3-a456-************",
    "recordName": "Oyster Bed #42",
    "status": "ACTIVE",
    "properties": {
      "species": "Oyster",
      "location": "East Bay Area",
      "quantity": 1000,
      "harvestDate": "2025-01-15"
    },
    "recordTypeCode": "SHELLFISH",
    "recordTypeName": "Shellfish"
  }
]
```

---

## 🧭 Summary

All request and response structures follow the updated conventions:

* **Record Types** → use `parentTypeCode` instead of `parentUuid`
* **Association Types** → use `parentRecordTypes` and `childRecordTypes` arrays
* **Records** → use `associations` array with:

  * `associationTypeCode`
  * `associatedRecordUuid`
  * optional `role`
