Based on the code excerpts, here are the dependencies and usages of License Service in the Calculation Service:

## 1. **CalculationServiceWrapper (Report Application)**

### Order Queries by License Type:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/CalculationServiceWrapper.java mode=EXCERPT
public List<OrderInvoiceResponse> getRegularDogOrders(Map<String, Object> params) {
    var responses = calculationService.order().findOrdersByFiltersAsync(
            new HashMap<>(Map.of(
                    "status", "ORDER_PAID",
                    "cartItemId", "license",
                    "cartItemName", "Dog License"))
    ).collectList().block();
    
    // Filter items that start with "Dog License"
    responses.forEach(response -> {
        List<OrderInvoiceItem> filteredItems = response.getItems().stream()
                .filter(item -> item.getPrimaryDisplay().startsWith("Dog License"))
                .collect(Collectors.toList());
        response.setItems(filteredItems);
    });
}
````

### Purebred License Orders:
````java path=report-application/src/main/java/com/scube/report/features/licensing/dog/service/CalculationServiceWrapper.java mode=EXCERPT
public List<OrderInvoiceResponse> getPurebredDogOrders(Map<String, Object> params) {
    List<OrderInvoiceResponse> responses = calculationService.order().findOrdersByFiltersAsync(
            new HashMap<>(Map.of(
                    "cartItemId", "license",
                    "cartItemName", "Purebred"))
    ).collectList().block();
}
````

## 2. **Database Function Dependencies**

### License Fee Calculation by Order Item:
````sql path=chazy/calculation/functions/get_dog_license_fees_by_order_item_id.pgsql mode=EXCERPT
create or replace function calculation.get_dog_license_fees_by_order_item_id(
    IN v_order_item_id bigint
)
RETURNS TABLE(
    state_fees numeric, 
    local_fees numeric, 
    total_fees numeric, 
    is_exempt boolean, 
    has_senior_discount boolean, 
    is_altered boolean
)
````

## 3. **License Service Fee Integration**

### Fee Calculation Service:
````java path=license-application/src/main/java/com/scube/licensing/infrastructure/httpclient/CalculationService.java mode=EXCERPT
@Cacheable(value = "getFeeByKey", key = "#key", unless = "#result == null")
public Fee getFeeByKey(String key) {
    var queryResponse = amqpGateway.queryResult(new GetFeesQuery(List.of(key)))
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to get fee by key: " + key));
    return Optional.ofNullable(queryResponse.fees())
            .filter(f -> !ObjectUtils.isEmpty(f))
            .map(List::getFirst)
            .orElse(null);
}
````

### License Fee Service Integration:
````java path=license-application/src/main/java/com/scube/licensing/features/license/fee/sql_fees/LicenseFeeService.java mode=EXCERPT
private @NotNull List<Fee> getFeeQuery(List<String> feeCodes) {
    return feeCodes.stream()
            .map(calculationService::getFeeByKey)
            .filter(Objects::nonNull)
            .toList();
}

public void refreshCartItem(UUID itemId) {
    amqpGateway.publish(new CalculationObserverEvent("refresh", new CalculationRefreshObserverPayload(List.of(itemId), null)));
}
````

## 4. **Cart Item Dependencies**

### License as Cart Item:
The calculation service treats licenses as cart items with:
- `cartItemId: "license"`
- `cartItemName: "Dog License"` / `"Purebred"`
- Primary display names starting with license types

## 5. **Observer Pattern Integration**

### Calculation Observer Events:
````java path=license-application/src/main/java/com/scube/licensing/features/license/LicenseService.java mode=EXCERPT
amqpGateway.publish(new CalculationObserverEvent("remove", List.of(license.getUuid())));
````

## **Key Integration Points:**

1. **Order Management**: Calculation service manages license orders by filtering on `cartItemId: "license"`
2. **Fee Calculation**: License service queries calculation service for fee data
3. **Cart Integration**: Licenses are treated as cart items in the calculation system
4. **Event Publishing**: License changes trigger calculation refresh events
5. **Report Generation**: Calculation service provides license-specific order reports

## **Impact of Service_Record Migration:**

When migrating to Service_Record, you'll need to:
- Update cart item filtering from `"license"` to generic record types
- Modify order queries to handle multiple record types (license/permit/etc.)
- Update fee calculation functions to work with generic record structures
- Change observer events to use generic record identifiers

My search failed to locate more specific calculation service internal code that might have additional license dependencies. You may want to @calculation-application files to get a complete picture.
